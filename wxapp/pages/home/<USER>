var api = require('../../config/api.js')
var util = require('../../utils/util')
var Config = require('../../config/index.js')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    ImgUrl: api.ImgUrl,
    isBack: false,
    statusBarHeight: null,
    logoHeight: null,
    imgObject: {
      search: api.ImgUrl + 'images/<EMAIL>', //搜索图标
      menu1: api.ImgUrl + 'images/bg_home_expert.png', //专家名医
      menu2: api.ImgUrl + 'images/img_video_consultation.png', //复诊开药
      bgImg: api.ImgUrl + 'images/bg_home.png', //底部背景图
      logo: api.ImgUrl + 'logo/ic_text.png', //logo
      img_blank_doctor: api.ImgUrl + 'images/img_blank_doctor.png',
      ic_font_message: api.ImgUrl + 'images/ic_font_message.png'
    },
    noData: false,
    imgUrls: [
      api.ImgUrl + 'images/img_home_banner.png',
      api.ImgUrl + 'images/img_home_banner.png'
    ],
    indicatorDots: false,
    autoplay: true,
    interval: 3000,
    duration: 500, //滑动动画时长
    circular: true,
    indicatorColor: 'rgba(255,255,255,1)', //普通轮播点背景色
    indicatorActiveColor: '#2e9cff', //选中轮播点背景色
    swiperCurrent: 0,
    list: [],
    listQuery: {
      page: 1 // 页码
    },
    templateId: [],
    clickFlag: true,
    doctorName: '',
    doctorId: null,
    type: null,
    isLogin: false,
    tapTime: '',
    company: Config.company,
    navTabList: [{ title: '医学资讯' }, { title: '我的医生' }, { title: '诊前咨询' }],
    navTabListT: [{ title: '医学资讯' }, { title: '我的医生' }],
    currentNav: 0,
    container: null,
    isFixed: false,
    // 资讯
    artCurrent: 0,
    artList: [],
    counselorList: [],
    artListQuery: {
      page: 1, // 页码
      type: 2,
      groupId: 1
    },
    counselorQuery: {
      page: 1 // 页码
    },
    artHasNext: false,
    docHasNext: false,
    couHasNext: false,
    departments: [], //
    noticeList: [],//院务公开最新一条
    bannerList: [],
    commonDiseaseList: [], //常见疾病列表
    groupsData: [] // 新增用于存储分组数据
  },

  // 常见疾病跳转
  handleDiseaseNavigator(e) {
    const { diseaseid, diseasename } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/famousDoctor/famousDoctor?diseaseId=${diseaseid}&diseaseName=${diseasename}`
    })
  },

  // 轮播图事件
  swiperChange(e) {
    const current = e.detail.current
    this.setData({
      swiperCurrent: current
    })
  },
  goSearchPage() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // LOGO位置设置
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight,
      logoHeight: app.globalData.navBarHeight - app.globalData.statusBarHeight
    })
    this.getDdepartments()
    this.fetchDiseaseList()
  },
  // 咨询记录
  counselHistory(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/consult/record/index?doctorId=' + id
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    this.setData({
      artHasNext: false,
      docHasNext: false,
      ['listQuery.page']: 1,
      ['artListQuery.page']: 1
    })
    const applicationConfig = await util.getHaiNanConfig()
    wx.setStorageSync('applicationConfig', applicationConfig)
    var token = wx.getStorageSync('token')
    if (token !== '') {
      const data = await this.getMyDoctor(1)
      
      this.setData({
        isLogin: data ? true : false
      }, () => {
        this.getcounselorList()
      })
    } else {
      this.setData({
        isLogin: false
      })
    }

    // 先预加载资讯分组数据，然后再加载资讯内容
    await this.preloadGroupsList()

    // 无论是否登录都加载内容
    this.getartList()
    this.getBannerList()
    this.getList()
    app.getMessageNum()
  },

  /**
   * 预加载资讯分组数据
   */
  async preloadGroupsList() {
    console.log('开始预加载资讯分组数据')
    try {
      const { data } = await util.request(api.groupsList, {}, 'get', 1, false)
      console.log('资讯分组数据请求结果:', data)

      if (data.code === 0) {
        // 将分组数据存储到页面data中，供scrollTabs组件使用
        this.setData({
          groupsData: data.data
        })

        // 如果有分组数据，设置默认选中第一个分组
        if (data.data.length > 0) {
          console.log('设置默认分组ID:', data.data[0].id)
          this.setData({
            'artListQuery.groupId': data.data[0].id,
            artCurrent: 0
          })
        } else {
          console.warn('没有获取到分组数据')
        }
      } else {
        console.error('获取分组数据失败:', data.msg)
      }
    } catch (error) {
      console.error('预加载资讯分组失败:', error)
      // 如果分组数据加载失败，使用默认值
      this.setData({
        'artListQuery.groupId': 1,
        artCurrent: 0
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    var that = this
    // 删除登录检查，允许未登录用户也能刷新
    if (that.data.isLogin) {
      that.getMyDoctor(1)
    }
    that.getartList()
    that.getList()
    this.getcounselorList()
    that.fetchDiseaseList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    const { currentNav, artHasNext, docHasNext, couHasNext } = this.data
    if (currentNav === 2 && couHasNext) {
      this.data.counselorQuery.page++
      this.getcounselorList()
    }
    if (currentNav === 1 && docHasNext) {
      this.data.listQuery.page++
      this.getMyDoctor(2)
    }
    if (currentNav === 0 && artHasNext) {
      this.data.artListQuery.page++
      this.getartList()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {},
  async getMyDoctor(type) {
    if (type !== 1 && type !== 2) {
      type = 1
    }
    var that = this
    var token = wx.getStorageSync('token')
    if (token === '') {
      // 未登录状态下返回空数据而不是失败
      that.setData({
        list: [],
        docHasNext: false
      })
      return false
    }
    try {
      const { data } = await util.request(
        api.myDoctor,
        that.data.listQuery,
        'post',
        2,
        false
      )
      if (!data) {
        return false
      }
      const result = data.data
      console.log(result, '======data======')
      if (data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          docHasNext: result.hasNext
        })
      } else {
        util.showToast({
          icon: 'none',
          title: data.msg
        })
      }
      return true
    } catch (error) {
      return false
    }
  },
  async handleLogin() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
  },
  handleSwatchNav(e) {
    const { index } = e.currentTarget.dataset
    const { artCurrent, isLogin, groupsData } = this.data
    this.setData(
      {
        currentNav: index
      },
      () => {
        if (index === 0 && isLogin) {
          this.scrollTabs = this.selectComponent('#scrollTabs')
          console.log(artCurrent, index, 229)

          // 如果已经有预加载的分组数据，直接设置给组件
          if (groupsData && groupsData.length > 0) {
            this.scrollTabs.setData(
              {
                currentTab: artCurrent,
                navArr: groupsData
              },
              () => {
                if (artCurrent == 0) {
                  this.getartList()
                }
              }
            )
          } else {
            // 如果没有预加载数据，让组件自己请求
            this.scrollTabs.setData(
              {
                currentTab: artCurrent
              },
              () => {
                if (artCurrent == 0) {
                  this.getartList()
                }
              }
            )
          }
        }
        if (index === 1 && isLogin) {
          this.getMyDoctor(1)
        }
        if (index === 2 && isLogin) {
          this.getcounselorList()
        }
      }
    )
  },
  // 监听滚动黏贴定位
  onNavScroll(e) {
    const { isFixed } = e.detail
    this.setData({
      isFixed
    })
  },
  // 切换资讯tag
  onSwatchTab(params) {
    // 切换我的医生-医学资讯 保留tab选中状态
    this.setData(
      {
        artHasNext: false,
        'artListQuery.page': 1,
        'artListQuery.groupId': params.detail.id,
        artCurrent: params.detail.index
      },
      () => {
        this.getartList()
      }
    )
    console.log(this.data.artCurrent, '切换资讯内容')
  },
  //获取资讯内容
  async getartList() {
    console.log('开始获取资讯内容')
    const { artListQuery } = this.data
    console.log('当前资讯查询参数:', artListQuery)

    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const { artList } = this.data
      const { data } = await util.request(api.articleList, {
        ...artListQuery
      }, 'get',1, false)

      console.log('资讯内容请求结果:', data)

      if (data.code !== 0) {
        throw new Error(data.msg)
      }

      console.log('获取到的资讯数据:', data.data.result)

      this.setData({
        artList:
          artListQuery.page > 1
            ? this.data.artList.concat(data.data.result)
            : data.data.result,
        artHasNext: data.data.hasNext
      })

      console.log('设置后的资讯列表长度:', this.data.artList.length)
    } catch (error) {
      console.error('获取资讯内容失败:', error)
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  //获取咨询师列表
  async getcounselorList() {
    var that = this
    var token = wx.getStorageSync('token')
    if (token === '') {
      // 未登录状态下返回空数据而不是失败
      that.setData({
        counselorList: [],
        couHasNext: false
      })
      return false
    }
    try {
      const { counselorList, counselorQuery } = this.data
      const { data } = await util.request(api.counselorList, {
        ...counselorQuery
      }, 'get', 1, false)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        counselorList:
        counselorQuery.page > 1
          ? this.data.counselorList.concat(data.data.result)
          : data.data.result,
        couHasNext: data.data.hasNext
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  getDdepartments() {
    util.request(api.citydepart, {}, 'get', 1, false)
      .then(res => {
        if (res.data.code === 0) {
          this.data.items = res.data.data.departments.list.map(item => {
            return {
              text: item.name,
              id: item.id,
              icon: item.icon,
              default: item.icon ? item.icon : api.ImgUrl + `images/department/subject_def_${item.code}.png`
            }
          })
        }
        this.setData({
          departments: this.data.items.slice(0, 3)
        })
      })
  },
  // 图片加载失败
  onImageError(event) {
    console.log("图片加载失败：", event.detail.errMsg)
    this.setData({
      departments: this.data.departments.map(item => {
        item.default = api.ImgUrl + `images/department/<EMAIL>`
        return item
      })
    })
  },
  async getList() {
    try {
      const { data } = await util.request(api.noticeList, {}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        noticeList: data.data.result
      })
      console.log(data.data.result[0], 333)
    } catch (error) {
      throw new Error(error)
    }
  },
  async getBannerList() {
    try {
      const { data } = await util.request(api.bannerList, {}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        bannerList: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  handleDetail(e) {
    const { type, id, url, content, video } = e.currentTarget.dataset
    if (type === 1) {
      if (!content && !video) return
      wx.navigateTo({
        url: `/pages/article/articleDetail/index?id=${id}&type=banner`
      })
      console.log('图文')
    } else {
      wx.navigateTo({
        url: `/pages/webView/index?url=${url}`
      })
      console.log('外部链接')
    }
  },
  /**
   * 获取常见疾病列表
   */
  async fetchDiseaseList() {
    try {
      const { data } = await util.request(api.diseaseList,{ limit: 20 },'get', 1, false)
      if(data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        commonDiseaseList: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  }
})
