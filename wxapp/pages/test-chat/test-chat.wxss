.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.features {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.feature-item {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.test-btn {
  background: #007aff;
  color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border: none;
}

.test-btn::after {
  border: none;
}
