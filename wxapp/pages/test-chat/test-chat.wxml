<view class="container">
  <view class="title">聊天功能测试</view>
  <view class="desc">点击下面的按钮测试新的聊天功能：</view>
  <view class="features">
    <view class="feature-item">✓ 发送按钮（输入内容后高亮）</view>
    <view class="feature-item">✓ 输入框自动换行</view>
    <view class="feature-item">✓ 全屏编辑模式</view>
    <view class="feature-item">✓ 展开/收起按钮</view>
  </view>
  <button class="test-btn" bindtap="goToChat">进入聊天测试</button>
</view>
