// pages/paySuccess/paySuccess.js
const api = require('../../config/api.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    type: 1,
    icon: api.ImgUrl + 'images/ic_payment_success.png',
    id: '',
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '支付成功',
    showbtn: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      id: options.id,
      type: options.type,
      showbtn: options.showbtn == 'false' ? false : true
    })
    console.log(options, 25)
  },
  goOrderDetail() {
    if (this.data.type == 1) {
      wx.redirectTo({
        url: `/pages/orderDetail/orderDetail?orderSn=${this.data.id}`
      })
    } else {
      wx.redirectTo({
        url: `/pages/orderTcmDetail/index?orderSn=${this.data.id}`
      })
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
