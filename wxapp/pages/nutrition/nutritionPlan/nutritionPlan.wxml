<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  <view class="" hover-class="none" hover-stop-propagation="false">
    <view class="list pb1" wx:if="{{list.length>0}}">
      <view wx:for="{{list}}" wx:for-item="item" wx:key="*this">
        <navigator open-type='navigate'
          url="/pages/nutrition/nutritionDetail/nutritionDetail?tropId={{item.id}}" hover-class="none"
          class="item bg-color-white m20 p20">
            <view class="info">
              <view class="item bianhao flex">
                <view class="label">编号：</view>
                <view class="label">{{item.code}}</view>
              </view>
              <view class="item flex">
                <view class="label">就诊人：</view>
                <view class="name">{{item.patientName}}</view>
                <view class="gender">{{item.gender===1?'男':'女'}}</view>
                <view class="age">{{item.age||'--'}}岁</view>
              </view>
              <view class="item flex">
                <view class="label planLabel">干预方案：</view>
                <view class="plandDes">{{item.planName || ''}}</view>
              </view>
              <view class="item flex">
                <view class="label">医生：</view>
                <view class="name">{{item.doctorName || ''}}</view>
                <view class="deptName">{{item.departmentName || ''}}</view>
              </view>
              <view class="bottom flex">
                <button class="contact" bindtap="contactTap" hover-class="button-hover">查看详情</button>
              </view>
            </view>
          </navigator>
      </view>
    </view>
    <view wx:else class="no-data">暂无数据</view>
  </view>
</view>
