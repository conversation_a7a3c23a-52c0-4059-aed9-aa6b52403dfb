.public-title{
  padding-top: 14rpx;
  padding-bottom: 10rpx;
  padding-left: 32rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
}
.container{
  background-color: rgba(248, 248, 248, 1);
}
.info{
  font-size: 28rpx;
}


.info .item{
  font-size: 28rpx;
  padding-bottom: 16rpx;
  color: #333333;
}
.item .gender{
  margin: 0 10rpx;
}
.item .planLabel{
  min-width: 140rpx;
}
.item .plandDes{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis
}
.item .deptName{
  margin-left: 10rpx;
}

.info .bianhao{
  border-bottom: 1px solid #EEEEEE;
  padding: 20rpx  0;
  margin-bottom: 16rpx;
  color: #666666;
}


.bottom{
  justify-content: flex-end;
}
.bottom .contact{
  width: 180rpx;
  height: 52rpx;
  line-height: 50rpx;
  margin:0;
  font-size: 26rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  border: 2rpx solid #367DFF;
  color: #367DFF;
}
.no-data {
  text-align: center;
  padding: 20rpx;
}
