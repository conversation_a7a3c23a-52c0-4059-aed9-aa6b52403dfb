
// const api = require('../../../config/api')
const util = require('../../../utils/util')
const api = require('../../../config/api')
const app = getApp()
Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '我的营养方',
    list: []
  },
  contactTap(e) {
    e.stopPropagation()
    console.log(e)
  },
  getMyPlanList() {
    util.request(api.getMyPlanList, { patientId: app.globalData.userInfo.userId }, 'get')
      .then(res => {
        console.log('getMeddling', res)
        if (res.data.code === 0) {
          this.setData({
            list: res.data.data.result
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {
      })
  },
  async onLoad(options) {
    this.getMyPlanList()
  }
})
