<navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='#fff' navTitle="营养方详情"></navbar>
<view class="i-content">
  <view class="i-head">
    <view class="i-title">{{detail.hospitalName}}营养方</view>
    <view class="i-head-row">
      <view class="i-head-col">编号：{{detail.code}}</view>
      <view class="i-head-col">时间：{{detail.createdAt}}</view>
    </view>
    <view class="i-head-row">
      <view class="i-head-col">姓名：{{detail.name}}</view>
      <view class="i-head-col">性别：{{detail.gender === 1 ? '男' : '女'}}</view>
    </view>
    <view class="i-head-row">
      <view class="i-head-col">年龄：{{detail.age || '--'}}岁</view>
      <view class="i-head-col">科室：{{detail.departmentName}}</view>
    </view>
  </view>
  <!-- 逆转诊断 -->
  <view class="i-section">
    <view class="i-section-title">
      <view class="title">逆转诊断</view>
    </view>
    <view class="details">
      <view class="label">诊断</view>
      <view class="desc">
        {{detail.reverse || '--'}}
      </view>
    </view>
    <view class="details">
      <view class="label">营养处方</view>
      <view class="desc">
        {{detail.nutrition || '--'}}
      </view>
    </view>
    <view class="details">
      <view class="label">饮食处方</view>
      <view class="desc">
        {{detail.diet || '--'}}
      </view>
    </view>
    <view class="details">
      <view class="label">运动处方</view>
      <view class="desc">
        {{detail.sport || '--'}}
      </view>
    </view>
  </view>
  <!-- 干预方案 -->
  <view class="i-section" bind:tap="goPlanDetail">
    <view class="i-section-title">
      <view class="title">干预方案</view>
      <view class="more">查看详情<van-icon name="arrow"></van-icon></view>
    </view>
    <view class="i-card">
      <image src="{{planPackages.imageUrl}}" class="cover"></image>
      <view class="infos">
        <view class="info-title">{{planPackages.title}}</view>
        <view class="tips">
          <view class="date">周期：{{planPackages.cycle}}天</view>
          <view class="price">¥{{planPackages.price}}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 医嘱 -->
  <view class="i-section">
    <view class="i-section-title">
      <view class="title">医嘱</view>
    </view>
    <view class="text">
      {{detail.entrust || '--'}}
    </view>
  </view>
  <!-- 导诊档案 -->
  <view class="i-section">
    <view class="i-section-title">
      <view class="title">导诊档案</view>
    </view>
    <view class="img-list">
      <image src="{{item}}" wx:for="{{detail.diagnostics}}" data-index="{{index}}" bind:tap="viewImage"></image>
    </view>
  </view>
  <view class="i-foot">
    <view>医生：{{detail.doctorName}} <text style="margin-left: 60rpx;">{{detail.createdAt}}</text></view>
    <view>医院：{{detail.hospitalName}}</view>
  </view>
  <view wx:if="{{detail.payStatus === 2 || from === 'success'}}" class="bottom-btn-wrap">
    <van-button class="not-btn" color="#367DFF" size="large" style="width: 100%;height: 96rpx" bind:tap="showQrcodeDialog" disabled>已购买</van-button>
  </view>
  <!-- 支付状态:1:待支付;2支付成功;3:支付失败;4:支付中;5:关闭;6退款 -->
  <navigator
     wx:elif="{{detail.payStatus === 1}}"
     open-type="navigate"
     hover-class="none"
     url="/pages/payMeddling/payMeddling?tropId={{detail.tropId}}"
  >
    <view class="bottom-btn-wrap">
      <van-button color="#367DFF" size="large" style="width: 100%;height: 96rpx">立即购买</van-button>
    </view>
  </navigator>

  <image src="{{ic_prescription_seal}}" class="signature">
  </image>
</view>
<qrcodeDialog model:show="{{showDialog}}" scene="2"></qrcodeDialog>
