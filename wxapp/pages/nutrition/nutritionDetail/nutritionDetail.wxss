page {
  background: #f8f8f8;
}
.i-content {
  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));
  height: calc(100% - 176rpx);
  overflow: auto;
  position: relative;
}
.i-head {
  margin-top: 8rpx;
  background: #fff;
  padding: 28rpx;
}
.i-foot {
  height: 144rpx;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 28rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 34rpx;
  background: #fff;
  margin-top: 28rpx;
}
.i-title {
  font-size: 36rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 50rpx;
  text-align: center;
}
.i-head-row {
  margin-top: 8rpx;
  display: flex;
}
.i-head-col {
  flex: 1;
  flex-shrink: 0;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 34rpx;
}
.i-section {
  background: #fff;
  margin-top: 20rpx;
  padding: 28rpx 28rpx 0;
}
.i-section .img-list {
  display: flex;
  padding-top: 28rpx;
  flex-wrap: wrap;
}
.i-section .img-list image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.i-section .text {
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 44rpx;
  padding: 28rpx 0;
}
.i-section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.i-section-title .title {
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  color: #333333;
  line-height: 44rpx;
}
.i-section-title .more {
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 32rpx;
  display: flex;
  align-items: center;
}
.details {
  display: flex;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 42rpx;
  padding-bottom: 30rpx;
  border-bottom: 0.5rpx solid #eee;
  margin-top: 28rpx;
}
.details:last-child {
  border-bottom: none;
}
.details .label {
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 42rpx;
  width: 184rpx;
}
.details .desc {
  text-align: right;
  flex: 1;
}
.i-card {
  padding: 28rpx 0;
  display: flex;
}
.i-card .cover {
  width: 188rpx;
  height: 188rpx;
  margin-right: 20rpx;
}
.i-card .infos {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.i-card .infos .info-title {
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 44rpx;
}
.i-card .infos .tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.i-card .infos .price {
  font-size: 34rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #f05542;
  line-height: 48rpx;
}

.i-card .infos .tips .date {
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 36rpx;
}
.bottom-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 28rpx;
  padding-bottom: calc(28rpx + env(safe-area-inset-bottom));
}
.signature {
  position: absolute;
  width: 240rpx;
  height: 240rpx;
  top: 180rpx;
  right: 34rpx;
}

.not-btn .van-button--disabled {
  opacity: 0.5; /* 自定义透明度 */
  cursor: not-allowed; /* 鼠标样式显示为禁用 */
}
