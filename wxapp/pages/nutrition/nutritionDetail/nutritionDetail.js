const api = require('../../../config/api.js')
import util from '../../../utils/util'

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '营养方详情',
    detail: {},
    showDialog: false,
    ic_prescription_seal: api.ImgUrl + 'logo/ic_seal.png',
    from: ''
  },
  showQrcodeDialog() {
    return
    this.setData({
      showDialog: true
    })
  },
  goPlanDetail() {
    let url = `/pages/nutrition/nuprogrammeDetail/nuprogrammeDetail?tropId=${this.data.detail.tropId}`
    // let url = `/pages/programmeDetail/programmeDetail?id=${this.data.planPackages.id}`
    //   <!-- 支付状态:1:待支付;2支付成功;3:支付失败;4:支付中;5:关闭;6退款 -->
    if (this.data.detail.payStatus === 1) {
      url += `&needPay=1`
    }
    wx.navigateTo({
      url
    })
  },
  getDetail() {
    util.request(api.tropDetail, { tropId: this.options.tropId }, 'GET').then(res => {
      this.setData({
        detail: res.data.data,
        planPackages: res.data.data.planPackages[0]
      })
    })
  },
  viewImage(e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      urls: this.data.detail.diagnostics,
      showmenu: true,
      current: index
    })
  },
  onLoad(options) {
    this.options = options
    if (options.from) {
      this.setData({
        from: options.from
      })
    }
  },
  onShow() {
    this.getDetail()
  }
})
