<navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='#fff' navTitle="方案详情"></navbar>
<view>
  <image class="detailImg" src="{{detail.imageUrl}}" mode="widthFix"></image>
  <view class="baseInfo">
    <view class="top flex">
      <view class="price-wrap flex">
        <text class="unit">¥</text>
        <view class="price">{{detail.price || ''}}</view>
      </view>
      <view class="cycle">周期：{{detail.cycle || ''}}</view>
    </view>
    <view class="title">{{detail.title || ''}}</view>
  </view>
  <view class="bottom">
    <view class="detailTitle flex">
      <view>—</view>
      <view class="name">方案详情</view>
      <view>—</view>
    </view>
    <view class="content">
      <rich-text class="f30 c333 content" nodes="{{ detail.content }}"> </rich-text>
    </view>
  </view>
  <navigator
     open-type="redirect"
     hover-class="none"
     url="/pages/payMeddling/payMeddling?tropId={{tropId}}"
  >
    <view wx:if="{{needPay}}" class="bottom-btn-wrap">
      <van-button color="#367DFF" size="large" style="width: 100%;height: 96rpx">立即购买</van-button>
    </view>
  </navigator>
</view>
