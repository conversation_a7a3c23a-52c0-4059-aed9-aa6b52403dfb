import api from '../../../config/api.js'
import util from '../../../utils/util'

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '方案详情',
    detail: {},
    showDialog: false,
    needPay: '',
    tropId: ''
  },
  getDetail() {
    util.request(api.getDetailMeddling, { tropId: this.options.tropId }, 'GET').then(res => {
      if (res.data.code === 0) {
        this.setData({
          detail: res.data.data
        })
      }
    })
  },
  onLoad(options) {
    this.options = options
    this.setData({
      needPay: options.needPay,
      tropId: options.tropId
    })
  },
  onShow() {
    this.getDetail()
  }
})
