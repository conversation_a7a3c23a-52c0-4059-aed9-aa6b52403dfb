page{
  background: #F8F8F8;
}
.detailImg{
  width: 100%;
}
.detailTitle{
  font-size: 24rpx;
  color: #B4B4B4;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 28rpx  0;
}
.detailTitle .name{
  margin-left: 10rpx;
  margin-right: 10rpx;
}
.baseInfo{
  background: #fff;
  padding: 28rpx;
}
.baseInfo .top{
  justify-content: space-between;
}
.baseInfo .price-wrap{
  font-weight: 700;
  color: #F6270F;
}
.price-wrap .unit{
  font-size: 32rpx;
  height: 100%;
  vertical-align: bottom;
  line-height: 66rpx;
  margin-right: 8rpx;
  font-weight: 700;
}
.price-wrap .price{
  font-size: 44rpx;
  font-weight: 700;
  color: #F6270F;
}
.top .cycle{
  font-size: 28rpx;
  color: #666666;
}
.baseInfo .title{
  color: #333333;
  font-size: 32rpx;
  margin-top: 28rpx;
  font-weight: 700;
}
.detailInfo{
  background-color: #fff;
  margin: 0 28rpx;
  height: 1000rpx;
  margin-bottom: 56rpx;
}
.content{
  background: #fff;
  margin: 0 20rpx;
  padding: 0rpx 20rpx;
  padding-bottom: 120rpx;
}

.bottom-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 28rpx;
  padding-bottom:  calc(28rpx + env(safe-area-inset-bottom));
}
