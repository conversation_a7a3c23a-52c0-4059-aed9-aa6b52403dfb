.van-notice-bar{
  background: #F2F6FF;
  color: #367DFF;
  padding: 20rpx 28rpx;
  font-size: 26rpx;
}



.public-title{
  padding-top: 14rpx;
  padding-bottom: 10rpx;
  padding-left: 32rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
}
.container{
  background-color: rgba(248, 248, 248, 1);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.info{
  font-size: 28rpx;
  background-color: #fff;
  padding: 0 28rpx;
  padding-top: 28rpx;
  margin-bottom: 20rpx;
}
.info .orderId{
  font-size: 28rpx;
  color: rgba(102, 102, 102, 1);
}
.info image{
  width: 188rpx;
  height: 188rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.list{
}
.center {
  margin-bottom: 40rpx;
}
.center .des{
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 44rpx;
  margin-bottom: 20rpx;
}
.center .cycle{
  color: #999999;
}

.bottom{
  display: flex;
  justify-content: space-between;
}
.bottom .label{
  color: #666666;
  font-weight: 400;
}
.bottom .content{
  font-weight: 500;
  color: #333333;
 margin-bottom: 20rpx;
}
.cRed{
  color: #F05542!important;
}
.bottom .contact{
  width: 190rpx;
  height: 52rpx;
  line-height: 50rpx;
  margin:0;
  font-size: 26rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  border: 2rpx solid #367DFF;
  color: #367DFF;
}
.bb1{
  border-bottom: 1px solid #EEEEEE;
}
.realpay{
  display: flex;
  justify-content: flex-end;
  padding-top: 34rpx;
  padding-bottom: 32rpx;
}
.orderInfo{
  background-color: #fff;
  padding: 0 28rpx;
}
.orderInfo .bottom{
  height: 96rpx;
  line-height: 96rpx;
}
.orderInfo .content{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
}
.detailTitle{
  font-size: 24rpx;
  color: #B4B4B4;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 28rpx  0;
}
.detailTitle .name{
  margin-left: 10rpx;
  margin-right: 10rpx;
}
.detailInfo{
  background-color: #fff;
  padding: 28rpx;
  padding-bottom: 56rpx;
}
.btn{
  width: 100%;
  position: fixed;
  bottom: 0;
  padding: 16rpx 28rpx;
  background-color: #fff;
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);
}
