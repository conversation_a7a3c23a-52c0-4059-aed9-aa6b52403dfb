<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  <!-- <van-notice-bar
  color="#367DFF"
  background="#F2F6FF"
  wrapable
  scrollable="{{ false }}"
  text="可复制订单号，点击「联系营养师」了解物流进度后方案整体情况"
/> -->
  <view class="" hover-class="none" hover-stop-propagation="false">
    <view class="list pb1">
        <view class="info">
          <view class="center flex">
            <image class="icon-image" src="{{detailObj.packageImg}}" />
            <view>
              <view class="des">{{detailObj.planName}}</view>
              <view class="price-wrap flex">
                <view class="cycle">周期：{{detailObj.cycle}}天</view>
              </view>
            </view>
          </view>
          <view class="bottom flex">
            <view class="label">方案价格</view>
            <view class="content">¥{{detailObj.rawPrice}}</view>
          </view>
          <!-- <view class="bottom flex bb1">
            <view class="label">优惠券</view>
            <view class="content cRed">¥{{detailObj.disAmount || 0}}</view>
          </view> -->
          <view class="realpay">
            <view class="label flex">实付款：<view class="content cRed">¥{{detailObj.price}}</view></view>
          </view>
        </view>
        <view class="orderInfo">
          <view class="bottom flex bb1" bind:tap="copySn">
            <view class="label">订单号</view>
            <view class="content"><text selectable="{{true}}">{{detailObj.orderSn}}</text></view>
          </view>
          <view class="bottom flex">
            <view class="label">付款时间</view>
            <view class="content">{{detailObj.payTime}}</view>
          </view>
        </view>
        <view class="detailTitle flex">
          <view>—</view>
          <view class="name">方案详情</view>
          <view>—</view>
        </view>
        <view class="detailInfo">
          <rich-text nodes="{{detailObj.planContent}}"></rich-text>
        </view>
    </view>
    <!-- <view class="btn">
      <van-button color="#367DFF" bind:tap="showQrcodeDialog">联系营养师</van-button>
    </view> -->
  </view>
</view>
<qrcodeDialog model:show="{{showDialog}}" scene="3"></qrcodeDialog>
