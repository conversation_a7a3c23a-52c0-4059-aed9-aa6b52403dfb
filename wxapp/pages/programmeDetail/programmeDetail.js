
const util = require('../../utils/util')
const api = require('../../config/api')

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '方案详情',
    id: '',
    detailObj: {
      id: '2111151124120600',
      cycle: '28',
      des: '28天血糖达标管理方案 医生团队全程跟进',
      num: 1,
      price: '1280.00',
      realPay: '998.00',
      url: 'https://img-tydev.ehp.yichengzhonglian.com/images/static/2023/11/18/2d09181f-248e-42b2-aeb8-e69b5f969b0f.jpeg',
      payTime: '2023-09-08 12:00:00'
    },
    showDialog: false
  },
  contactTap(e) {
    e.stopPropagation()
    console.log(e)
  },
  showQrcodeDialog() {
    this.setData({
      showDialog: true
    })
  },
  copySn() {
    wx.setClipboardData({
      data: this.data.detailObj.orderSn
    })
  },
  getMeddlingDetail(id) {
    util.request(api.getMeddlingDetail, { id: id }, 'get')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            detailObj: res.data.data
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {
      })
  },
  async onLoad(options) {
    console.log(options.id, 'options')
    this.setData({
      id: options.id
    })
    this.getMeddlingDetail(options.id)
  }
})
