<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"
	pageNum="{{pageNum}}"></navbar>
<view class='chating-wrapper' catchtap='chatingWrapperClick' bindtouchstart="touchStart" bindtouchmove="touchMove" bindtouchend="touchEnd">
	<view id='dobBox' wx:if="{{doctorInfo.name}}" class="dob-box flex_lr bg-color-white"
		style="z-index:9999;top:{{44+statusBarHeight}}px">
		<view class="flex_m" hover-class="none" hover-stop-propagation="false">
			<view class="doc-photo">
				<image mode="aspectFill" src="{{doctorInfo.photo ? doctorInfo.photo : '/static/images/doctor_icon.png'}}"
					lazy-load="false">
				</image>
			</view>
			<view class="doc-content ml10">
				<view class="flex_m">
					<view class="f28 c333 b">
						{{doctorInfo.name}}
					</view>
					<view class="f24 c666 ml15">
						{{doctorInfo.department }}｜{{doctorInfo.title}}
					</view>
				</view>
				<view class="flex_m f24 c444 cFF9A46" wx:if="{{sendMessage}}">
					<view hover-class="none" hover-stop-propagation="false" wx:if='{{doctorInfo.actualCost>0}}'>
						本次诊费:¥{{doctorInfo.actualCost/100}}
					</view>
					<view hover-class="none" hover-stop-propagation="false" wx:else>
						本次咨询免费
					</view>
					<view selectable="false" space="false" decode="false" wx:if='{{firstNum && doctorInfo.actualCost !=0}}'>
						（已免）
					</view>
				</view>
			</view>
		</view>

		<view class="timeBox" wx:if="{{sendMessage}}">
			<view class="flex_l_m">
				<view class="f26 color-danger">距离结束还剩</view>
				<view class="f26 color-danger" wx:if='{{countdown}}'>
					<van-count-down time="{{ countdown }}" />
				</view>
			</view>
		</view>
	</view>
	<!-- 聊天模块 -->
	<import src='../template/consult.wxml' />
		<template is='consult'
		data="{{messageArr,baseUrl,previewList,doctorId,isAgain,scrollIntoView,doctorInfo,scrollHeight,scrollTop,avatar,static}}"></template>
	<!--底部输入框  -->
	<view id="chatBtn" wx:if="{{sendMessage && doctorInfo.name}}"
		class='chatinput-wrapper bg-color-white {{inputBottom==0 ? "envBottom":""}}' style='bottom:{{inputBottom}}px'
		catchtap='stopEventPropagation'>
		<view class='chatinput-content {{isFullScreen ? "chatinput-fullscreen" : ""}}'>
			<!-- 全屏模式顶部工具栏 -->
			<view wx:if="{{isFullScreen}}" class="fullscreen-header">
				<view class="fullscreen-title">编辑消息</view>
				<view class="fullscreen-close" catchtap="toggleFullScreen">完成</view>
			</view>

			<view class="chatinput-left flex_m">
				<image wx:if="{{!isFullScreen}}" src='{{sendType == 0 ? "/static/images/voices.png" : "/static/images/input.png"}}'
					class='chatinput-img ml25 mr10' mode='widthFix' catchtap='switchSendType'></image>
				<view class="chatinput-input-wrapper">
					<textarea wx:if="{{sendType == 0}}" adjust-position="{{false}}" value='{{inputValue}}' focus='{{focusFlag}}'
						bindfocus='inputFocus' bindblur='inputBlur' bindinput="oninput" bindconfirm='inputSend'
						bindkeyboardheightchange='onkeyboardheightchange' placeholder-class="c999"
						class='chatinput-input {{isFullScreen ? "chatinput-input-fullscreen" : ""}}'
						placeholder="请输入您想回复的内容" confirm-type='send' maxlength="1000"
						auto-height="{{!isFullScreen}}" show-confirm-bar="{{false}}" bindlinechange="onLineChange"></textarea>
					<button wx:if="{{sendType == 1}}"
						class="c999 {{ isLongPress ? 'chatinput-voice-mask chatinput-voice-mask-hover' : 'chatinput-voice-mask' }}"
						hover-class="none" catchtouchstart='longPressStart' catchtouchmove='pressMove' catchtouchend='longPressEnd'
						catchtouchcancel="touchCanel">按住说话</button>
					<!-- 展开/收起按钮 -->
					<view wx:if="{{sendType == 0 && lineCount > 2 && !isFullScreen}}" class="expand-btn" catchtap="toggleFullScreen">
						<text class="expand-icon">▲</text>
					</view>
				</view>
			</view>

			<!-- 全屏模式底部工具栏 -->
			<view wx:if="{{isFullScreen}}" class="fullscreen-footer">
				<view class="fullscreen-actions">
					<image src='/static/images/chat-phone.png' catchtap='chooseImageToSend' mode='widthFix'
						class='chatinput-img'></image>
					<view class="send-btn {{inputValue.trim() ? 'send-btn-active' : 'send-btn-disabled'}}" catchtap="sendTextMessage">
						发送
					</view>
				</view>
			</view>

			<!-- 普通模式右侧按钮 -->
			<view wx:if="{{!isFullScreen}}" class="chatinput-right flex_m">
				<image src='/static/images/chat-phone.png' catchtap='chooseImageToSend' mode='widthFix'
					class='chatinput-img ml10'></image>
				<!-- 发送按钮 -->
				<view class="send-btn {{inputValue.trim() ? 'send-btn-active' : 'send-btn-disabled'}}" catchtap="sendTextMessage">
					发送
				</view>
			</view>
		</view>
	</view>
	<view id="chatBtn" wx:if="{{!sendMessage && doctorInfo.name}}" class="chat-btn flex_c_m">
		<view class="chat-btn-btn flex1 flex_c_m mt25 mb25" catchtap="handleConsult" data-name='{{doctorInfo.name}}'
			data-id="{{doctorInfo.doctorId}}" data-templateId='{{templateId}}' data-type="1" data-departmentCode='{{item.departmentCode}}'>
			<view class="f28 color-primary">
				图文
			</view>
			<view class="f28 color-danger b">
				¥{{doctorInfo.consultCost / 100}}
			</view>
			<view class="f28 color-primary">
				/次
			</view>
		</view>
		<view class="chat-btn-btn flex1 flex_c_m ml20 mt25 mb25" catchtap="handleConsult" data-name='{{doctorInfo.name}}'
			data-id="{{doctorInfo.doctorId}}" data-type="2" data-templateId='{{templateId}}' wx:if="{{videoSwitch}}">
			<view class="f28 color-primary">
				视频
			</view>
			<view class="f28 b color-danger">
				¥{{doctorInfo.videoCost/100}}
			</view>
			<view class="f28 color-primary">
				/15分钟
			</view>
		</view>
	</view>
</view>
<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>
<view class="showToast" hidden="{{toastDis}}">
	<image src="/static/images/audio-icon.gif"></image>
	<!-- <image src="/static/images/audio-icon.png"></image> -->
	<text>{{isCanelAudio ? '松开手指,取消发送' : '手指上滑,取消发送'}}</text>
</view>
