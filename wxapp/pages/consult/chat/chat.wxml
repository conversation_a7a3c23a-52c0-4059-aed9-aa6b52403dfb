<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"
	pageNum="{{pageNum}}"></navbar>
<view class='chating-wrapper' catchtap='chatingWrapperClick' bindtouchstart="touchStart" bindtouchmove="touchMove" bindtouchend="touchEnd">
	<view id='dobBox' wx:if="{{doctorInfo.name}}" class="dob-box flex_lr bg-color-white"
		style="z-index:9999;top:{{44+statusBarHeight}}px">
		<view class="flex_m" hover-class="none" hover-stop-propagation="false">
			<view class="doc-photo">
				<image mode="aspectFill" src="{{doctorInfo.photo ? doctorInfo.photo : '/static/images/doctor_icon.png'}}"
					lazy-load="false">
				</image>
			</view>
			<view class="doc-content ml10">
				<view class="flex_m">
					<view class="f28 c333 b">
						{{doctorInfo.name}}
					</view>
					<view class="f24 c666 ml15">
						{{doctorInfo.department }}｜{{doctorInfo.title}}
					</view>
				</view>
				<view class="flex_m f24 c444 cFF9A46" wx:if="{{sendMessage}}">
					<view hover-class="none" hover-stop-propagation="false" wx:if='{{doctorInfo.actualCost>0}}'>
						本次诊费:¥{{doctorInfo.actualCost/100}}
					</view>
					<view hover-class="none" hover-stop-propagation="false" wx:else>
						本次咨询免费
					</view>
					<view selectable="false" space="false" decode="false" wx:if='{{firstNum && doctorInfo.actualCost !=0}}'>
						（已免）
					</view>
				</view>
			</view>
		</view>

		<view class="timeBox" wx:if="{{sendMessage}}">
			<view class="flex_l_m">
				<view class="f26 color-danger">距离结束还剩</view>
				<view class="f26 color-danger" wx:if='{{countdown}}'>
					<van-count-down time="{{ countdown }}" />
				</view>
			</view>
		</view>
	</view>
	<!-- 聊天模块 -->
	<import src='../template/consult.wxml' />
		<template is='consult'
		data="{{messageArr,baseUrl,previewList,doctorId,isAgain,scrollIntoView,doctorInfo,scrollHeight,scrollTop,avatar,static}}"></template>
	<!--底部输入框  -->
	<view id="chatBtn" wx:if="{{sendMessage && doctorInfo.name}}"
		class='chatinput-wrapper bg-color-white flex_m {{inputBottom==0 ? "envBottom":""}}' style='bottom:{{inputBottom}}px'
		catchtap='stopEventPropagation'>
		<view class='chatinput-content'>
			<image src='{{sendType == 0 ? "/static/images/voices.png" : "/static/images/input.png"}}'
				class='chatinput-img ml25 mr10' mode='widthFix' catchtap='switchSendType'></image>
			<view class="input-container" wx:if="{{sendType == 0}}">
				<textarea adjust-position="{{false}}" value='{{inputValue}}' focus='{{focusFlag}}'
					bindfocus='inputFocus' bindblur='inputBlur' bindinput="oninput" 
					bindkeyboardheightchange='onkeyboardheightchange' placeholder-class="c999" class='chatinput-textarea'
					placeholder="请输入您想回复的内容" maxlength="1000" auto-height="{{true}}"
					show-confirm-bar="{{false}}"></textarea>
			</view>
			<button wx:if="{{sendType == 1}}"
				class="c999 {{ isLongPress ? 'chatinput-voice-mask chatinput-voice-mask-hover' : 'chatinput-voice-mask' }}"
				hover-class="none" catchtouchstart='longPressStart' catchtouchmove='pressMove' catchtouchend='longPressEnd'
				catchtouchcancel="touchCanel">按住说话</button>
			<image src='/static/images/chat-phone.png' catchtap='chooseImageToSend' mode='widthFix'
				class='chatinput-img ml10 mr10'></image>
			<view class="send-btn {{inputValue && inputValue.trim() ? 'send-btn-active' : 'send-btn-disabled'}}" 
				wx:if="{{sendType == 0}}" catchtap='sendTextMessage'>发送</view>
		</view>
	</view>
	<view id="chatBtn" wx:if="{{!sendMessage && doctorInfo.name}}" class="chat-btn flex_c_m">
		<view class="chat-btn-btn flex1 flex_c_m mt25 mb25" catchtap="handleConsult" data-name='{{doctorInfo.name}}'
			data-id="{{doctorInfo.doctorId}}" data-templateId='{{templateId}}' data-type="1" data-departmentCode='{{item.departmentCode}}'>
			<view class="f28 color-primary">
				图文
			</view>
			<view class="f28 color-danger b">
				¥{{doctorInfo.consultCost / 100}}
			</view>
			<view class="f28 color-primary">
				/次
			</view>
		</view>
		<view class="chat-btn-btn flex1 flex_c_m ml20 mt25 mb25" catchtap="handleConsult" data-name='{{doctorInfo.name}}'
			data-id="{{doctorInfo.doctorId}}" data-type="2" data-templateId='{{templateId}}' wx:if="{{videoSwitch}}">
			<view class="f28 color-primary">
				视频
			</view>
			<view class="f28 b color-danger">
				¥{{doctorInfo.videoCost/100}}
			</view>
			<view class="f28 color-primary">
				/15分钟
			</view>
		</view>
	</view>
</view>
<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>
<view class="showToast" hidden="{{toastDis}}">
	<image src="/static/images/audio-icon.gif"></image>
	<!-- <image src="/static/images/audio-icon.png"></image> -->
	<text>{{isCanelAudio ? '松开手指,取消发送' : '手指上滑,取消发送'}}</text>
</view>
