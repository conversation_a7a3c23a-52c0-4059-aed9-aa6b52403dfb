Page {
  background-color: var(--bgColor);
  color: #000;
  font-size: 30rpx;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.chating-wrapper {
  width: 100%;
  min-height: 100%;
  position: relative;
  /* margin: 70rpx 0 100rpx; */
  box-sizing: border-box;
  overflow: hidden;
}
/*聊天输入框  */
.chatinput-wrapper {
  width: 100%;
  position: fixed;
  left: 0;
}
.envBottom {
  padding-bottom: env(safe-area-inset-bottom);
}
.chatinput-content {
  width: 100%;
  min-height: 70rpx;
  margin: 15rpx 20rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.chatinput-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f7f7f7;
  z-index: 9999;
  flex-direction: column;
  align-items: stretch;
  margin: 0;
  padding: 20rpx;
  box-sizing: border-box;
}

.chatinput-left {
  flex: 1;
  display: flex;
  align-items: flex-end;
}

.chatinput-right {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}
.chatinput-img {
  width: 56rpx;
  height: 56rpx;
  /*border-radius: 100%;*/
  display: inline-block;
}
.chatinput-img:active {
  opacity: 0.6;
}
.chatinput-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: flex-end;
}

.chatinput-input {
  width: 100%;
  min-height: 70rpx;
  max-height: 200rpx;
  border-radius: 8rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 15rpx 20rpx;
  font-size: 32rpx;
  line-height: 1.4;
  resize: none;
  border: 2rpx solid #e5e5e5;
  outline: none;
}

.chatinput-input-fullscreen {
  flex: 1;
  max-height: none;
  height: 60vh;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  font-size: 34rpx;
}
.chatinput-voice-mask {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 6rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  text-align: center;
  background-color: #f5f5f5;
  border: none;
  outline: none;
}
.chatinput-voice-mask-hover {
  background-color: #999;
}

/* 展开/收起按钮 */
.expand-btn {
  position: absolute;
  right: 15rpx;
  bottom: 15rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  z-index: 10;
  transition: all 0.2s ease;
}

.expand-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.expand-icon {
  font-size: 22rpx;
  color: #666;
  line-height: 1;
}

/* 发送按钮 */
.send-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  text-align: center;
  margin-left: 15rpx;
  transition: all 0.2s ease;
  min-width: 88rpx;
  height: 70rpx;
  line-height: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn-active {
  background: var(--themeColor, #07c160);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.send-btn-active:active {
  background: #06ad56;
  transform: scale(0.98);
}

.send-btn-disabled {
  background: #f0f0f0;
  color: #c0c0c0;
}

/* 全屏模式样式 */
.fullscreen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  margin-bottom: 20rpx;
}

.fullscreen-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.fullscreen-close {
  font-size: 30rpx;
  color: var(--themeColor, #07c160);
  padding: 10rpx 20rpx;
}

.fullscreen-footer {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e5e5e5;
}

.fullscreen-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/*聊天记录  */
.record-wrapper {
  width: 100%;
  /* height: 70vh; */
  position: fixed;
  left: 0;
  top: 130rpx;
  bottom: 160rpx;
}
.record-chatting-item {
  width: 100%;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
}
.record-item-time-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.record-item-time {
  border-radius: 10rpx;
  padding: 4rpx 10rpx;
  background-color: #999999;
  color: #fff;
  font-size: 26rpx;
}
.record-chatting-item-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 100%;
  display: inline-block;
}
.sendtext {
  max-width: 70%;
  background: var(--themeColor);
  border-radius: 8rpx 0 8rpx 8rpx;
  color: #333;
	word-wrap:break-word;
  word-break:break-all;
  overflow: hidden;
  color: #fff;
  display: flex;
  align-content: center;
}
.receivetext {
  max-width: 70%;
  border-radius: 0 8rpx 8rpx 8rpx;
  background-color: #fff;
  border: 1px solid #fff;
  padding: 20rpx;
  color: #333;
  word-wrap:break-word;
  word-break:break-all;
  overflow: hidden;
}
.sendimage,
.receiveimage {
  max-width: 70%;
  border-radius: 10rpx;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow: hidden;
  padding: 10rpx;
}
.receiveimage {
  border: 1px solid #fff;
  background-color: #fff;
}
.receiveimage image,
.sendimage image {
  display: block;
  max-width: 200rpx;
  height: 200rpx;
}

.self {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  color: #fff;
}
.other {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  color: #fff;
}
.right-triangle {
  position: relative;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  overflow: hidden;
}
.right-triangle .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.receiveaudio {
  background-color: #fff;
  border-radius: 0 8rpx 8rpx 8rpx;
  display: flex;
  align-items: center;
  padding: 20rpx;
  min-width: 30%;
  box-sizing: border-box;
  margin-left: -2px;
}
.sendaudio {
  background-color: var(--themeColor);
  border-radius: 8rpx 0 8rpx 8rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx;
  min-width: 30%;
  box-sizing: border-box;
  margin-left: -2px;
}
.receiveaudio {
  border-color: #fff;
}
.sendaudio .image,
.receiveaudio .image {
  width: 40rpx;
  height: 40rpx;
}
.sendaudio .text,
.receiveaudio .text {
  align-self: center;
  color: #000;
  opacity: 0.5;
}
.unread:after {
  content: " ";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f00;
}
.chat-btn {
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  position: fixed;
  bottom: 0;
  color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  /* padding-bottom: env(safe-area-inset-bottom); */
  padding-left: 30rpx;
  padding-right: 30rpx;
  background-color: #fff;
}
.chat-btn-btn {
  height: 84rpx;
  border-radius: var(--btnRadius);
  border: 2rpx solid var(--themeColor);
}
.chat-btn-title {
  line-height: 74rpx;
  font-size: 32rpx;
  padding-top: 10rpx;
}
.chat-btn-text {
  font-size: 26rpx;
  opacity: 0.6;
}
.showToast {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 300rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10rpx;
}
.showToast > image {
  width: 100rpx;
  height: 100rpx;
  display: block;
  margin: 80rpx auto 50rpx;
}
.showToast > text {
  color: #fff;
  text-align: center;
  display: block;
}
.chat-tips {
  font-size: 26rpx;
  text-align: center;
  color: #aaa;
  opacity: 0.6;
  padding: 20rpx 0;
}
.systemChat {
  color: #333;
  font-size: 32rpx;
}
.recipel {
  background-color: #fff;
  border-radius: 4rpx;
}
.recipelMess {
  width: 490rpx;
  border-radius: 8rpx;
}
.recipelMess .title {
  height: 80rpx;
  /* background: linear-gradient(180deg, rgba(74, 163, 255, 0.3) 0%, rgba(40, 147, 255, 0) 100%); */
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}
.bg_image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.recipelMess .title2 {
  height: 80rpx;
  /* background: linear-gradient(360deg, rgba(255, 181, 94, 0) 0%, #FFEEDA 100%); */
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}
.recipelMess .drug {
  padding-bottom: 10rpx;
}
.recipelMess .durguse {
  padding-top: 10rpx;
}
.sys-btn {
  padding: 20rpx;
}
.record-chatting-item-text {
  padding: 20rpx;
  display: flex;
  align-items: center;
}
/* 新增样式 */
.dob-box {
  width: 100%;
  padding: 10px 3%;
  position: fixed;
  top: 120rpx;
  left: 0;
}
.doc-photo image {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}
.van-count-down {
  color: #f06454 !important;
  color: var(--count-down-text-color, #f06454) !important;
  text-align: center;
}
.item {
  color: #f06454;
}
.bb1 {
  border-bottom: 1rpx solid #389aff;
}
button::after {
  border: none;
}
.timeBox {
  display: flex;
  align-items: center;
}

.evaluate {
  padding: 30rpx 90rpx;
  border-radius: 16rpx;
  /* background-color: #fff; */
}
.evaluate-title {
  height: 90rpx;
  background: linear-gradient(
    180deg,
    rgba(74, 163, 255, 0.3) 0%,
    rgba(40, 147, 255, 0) 100%
  );
  border-radius: 16rpx 16rpx 0px 0px;
}
.timeText {
  text-align: center;
  color: #999999;
  font-size: 26rpx;
  padding: 30rpx 0;
}
.cFF9A46 {
  color: #ff9a46;
}
.blue {
  color: var(--themeColor);
}

.noClick {
  border: 2rpx solid #e0e0e0;
  color: #bbbbbb;
}
.rich-text {
  text-decoration: underline;
  color: var(--themeColor);
}
.flex-box {
  display: flex;
}
.flex-box text {
  flex-shrink: 0;
}
.flex-box view {
  flex: 1;
}

