/* pages/addPeople/addPeople.wxss */
@import '../detail/detail.wxss';
page{
  background: #F8F8F8;
}
.notice{
  width: 686rpx;
  background: #EEF9FF;
  margin: 0 auto;
  padding-bottom: 22rpx;
  border-radius: 20rpx;
}
.notice image{
  display: block;
  float: left;
  width: 30rpx;
  height: 26rpx;
  margin-left: 24rpx;
  margin-top: 28rpx;
}
.notice view{
  width: 588rpx;
  float: right;
  color: #2B94FF;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-right: 30rpx;
  padding-top: 18rpx;
}
.relation{
  text-align: left;
}
.relation text{
  display: inline-block;
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #CCCCCC;
  line-height: 56rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666666;
}
.relation text:last-child{
  margin-right: 0rpx;
}
.relation .cur{
  color: #fff;
  background: var(--themeColor);
  border-color: var(--themeColor);
}
.radio{
  float: right;
}
.confir{
  border-top: none;
}
.sex{
  height: 108rpx;
  line-height:108rpx;
}
.icon{
  position: absolute;
  width: 44rpx;
  height: 44rpx;
  right: 0;
  top: 32rpx;
}
.container{
  padding-bottom: 0rpx;
}
.confir button{
  width: 100%;
}
.inputBox .van-cell {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

/* 必填项样式 */
.required-field {
  position: relative;
}

.required-star {
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #ee0a24;
  font-size: 32rpx;
  z-index: 1;
}

.required-field input {
  padding-left: 30rpx !important;
}

.required-field .f24 {
  margin-left: 30rpx;
}

.required-title .required-star {
  position: static;
  transform: none;
  margin-right: 8rpx;
}
