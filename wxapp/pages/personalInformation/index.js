const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
import Toast from '../../lib/vant-weapp/toast/toast'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '个人信息',
    avatarUrl: '',
    nickname: '',
    phoneNumber: '',
    realPhone: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options)
    // 获取用户现有信息
    const userInfo = wx.getStorageSync('userInfo') || {}
    console.log(userInfo)
    this.setData({
      avatarUrl: userInfo.avatar || '/static/images/default_avatar.png',
      nickname: userInfo.nickName || '',
      phoneNumber: this.formatPhoneNumber(options.phone || ''),
      realPhone: options.phone || ''
    });
  },

  formatPhoneNumber(phone) {
    if (phone.length === 11) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
    return phone;
  },

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    const sysInfo = wx.getSystemInfoSync()
    wx.uploadFile({
      url: api.uplodPhoto,
      filePath: avatarUrl,
      name: 'file',
      header: {
        'content-type': 'multipart/form-data',
        'Authorization': wx.getStorageSync('token'),
        '_m': sysInfo.model,
        '_o': 0,
        '_w': 1
      },
      success: (res) => {
        var data = JSON.parse(res.data)
        const userInfo = wx.getStorageSync('userInfo')
        userInfo.avatar = data.data
        util.setUserInfo({ ...userInfo })
        this.setData({
          avatarUrl: data.data
        })
      }
    })
  },

  onInputNickname(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  handleSave() {
    const { avatarUrl, nickname } = this.data
    
    if (!avatarUrl) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }

    if (!nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    util.request(api.uploadUserInfo, 
      { nickName: nickname, avatarUrl: this.data.avatarUrl, phone: this.data.realPhone }, 'post', 1, false)
      .then(res => {
      if (res.data.code === 0) {
        Toast({ message: '保存成功', position: 'center',duration: 2000 })
        const { nickName, phone } = res.data.data
        const userInfo = wx.getStorageSync('userInfo')
        userInfo.nickName = nickName
        userInfo.phone = phone
        util.setUserInfo({ ...userInfo })
        setTimeout(() => {
          wx.navigateBack();
        }, 1000)
      } else {
        util.showToast({ title: res.data.msg, icon: 'none' })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})