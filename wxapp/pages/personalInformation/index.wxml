<view class="personal-info">
  <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
  <view class="info-item">
    <text class="label c666 f32">头像</text>
    <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
      <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
    </button>
  </view>

  <view class="info-item ptb40">
    <text class="label c666 f32">昵称</text>
    <input type="nickname" class="nickname-input" placeholder="请输入昵称" 
           value="{{nickname}}" bindchange="onInputNickname" />
  </view>

  <view class="info-item ptb40">
    <text class="label c666 f32">手机号</text>
    <text class="phone-text disabled c999">{{phoneNumber}}</text>
  </view>

  <view class="save-btn">
    <button bindtap="handleSave">保存</button>
  </view>
</view>
<van-toast id="van-toast" />