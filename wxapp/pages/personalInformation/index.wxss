.personal-info {
  padding: 8rpx 0 0;
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx;
  border-bottom: 1rpx solid #eee;
  background: #ffffff;
}

.ptb40 {
  padding: 40rpx 28rpx;
}

.label {
  font-size: 32rpx;
  color: #666;
}

.avatar-wrapper {
  padding: 0;
  width: 68rpx !important;
  height: 68rpx !important;
  border-radius: 50%;
  background: none;
  margin-right: 0;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
}

.nickname-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  padding-right: 0;
}

.phone-text {
  flex: 1;
  font-size: 28rpx;
  text-align: right;
  padding-right: 0;
}

.disabled {
  color: #999;
}

.save-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
  background: #fff;
  z-index: 100;
}

.save-btn button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4080ff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}
