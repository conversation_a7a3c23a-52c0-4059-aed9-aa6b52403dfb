.public-title{
  padding-top: 14rpx;
  padding-bottom: 10rpx;
  padding-left: 32rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
}
.container{
  background-color: rgba(248, 248, 248, 1);
}
.info{
  font-size: 28rpx;
}
.info .orderId{
  font-size: 28rpx;
  color: rgba(102, 102, 102, 1);
}
.info image{
  width: 128rpx;
  height: 128rpx;
  margin-right: 16rpx;
}
.info .center{
  background: #F8F8F8;
  padding:8rpx;
  border-radius: 8rpx;
  margin-top: 28rpx;
  margin-bottom: 20rpx;
}
.center .des{
  font-weight: 400;
  color: #333333;
}
.des-wrap{
  flex: 1;
}
.price-wrap{
  display:flex;
  justify-content: space-between;
  font-weight: 400;
  color: #666666;
  margin-top: 8rpx;
}
.info .bottom{
  display: flex;
  justify-content: space-between;
}
.bottom .realPay{
  color: #333333;
  font-weight: 700;
}
.bottom .label{
  color: #333333;
  font-weight: 400;
}
.bottom .contact{
  width: 190rpx;
  height: 52rpx;
  line-height: 50rpx;
  margin:0;
  font-size: 26rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  border: 2rpx solid #367DFF;
  color: #367DFF;
}

.empty{
  font-size: 26rpx;
  text-align: center;
  margin-top: 100rpx;
  color: #333;
}