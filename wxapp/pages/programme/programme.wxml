<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  <view class="" hover-class="none" hover-stop-propagation="false">
    <view class="list pb1" wx:if="{{list.length > 0}}">
      <view wx:for="{{list}}" wx:for-item="item" wx:key="*this">
        <navigator open-type='navigate'
          url="/pages/programmeDetail/programmeDetail?id={{item.id}}" hover-class="none"
          class="item bg-color-white m20 p20">
            <view class="info">
              <view class="orderId">订单号：{{item.orderSn}}</view>
              <view class="center flex">
                <image class="icon-image" src="{{item.packageImg}}" />
                <view class="des-wrap">
                  <view class="des">{{item.planName}}</view>
                  <view class="price-wrap flex">
                    <view class="price">¥{{item.rawPrice}}</view>
                    <view class="num">x {{item.cycle}}</view>
                  </view>
                </view>
              </view>
              <view class="bottom flex">
                <view class="realPay flex"><view class="label">实付款：</view>¥{{item.price}}</view>
                <!-- bindtap="contactTap"  -->
                <!-- <button class="contact" catch:tap="showQrcodeDialog" hover-class="button-hover">联系健管师</button> -->
              </view>
            </view>
          </navigator>
      </view>
    </view>
    <view class="list pb1 empty" wx:else>暂无数据</view>
  </view>
</view>
<qrcodeDialog model:show="{{showDialog}}" scene="3"></qrcodeDialog>
