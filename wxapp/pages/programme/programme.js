
const util = require('../../utils/util')
const api = require('../../config/api')
const app = getApp()

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '我的方案',
    list: [],
    showDialog: false
  },
  getMeddling() {
    util.request(api.getMeddling, { patientId: app.globalData.userInfo.userId }, 'get')
      .then(res => {
        console.log('getMeddling', res)
        if (res.data.code === 0) {
          this.setData({
            list: res.data.data.result
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {
      })
  },
  showQrcodeDialog() {
    this.setData({
      showDialog: true
    })
  },
  async onLoad(options) {
    this.getMeddling()
  }
})
