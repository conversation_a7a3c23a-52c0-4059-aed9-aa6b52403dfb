// pages/pay/pay.js
const api = require('../../config/api')
const util = require('../../utils/util')
const Big = require('../../lib/big.min.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    doctor: {},
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '支付',
    couponData: null, // 优惠券数据
    planPackages: {},
    showDialog: false,
    payPrice: 0

  },
  payinfo() {
    var that = this
    var data = {
      tropId: this.tropId,
      price: this.data.planPackages.price,
      voucherId: this.data.couponData ? this.data.couponData.id : ''
    }
    util.showLoading({
      title: 'loading',
      mask: true
    })
    util.request(api.planOrderPay, data, 'post', 1)
      .then(res => {
        const payRes = res
        if (res.data.code === 0) {
          var wxPayData = res.data.data
          util.hideLoading()
          if (wxPayData.payStatus == '2') {
            that.paySuccess(payRes)
          } else {
            wx.requestPayment({
              'timeStamp': wxPayData.timeStamp,
              'nonceStr': wxPayData.nonceStr,
              'package': wxPayData.package,
              'signType': 'MD5',
              'paySign': wxPayData.paySign,
              'success': function(res) {
                that.paySuccess(payRes)
              },
              'fail': function(res) {
                util.showToast({
                  title: '支付失败',
                  icon: 'none'
                })
                if (data.voucherId) {
                  util.request(api.backVoucher, { orderSn: wxPayData.orderSn }, 'post')
                }
              },
              'complete': function(res) {}
            })
          }
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {})
  },
  paySuccess(res) {
    const that = this
    util.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 1000,
      success: function() {
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/payPlanSuccess/payPlanSuccess?tropId=' + that.tropId + '&orderId=' + res.data.data.id
          })
        }, 1000)
      }
    })
  },
  goMyCoupon() {
    wx.navigateTo({
      url: '/pages/myCoupon/myCoupon?from=pay&paytype=recipe'
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    const app = getApp()
    app.globalData.couponData = null
    this.tropId = options.tropId
    util.request(api.tropDetail, { tropId: options.tropId }, 'GET').then(res => {
      const planPackages = res.data.data.planPackages[0]
      this.setData({
        payPrice: planPackages.price,
        planPackages
      })
    })
    this.noPayCheck()
  },
  noPayCheck() {
    util.request(api.noPayCheck, { patientId: wx.getStorageSync('userInfo').userId }, 'post')
  },
  goAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/index?type=14'
    })
  },
  openDialog() {
    this.setData({
      showDialog: true
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const app = getApp()
    const couponData = app.globalData.couponData
    if (couponData) {
      this.setData({
        payPrice: Math.max(0, new Big(this.data.planPackages.price).minus(couponData.discountAmount)),
        couponData
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
