/* pages/pay/pay.wxss */
page{
  background: #F8F8F8;
}
.photo{
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
  overflow: hidden;
}
.text view{
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}
.text view text{
  color: var(--themeColor);
}
.confir button {
  width: 120rpx;
  height: 76rpx;
  line-height: 76rpx;
  font-size: 28rpx;
}

.i-card {
  padding: 28rpx 0;
  display: flex;
}
.i-card .cover {
  width: 188rpx;
  height: 188rpx;
  margin-right: 20rpx;
}
.i-card .infos {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.i-card .infos .info-title {
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 44rpx;
}
.i-card .infos .tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.i-card .infos .price {
  font-size: 34rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #F05542;
  line-height: 48rpx;
}

.i-card .infos .tips .date {
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 36rpx;
}

.i-section {
  background: #fff;
}
.i-section .img-list {
  display: flex;
  padding-top: 28rpx
}
.i-section .img-list image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.i-section .text {
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 44rpx;
  padding: 28rpx 0;
}
.i-section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.i-section-title .title {
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 44rpx;
}
