var api = require('../../config/api.js')
var Config = require('../../config/index.js')
const util = require('../../utils/util')
const app = getApp()
const user = require('../../utils/user')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    countdown: null,
    productsArr: [],
    orderId: '', // 订单id
    orderInfo: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options')
    if (options.orderId) {
      wx.setStorageSync('orderId', options.orderId)
    }
    this.setData({
      orderId: options.orderId
    })
    if (wx.getStorageSync('token')) {
      this.getDetail(options.orderId)
    } else {
      user.loginByWeixin().then(response => {
        console.log('response:>>', response)
        user.setUserInfo(response.data.data.userInfo)
        app.globalData.userInfo = response.data.data.userInfo
        this.getDetail(options.orderId)
      })
    }
    // this.getDetail(options.orderId)
  },

  // 获取代付订单详情
  async getDetail(orderId = '') {
    try {
      util.showToast({
        title: '加载中..',
        icon: 'loading'
      })
      const { data } = await util.request(api.ordersPayDetail + `/${orderId}`, {}, 'get', 1, true)
      console.log(data)
      const result = data.data
      console.log(result)
      if (data.code === 0) {
        this.setData({
          countdown: data.data.orderCountdownTime,
          productsArr: data.data.items,
          orderInfo: data.data
        })
      } else {
        util.showToast({
          icon: 'none',
          title: data.msg
        })
      }
      util.hideToast()
    } catch (error) {
      throw new Error(error.errMsg)
    }
  },

  // 代付
  async orderPay() {
    try {
      const {
        data
      } = await util.request(util.getRealUrl(api.orderPay, this.data.orderInfo.orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      wx.requestPayment({
        ...data.data,
        success: (result) => {
          this.authSub(this.data.orderInfo.orderId)
        },
        fail: () => {
          util.showModal({
            title: '支付结果',
            content: `您已取消支付！`,
            showCancel: false,
            cancelText: '我点错了',
            cancelColor: '#666666',
            confirmText: '确定',
            success: (result) => {
            }
          })
        }
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },

  authSub(orderNumber) {
    var that = this
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        that.paySuccess(orderNumber)
      },
      fail(res) {
        that.paySuccess(orderNumber)
      }
    })
  },

  paySuccess(orderNumber) {
    wx.redirectTo({
      // url: '/pages/paySuccess/paySuccess?id=' + orderNumber + '&type=1&showbtn=false'
      url: `/pages/paySuccess/paySuccess?id=${orderNumber}&type=1&showbtn=false`
    })
  },

  // 查看订单详情
  goOrderDetail() {
    wx.redirectTo({
      url: '/pages/orderDetail/orderDetail?orderSn=' + this.data.orderInfo.orderSn
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(res) {
    console.log(res)
    if (res.from === 'button') {
      console.log("onShareAppMessage()==>>来自页面内转发按钮")
      console.log(res.target)
    } else {
      console.log("onShareAppMessage()==>>来自右上角转发菜单")
    }
    return {
      path: `/pages/paymentDetail/index?others=1&orderId=${this.data.orderId}`
    }
  }
})