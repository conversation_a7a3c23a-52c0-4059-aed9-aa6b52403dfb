{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*!\n * word-wrap <https://github.com/jonschlinkert/word-wrap>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nmodule.exports = function(str, options) {\n  options = options || {};\n  if (str == null) {\n    return str;\n  }\n\n  var width = options.width || 50;\n  var indent = (typeof options.indent === 'string')\n    ? options.indent\n    : '  ';\n\n  var newline = options.newline || '\\n' + indent;\n  var escape = typeof options.escape === 'function'\n    ? options.escape\n    : identity;\n\n  var regexString = '.{1,' + width + '}';\n  if (options.cut !== true) {\n    regexString += '([\\\\s\\u200B]+|$)|[^\\\\s\\u200B]+?([\\\\s\\u200B]+|$)';\n  }\n\n  var re = new RegExp(regexString, 'g');\n  var lines = str.match(re) || [];\n  var result = indent + lines.map(function(line) {\n    if (line.slice(-1) === '\\n') {\n      line = line.slice(0, line.length - 1);\n    }\n    return escape(line);\n  }).join(newline);\n\n  if (options.trim === true) {\n    result = result.replace(/[ \\t]*$/gm, '');\n  }\n  return result;\n};\n\nfunction identity(str) {\n  return str;\n}\n"]}