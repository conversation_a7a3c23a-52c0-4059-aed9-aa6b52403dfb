{"version": 3, "sources": ["index.js", "createStream.js", "alignTableData.js", "alignString.js", "utils.js", "getBorderCharacters.js", "calculateRowHeights.js", "calculateCellHeight.js", "wrapCell.js", "wrapString.js", "wrapWord.js", "drawBorder.js", "drawContent.js", "drawRow.js", "makeStreamConfig.js", "validateConfig.js", "generated/validators.js", "mapDataUsingRowHeights.js", "padTableData.js", "stringifyTableData.js", "truncateTableData.js", "table.js", "calculateCellWidths.js", "drawTable.js", "drawHeader.js", "makeTableConfig.js", "calculateColumnWidths.js", "validateTableData.js", "types/api.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,AIZA,ALeA,AIZA;ADIA,ADGA,ADGA,AIZA,ALeA,AIZA;ADIA,ADGA,ADGA,AIZA,ALeA,AIZA;ADIA,ADGA,AIZA,ALeA,AIZA,ALeA,AIZA;ADIA,ADGA,AIZA,ALeA,AIZA,ALeA,AIZA;ADIA,AGTA,ALeA,AIZA,ALeA,AIZA;ADIA,AIZA,ADGA,ALeA,AIZA,ADGA;ADIA,AIZA,ADGA,ALeA,AIZA,ADGA;ADIA,AIZA,ADGA,ALeA,AIZA,ADGA;ADIA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA;ALgBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA;ALgBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA;ALgBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA;ANmBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA;ANmBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA;ANmBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA,ACHA;APsBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA,ACHA;APsBA,AIZA,ADGA,ALeA,AIZA,ADGA,AIZA,ACHA,ACHA;APsBA,AGTA,ALeA,AU9BA,ANkBA,ADGA,AIZA,ACHA,ACHA;APsBA,AGTA,ALeA,AU9BA,ANkBA,ADGA,AIZA,ACHA,ACHA;APsBA,AGTA,ALeA,AU9BA,ANkBA,ADGA,AIZA,ACHA,ACHA;APsBA,AGTA,ALeA,AU9BA,ACHA,APqBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,APqBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,APqBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,ADGA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AIZA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,ARwBA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AV8BA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,AbuCA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,AbuCA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,AbuCA,AWjCA,APqBA,ACHA,ACHA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,Ad0CA,AWjCA,APqBA,AENA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,Ad0CA,AWjCA,APqBA,AENA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,Ad0CA,AWjCA,APqBA,AENA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,ACHA,Af6CA,AWjCA,ALeA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,ACHA,Af6CA,AWjCA,ALeA;APsBA,AFMA,AU9BA,ACHA,ACHA,AGTA,AXiCA,AS3BA,AGTA,ACHA,ACHA,Af6CA,AWjCA,ALeA;APsBA,AFMA,AU9BA,ACHA,AIZA,AXiCA,AS3BA,AGTA,ACHA,ACHA,ACHA,AhBgDA,AWjCA,ALeA;APsBA,AFMA,AU9BA,ACHA,AIZA,AXiCA,AS3BA,AGTA,ACHA,ACHA,ACHA,AhBgDA,AWjCA,ALeA;APsBA,AFMA,AU9BA,ACHA,AIZA,AXiCA,AS3BA,AGTA,ACHA,ACHA,ACHA,AhBgDA,AWjCA,ALeA;APsBA,AFMA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;APsBA,AFMA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;APsBA,AFMA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;APsBA,AmBzDA,ArB+DA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;APsBA,AmBzDA,ArB+DA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;APsBA,AmBzDA,ArB+DA,AU9BA,AKfA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;AYnCA,ArB+DA,AU9BA,AYpCA,APqBA,AXiCA,AS3BA,AGTA,ACHA,ACHA,AENA,ADGA,AhBgDA,AWjCA,ALeA;AYnCA,ArB+DA,AU9BA,AYpCA,APqBA,AXiCA,AS3BA,AGTA,ACHA,AGTA,ADGA,AhBgDA,AWjCA,ALeA;AYnCA,ArB+DA,AU9BA,AYpCA,APqBA,AXiCA,AS3BA,AGTA,AIZA,ADGA,AhBgDA,AMlBA;AYnCA,ArB+DA,AU9BA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AGTA,AIZA,ADGA,AhBgDA;AkBrDA,ArB+DA,AU9BA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AGTA,AIZA,ADGA,AhBgDA;AkBrDA,ArB+DA,AU9BA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AGTA,AIZA,ADGA,AhBgDA;AkBrDA,ArB+DA,AU9BA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AXiCA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AXiCA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AIZA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AIZA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AIZA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA;AkBrDA,AIZA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA,AuBrEA;ALgBA,AIZA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,ADGA,AhBgDA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AOrBA,AxBwEA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AOrBA,AxBwEA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AOrBA,AxBwEA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AjBmDA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AjBmDA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AS3BA,AWjCA,ARwBA,AIZA,AjBmDA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;ADIA,Af6CA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ARwBA,AbuCA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AavCA,ADGA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AYpCA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AYpCA,APqBA,AXiCA,AoB5DA,ArB+DA,AuBrEA;AhBiDA,AYpCA,APqBA,AS3BA,ArB+DA,AuBrEA;AhBiDA,AYpCA,APqBA,AS3BA,AENA;AhBiDA,AYpCA,APqBA,AS3BA,AENA;AhBiDA,AYpCA,APqBA,AS3BA,AENA;AhBiDA,AKfA,AS3BA,AENA;AhBiDA,AKfA,AS3BA,AENA;AhBiDA,AKfA,AS3BA,AENA;AhBiDA,AKfA,AS3BA,AENA;AhBiDA,AKfA,AS3BA,AENA;AhBiDA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;Ad2CA,AKfA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getBorderCharacters = exports.createStream = exports.table = void 0;\nconst createStream_1 = require(\"./createStream\");\nObject.defineProperty(exports, \"createStream\", { enumerable: true, get: function () { return createStream_1.createStream; } });\nconst getBorderCharacters_1 = require(\"./getBorderCharacters\");\nObject.defineProperty(exports, \"getBorderCharacters\", { enumerable: true, get: function () { return getBorderCharacters_1.getBorderCharacters; } });\nconst table_1 = require(\"./table\");\nObject.defineProperty(exports, \"table\", { enumerable: true, get: function () { return table_1.table; } });\n__exportStar(require(\"./types/api\"), exports);\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createStream = void 0;\nconst alignTableData_1 = require(\"./alignTableData\");\nconst calculateRowHeights_1 = require(\"./calculateRowHeights\");\nconst drawBorder_1 = require(\"./drawBorder\");\nconst drawRow_1 = require(\"./drawRow\");\nconst makeStreamConfig_1 = require(\"./makeStreamConfig\");\nconst mapDataUsingRowHeights_1 = require(\"./mapDataUsingRowHeights\");\nconst padTableData_1 = require(\"./padTableData\");\nconst stringifyTableData_1 = require(\"./stringifyTableData\");\nconst truncateTableData_1 = require(\"./truncateTableData\");\nconst prepareData = (data, config) => {\n    let rows = stringifyTableData_1.stringifyTableData(data);\n    rows = truncateTableData_1.truncateTableData(rows, config);\n    const rowHeights = calculateRowHeights_1.calculateRowHeights(rows, config);\n    rows = mapDataUsingRowHeights_1.mapDataUsingRowHeights(rows, rowHeights, config);\n    rows = alignTableData_1.alignTableData(rows, config);\n    rows = padTableData_1.padTableData(rows, config);\n    return rows;\n};\nconst create = (row, columnWidths, config) => {\n    const rows = prepareData([row], config);\n    const body = rows.map((literalRow) => {\n        return drawRow_1.drawRow(literalRow, config);\n    }).join('');\n    let output;\n    output = '';\n    output += drawBorder_1.drawBorderTop(columnWidths, config);\n    output += body;\n    output += drawBorder_1.drawBorderBottom(columnWidths, config);\n    output = output.trimEnd();\n    process.stdout.write(output);\n};\nconst append = (row, columnWidths, config) => {\n    const rows = prepareData([row], config);\n    const body = rows.map((literalRow) => {\n        return drawRow_1.drawRow(literalRow, config);\n    }).join('');\n    let output = '';\n    const bottom = drawBorder_1.drawBorderBottom(columnWidths, config);\n    if (bottom !== '\\n') {\n        output = '\\r\\u001B[K';\n    }\n    output += drawBorder_1.drawBorderJoin(columnWidths, config);\n    output += body;\n    output += bottom;\n    output = output.trimEnd();\n    process.stdout.write(output);\n};\nconst createStream = (userConfig) => {\n    const config = makeStreamConfig_1.makeStreamConfig(userConfig);\n    const columnWidths = Object.values(config.columns).map((column) => {\n        return column.width + column.paddingLeft + column.paddingRight;\n    });\n    let empty = true;\n    return {\n        write: (row) => {\n            if (row.length !== config.columnCount) {\n                throw new Error('Row cell count does not match the config.columnCount.');\n            }\n            if (empty) {\n                empty = false;\n                create(row, columnWidths, config);\n            }\n            else {\n                append(row, columnWidths, config);\n            }\n        },\n    };\n};\nexports.createStream = createStream;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.alignTableData = void 0;\nconst alignString_1 = require(\"./alignString\");\nconst alignTableData = (rows, config) => {\n    return rows.map((row) => {\n        return row.map((cell, cellIndex) => {\n            const { width, alignment } = config.columns[cellIndex];\n            return alignString_1.alignString(cell, width, alignment);\n        });\n    });\n};\nexports.alignTableData = alignTableData;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.alignString = void 0;\nconst string_width_1 = __importDefault(require(\"string-width\"));\nconst utils_1 = require(\"./utils\");\nconst alignLeft = (subject, width) => {\n    return subject + ' '.repeat(width);\n};\nconst alignRight = (subject, width) => {\n    return ' '.repeat(width) + subject;\n};\nconst alignCenter = (subject, width) => {\n    return ' '.repeat(Math.floor(width / 2)) + subject + ' '.repeat(Math.ceil(width / 2));\n};\nconst alignJustify = (subject, width) => {\n    const spaceSequenceCount = utils_1.countSpaceSequence(subject);\n    if (spaceSequenceCount === 0) {\n        return alignLeft(subject, width);\n    }\n    const addingSpaces = utils_1.distributeUnevenly(width, spaceSequenceCount);\n    if (Math.max(...addingSpaces) > 3) {\n        return alignLeft(subject, width);\n    }\n    let spaceSequenceIndex = 0;\n    return subject.replace(/\\s+/g, (groupSpace) => {\n        return groupSpace + ' '.repeat(addingSpaces[spaceSequenceIndex++]);\n    });\n};\n/**\n * Pads a string to the left and/or right to position the subject\n * text in a desired alignment within a container.\n */\nconst alignString = (subject, containerWidth, alignment) => {\n    const subjectWidth = string_width_1.default(subject);\n    if (subjectWidth === containerWidth) {\n        return subject;\n    }\n    if (subjectWidth > containerWidth) {\n        throw new Error('Subject parameter value width cannot be greater than the container width.');\n    }\n    if (subjectWidth === 0) {\n        return ' '.repeat(containerWidth);\n    }\n    const availableWidth = containerWidth - subjectWidth;\n    if (alignment === 'left') {\n        return alignLeft(subject, availableWidth);\n    }\n    if (alignment === 'right') {\n        return alignRight(subject, availableWidth);\n    }\n    if (alignment === 'justify') {\n        return alignJustify(subject, availableWidth);\n    }\n    return alignCenter(subject, availableWidth);\n};\nexports.alignString = alignString;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.distributeUnevenly = exports.countSpaceSequence = exports.groupBySizes = exports.makeBorderConfig = exports.splitAnsi = exports.normalizeString = void 0;\nconst slice_ansi_1 = __importDefault(require(\"slice-ansi\"));\nconst string_width_1 = __importDefault(require(\"string-width\"));\nconst strip_ansi_1 = __importDefault(require(\"strip-ansi\"));\nconst getBorderCharacters_1 = require(\"./getBorderCharacters\");\n/**\n * Converts Windows-style newline to Unix-style\n *\n * @internal\n */\nconst normalizeString = (input) => {\n    return input.replace(/\\r\\n/g, '\\n');\n};\nexports.normalizeString = normalizeString;\n/**\n * Splits ansi string by newlines\n *\n * @internal\n */\nconst splitAnsi = (input) => {\n    const lengths = strip_ansi_1.default(input).split('\\n').map(string_width_1.default);\n    const result = [];\n    let startIndex = 0;\n    lengths.forEach((length) => {\n        result.push(length === 0 ? '' : slice_ansi_1.default(input, startIndex, startIndex + length));\n        // Plus 1 for the newline character itself\n        startIndex += length + 1;\n    });\n    return result;\n};\nexports.splitAnsi = splitAnsi;\n/**\n * Merges user provided border characters with the default border (\"honeywell\") characters.\n *\n * @internal\n */\nconst makeBorderConfig = (border) => {\n    return {\n        ...getBorderCharacters_1.getBorderCharacters('honeywell'),\n        ...border,\n    };\n};\nexports.makeBorderConfig = makeBorderConfig;\n/**\n * Groups the array into sub-arrays by sizes.\n *\n * @internal\n * @example\n * groupBySizes(['a', 'b', 'c', 'd', 'e'], [2, 1, 2]) = [ ['a', 'b'], ['c'], ['d', 'e'] ]\n */\nconst groupBySizes = (array, sizes) => {\n    let startIndex = 0;\n    return sizes.map((size) => {\n        const group = array.slice(startIndex, startIndex + size);\n        startIndex += size;\n        return group;\n    });\n};\nexports.groupBySizes = groupBySizes;\n/**\n * Counts the number of continuous spaces in a string\n *\n * @internal\n * @example\n * countGroupSpaces('a  bc  de f') = 3\n */\nconst countSpaceSequence = (input) => {\n    var _a, _b;\n    return (_b = (_a = input.match(/\\s+/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;\n};\nexports.countSpaceSequence = countSpaceSequence;\n/**\n * Creates the non-increasing number array given sum and length\n * whose the difference between maximum and minimum is not greater than 1\n *\n * @internal\n * @example\n * distributeUnevenly(6, 3) = [2, 2, 2]\n * distributeUnevenly(8, 3) = [3, 3, 2]\n */\nconst distributeUnevenly = (sum, length) => {\n    const result = Array.from({ length }).fill(Math.floor(sum / length));\n    return result.map((element, index) => {\n        return element + (index < sum % length ? 1 : 0);\n    });\n};\nexports.distributeUnevenly = distributeUnevenly;\n", "\n/* eslint-disable sort-keys-fix/sort-keys-fix */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getBorderCharacters = void 0;\nconst getBorderCharacters = (name) => {\n    if (name === 'honeywell') {\n        return {\n            topBody: '═',\n            topJoin: '╤',\n            topLeft: '╔',\n            topRight: '╗',\n            bottomBody: '═',\n            bottomJoin: '╧',\n            bottomLeft: '╚',\n            bottomRight: '╝',\n            bodyLeft: '║',\n            bodyRight: '║',\n            bodyJoin: '│',\n            headerJoin: '┬',\n            joinBody: '─',\n            joinLeft: '╟',\n            joinRight: '╢',\n            joinJoin: '┼',\n        };\n    }\n    if (name === 'norc') {\n        return {\n            topBody: '─',\n            topJoin: '┬',\n            topLeft: '┌',\n            topRight: '┐',\n            bottomBody: '─',\n            bottomJoin: '┴',\n            bottomLeft: '└',\n            bottomRight: '┘',\n            bodyLeft: '│',\n            bodyRight: '│',\n            bodyJoin: '│',\n            headerJoin: '┬',\n            joinBody: '─',\n            joinLeft: '├',\n            joinRight: '┤',\n            joinJoin: '┼',\n        };\n    }\n    if (name === 'ramac') {\n        return {\n            topBody: '-',\n            topJoin: '+',\n            topLeft: '+',\n            topRight: '+',\n            bottomBody: '-',\n            bottomJoin: '+',\n            bottomLeft: '+',\n            bottomRight: '+',\n            bodyLeft: '|',\n            bodyRight: '|',\n            bodyJoin: '|',\n            headerJoin: '+',\n            joinBody: '-',\n            joinLeft: '|',\n            joinRight: '|',\n            joinJoin: '|',\n        };\n    }\n    if (name === 'void') {\n        return {\n            topBody: '',\n            topJoin: '',\n            topLeft: '',\n            topRight: '',\n            bottomBody: '',\n            bottomJoin: '',\n            bottomLeft: '',\n            bottomRight: '',\n            bodyLeft: '',\n            bodyRight: '',\n            bodyJoin: '',\n            headerJoin: '',\n            joinBody: '',\n            joinLeft: '',\n            joinRight: '',\n            joinJoin: '',\n        };\n    }\n    throw new Error('Unknown border template \"' + name + '\".');\n};\nexports.getBorderCharacters = getBorderCharacters;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateRowHeights = void 0;\nconst calculateCellHeight_1 = require(\"./calculateCellHeight\");\n/**\n * Produces an array of values that describe the largest value length (height) in every row.\n */\nconst calculateRowHeights = (rows, config) => {\n    return rows.map((row) => {\n        let rowHeight = 1;\n        row.forEach((cell, cellIndex) => {\n            const cellHeight = calculateCellHeight_1.calculateCellHeight(cell, config.columns[cellIndex].width, config.columns[cellIndex].wrapWord);\n            rowHeight = Math.max(rowHeight, cellHeight);\n        });\n        return rowHeight;\n    });\n};\nexports.calculateRowHeights = calculateRowHeights;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateCellHeight = void 0;\nconst wrapCell_1 = require(\"./wrapCell\");\n/**\n * Calculates height of cell content in regard to its width and word wrapping.\n */\nconst calculateCellHeight = (value, columnWidth, useWrapWord = false) => {\n    return wrapCell_1.wrapCell(value, columnWidth, useWrapWord).length;\n};\nexports.calculateCellHeight = calculateCellHeight;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.wrapCell = void 0;\nconst utils_1 = require(\"./utils\");\nconst wrapString_1 = require(\"./wrapString\");\nconst wrapWord_1 = require(\"./wrapWord\");\n/**\n * Wrap a single cell value into a list of lines\n *\n * Always wraps on newlines, for the remainder uses either word or string wrapping\n * depending on user configuration.\n *\n */\nconst wrapCell = (cellValue, cellWidth, useWrapWord) => {\n    // First split on literal newlines\n    const cellLines = utils_1.splitAnsi(cellValue);\n    // Then iterate over the list and word-wrap every remaining line if necessary.\n    for (let lineNr = 0; lineNr < cellLines.length;) {\n        let lineChunks;\n        if (useWrapWord) {\n            lineChunks = wrapWord_1.wrapWord(cellLines[lineNr], cellWidth);\n        }\n        else {\n            lineChunks = wrapString_1.wrapString(cellLines[lineNr], cellWidth);\n        }\n        // Replace our original array element with whatever the wrapping returned\n        cellLines.splice(lineNr, 1, ...lineChunks);\n        lineNr += lineChunks.length;\n    }\n    return cellLines;\n};\nexports.wrapCell = wrapCell;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.wrapString = void 0;\nconst slice_ansi_1 = __importDefault(require(\"slice-ansi\"));\nconst string_width_1 = __importDefault(require(\"string-width\"));\n/**\n * Creates an array of strings split into groups the length of size.\n * This function works with strings that contain ASCII characters.\n *\n * wrapText is different from would-be \"chunk\" implementation\n * in that whitespace characters that occur on a chunk size limit are trimmed.\n *\n */\nconst wrapString = (subject, size) => {\n    let subjectSlice = subject;\n    const chunks = [];\n    do {\n        chunks.push(slice_ansi_1.default(subjectSlice, 0, size));\n        subjectSlice = slice_ansi_1.default(subjectSlice, size).trim();\n    } while (string_width_1.default(subjectSlice));\n    return chunks;\n};\nexports.wrapString = wrapString;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.wrapWord = void 0;\nconst slice_ansi_1 = __importDefault(require(\"slice-ansi\"));\nconst strip_ansi_1 = __importDefault(require(\"strip-ansi\"));\nconst calculateStringLengths = (input, size) => {\n    let subject = strip_ansi_1.default(input);\n    const chunks = [];\n    // https://regex101.com/r/gY5kZ1/1\n    const re = new RegExp('(^.{1,' + String(size) + '}(\\\\s+|$))|(^.{1,' + String(size - 1) + '}(\\\\\\\\|/|_|\\\\.|,|;|-))');\n    do {\n        let chunk;\n        const match = re.exec(subject);\n        if (match) {\n            chunk = match[0];\n            subject = subject.slice(chunk.length);\n            const trimmedLength = chunk.trim().length;\n            const offset = chunk.length - trimmedLength;\n            chunks.push([trimmedLength, offset]);\n        }\n        else {\n            chunk = subject.slice(0, size);\n            subject = subject.slice(size);\n            chunks.push([chunk.length, 0]);\n        }\n    } while (subject.length);\n    return chunks;\n};\nconst wrapWord = (input, size) => {\n    const result = [];\n    let startIndex = 0;\n    calculateStringLengths(input, size).forEach(([length, offset]) => {\n        result.push(slice_ansi_1.default(input, startIndex, startIndex + length));\n        startIndex += length + offset;\n    });\n    return result;\n};\nexports.wrapWord = wrapWord;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.drawBorderTop = exports.drawBorderJoin = exports.drawBorderBottom = exports.drawBorder = exports.createTableBorderGetter = void 0;\nconst drawContent_1 = require(\"./drawContent\");\nconst drawBorder = (columnWidths, config) => {\n    const { separator, drawVerticalLine } = config;\n    const columns = columnWidths.map((size) => {\n        return config.separator.body.repeat(size);\n    });\n    return drawContent_1.drawContent(columns, {\n        drawSeparator: drawVerticalLine,\n        separatorGetter: (index, columnCount) => {\n            if (index === 0) {\n                return separator.left;\n            }\n            if (index === columnCount) {\n                return separator.right;\n            }\n            return separator.join;\n        },\n    }) + '\\n';\n};\nexports.drawBorder = drawBorder;\nconst drawBorderTop = (columnWidths, config) => {\n    const result = drawBorder(columnWidths, {\n        ...config,\n        separator: {\n            body: config.border.topBody,\n            join: config.border.topJoin,\n            left: config.border.topLeft,\n            right: config.border.topRight,\n        },\n    });\n    if (result === '\\n') {\n        return '';\n    }\n    return result;\n};\nexports.drawBorderTop = drawBorderTop;\nconst drawBorderJoin = (columnWidths, config) => {\n    return drawBorder(columnWidths, {\n        ...config,\n        separator: {\n            body: config.border.joinBody,\n            join: config.border.joinJoin,\n            left: config.border.joinLeft,\n            right: config.border.joinRight,\n        },\n    });\n};\nexports.drawBorderJoin = drawBorderJoin;\nconst drawBorderBottom = (columnWidths, config) => {\n    return drawBorder(columnWidths, {\n        ...config,\n        separator: {\n            body: config.border.bottomBody,\n            join: config.border.bottomJoin,\n            left: config.border.bottomLeft,\n            right: config.border.bottomRight,\n        },\n    });\n};\nexports.drawBorderBottom = drawBorderBottom;\nconst createTableBorderGetter = (columnWidths, config) => {\n    return (index, size) => {\n        if (!config.header) {\n            if (index === 0) {\n                return drawBorderTop(columnWidths, config);\n            }\n            if (index === size) {\n                return drawBorderBottom(columnWidths, config);\n            }\n            return drawBorderJoin(columnWidths, config);\n        }\n        // Deal with the header\n        if (index === 0) {\n            return drawBorderTop(columnWidths, {\n                ...config,\n                border: {\n                    ...config.border,\n                    topJoin: config.border.topBody,\n                },\n            });\n        }\n        if (index === 1) {\n            return drawBorderJoin(columnWidths, {\n                ...config,\n                border: {\n                    ...config.border,\n                    joinJoin: config.border.headerJoin,\n                },\n            });\n        }\n        if (index === size) {\n            return drawBorderBottom(columnWidths, config);\n        }\n        return drawBorderJoin(columnWidths, config);\n    };\n};\nexports.createTableBorderGetter = createTableBorderGetter;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.drawContent = void 0;\n/**\n * Shared function to draw horizontal borders, rows or the entire table\n */\nconst drawContent = (contents, separatorConfig) => {\n    const { separatorGetter, drawSeparator } = separatorConfig;\n    const contentSize = contents.length;\n    const result = [];\n    if (drawSeparator(0, contentSize)) {\n        result.push(separatorGetter(0, contentSize));\n    }\n    contents.forEach((content, contentIndex) => {\n        result.push(content);\n        // Only append the middle separator if the content is not the last\n        if (contentIndex + 1 < contentSize && drawSeparator(contentIndex + 1, contentSize)) {\n            result.push(separatorGetter(contentIndex + 1, contentSize));\n        }\n    });\n    if (drawSeparator(contentSize, contentSize)) {\n        result.push(separatorGetter(contentSize, contentSize));\n    }\n    return result.join('');\n};\nexports.drawContent = drawContent;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.drawRow = void 0;\nconst drawContent_1 = require(\"./drawContent\");\nconst drawRow = (row, config) => {\n    const { border, drawVerticalLine } = config;\n    return drawContent_1.drawContent(row, {\n        drawSeparator: drawVerticalLine,\n        separatorGetter: (index, columnCount) => {\n            if (index === 0) {\n                return border.bodyLeft;\n            }\n            if (index === columnCount) {\n                return border.bodyRight;\n            }\n            return border.bodyJoin;\n        },\n    }) + '\\n';\n};\nexports.drawRow = drawRow;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.makeStreamConfig = void 0;\nconst lodash_clonedeep_1 = __importDefault(require(\"lodash.clonedeep\"));\nconst utils_1 = require(\"./utils\");\nconst validateConfig_1 = require(\"./validateConfig\");\n/**\n * Creates a configuration for every column using default\n * values for the missing configuration properties.\n */\nconst makeColumnsConfig = (columnCount, columns = {}, columnDefault) => {\n    return Array.from({ length: columnCount }).map((_, index) => {\n        return {\n            alignment: 'left',\n            paddingLeft: 1,\n            paddingRight: 1,\n            truncate: Number.POSITIVE_INFINITY,\n            verticalAlignment: 'top',\n            wrapWord: false,\n            ...columnDefault,\n            ...columns[index],\n        };\n    });\n};\n/**\n * Makes a new configuration object out of the userConfig object\n * using default values for the missing configuration properties.\n */\nconst makeStreamConfig = (userConfig) => {\n    validateConfig_1.validateConfig('streamConfig.json', userConfig);\n    const config = lodash_clonedeep_1.default(userConfig);\n    if (config.columnDefault.width === undefined) {\n        throw new Error('Must provide config.columnDefault.width when creating a stream.');\n    }\n    return {\n        drawVerticalLine: () => {\n            return true;\n        },\n        ...config,\n        border: utils_1.makeBorderConfig(config.border),\n        columns: makeColumnsConfig(config.columnCount, config.columns, config.columnDefault),\n    };\n};\nexports.makeStreamConfig = makeStreamConfig;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateConfig = void 0;\nconst validators_1 = __importDefault(require(\"./generated/validators\"));\nconst validateConfig = (schemaId, config) => {\n    const validate = validators_1.default[schemaId];\n    if (!validate(config) && validate.errors) {\n        const errors = validate.errors.map((error) => {\n            return {\n                message: error.message,\n                params: error.params,\n                schemaPath: error.schemaPath,\n            };\n        });\n        /* eslint-disable no-console */\n        console.log('config', config);\n        console.log('errors', errors);\n        /* eslint-enable no-console */\n        throw new Error('Invalid config.');\n    }\n};\nexports.validateConfig = validateConfig;\n", "\nexports[\"config.json\"] = validate43;\nconst schema13 = {\n    \"$id\": \"config.json\",\n    \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n    \"type\": \"object\",\n    \"properties\": {\n        \"border\": {\n            \"$ref\": \"shared.json#/definitions/borders\"\n        },\n        \"header\": {\n            \"type\": \"object\",\n            \"properties\": {\n                \"content\": {\n                    \"type\": \"string\"\n                },\n                \"alignment\": {\n                    \"$ref\": \"shared.json#/definitions/alignment\"\n                },\n                \"wrapWord\": {\n                    \"type\": \"boolean\"\n                },\n                \"truncate\": {\n                    \"type\": \"integer\"\n                },\n                \"paddingLeft\": {\n                    \"type\": \"integer\"\n                },\n                \"paddingRight\": {\n                    \"type\": \"integer\"\n                }\n            },\n            \"required\": [\"content\"],\n            \"additionalProperties\": false\n        },\n        \"columns\": {\n            \"$ref\": \"shared.json#/definitions/columns\"\n        },\n        \"columnDefault\": {\n            \"$ref\": \"shared.json#/definitions/column\"\n        },\n        \"drawVerticalLine\": {\n            \"typeof\": \"function\"\n        },\n        \"drawHorizontalLine\": {\n            \"typeof\": \"function\"\n        },\n        \"singleLine\": {\n            \"typeof\": \"boolean\"\n        }\n    },\n    \"additionalProperties\": false\n};\nconst schema15 = {\n    \"type\": \"object\",\n    \"properties\": {\n        \"topBody\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"topJoin\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"topLeft\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"topRight\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bottomBody\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bottomJoin\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bottomLeft\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bottomRight\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bodyLeft\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bodyRight\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"bodyJoin\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"headerJoin\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"joinBody\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"joinLeft\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"joinRight\": {\n            \"$ref\": \"#/definitions/border\"\n        },\n        \"joinJoin\": {\n            \"$ref\": \"#/definitions/border\"\n        }\n    },\n    \"additionalProperties\": false\n};\nconst func8 = Object.prototype.hasOwnProperty;\nconst schema16 = {\n    \"type\": \"string\"\n};\nfunction validate46(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (typeof data !== \"string\") {\n        const err0 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"string\"\n            },\n            message: \"must be string\"\n        };\n        if (vErrors === null) {\n            vErrors = [err0];\n        }\n        else {\n            vErrors.push(err0);\n        }\n        errors++;\n    }\n    validate46.errors = vErrors;\n    return errors === 0;\n}\nfunction validate45(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(func8.call(schema15.properties, key0))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.topBody !== undefined) {\n            if (!(validate46(data.topBody, {\n                instancePath: instancePath + \"/topBody\",\n                parentData: data,\n                parentDataProperty: \"topBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topJoin !== undefined) {\n            if (!(validate46(data.topJoin, {\n                instancePath: instancePath + \"/topJoin\",\n                parentData: data,\n                parentDataProperty: \"topJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topLeft !== undefined) {\n            if (!(validate46(data.topLeft, {\n                instancePath: instancePath + \"/topLeft\",\n                parentData: data,\n                parentDataProperty: \"topLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topRight !== undefined) {\n            if (!(validate46(data.topRight, {\n                instancePath: instancePath + \"/topRight\",\n                parentData: data,\n                parentDataProperty: \"topRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomBody !== undefined) {\n            if (!(validate46(data.bottomBody, {\n                instancePath: instancePath + \"/bottomBody\",\n                parentData: data,\n                parentDataProperty: \"bottomBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomJoin !== undefined) {\n            if (!(validate46(data.bottomJoin, {\n                instancePath: instancePath + \"/bottomJoin\",\n                parentData: data,\n                parentDataProperty: \"bottomJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomLeft !== undefined) {\n            if (!(validate46(data.bottomLeft, {\n                instancePath: instancePath + \"/bottomLeft\",\n                parentData: data,\n                parentDataProperty: \"bottomLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomRight !== undefined) {\n            if (!(validate46(data.bottomRight, {\n                instancePath: instancePath + \"/bottomRight\",\n                parentData: data,\n                parentDataProperty: \"bottomRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyLeft !== undefined) {\n            if (!(validate46(data.bodyLeft, {\n                instancePath: instancePath + \"/bodyLeft\",\n                parentData: data,\n                parentDataProperty: \"bodyLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyRight !== undefined) {\n            if (!(validate46(data.bodyRight, {\n                instancePath: instancePath + \"/bodyRight\",\n                parentData: data,\n                parentDataProperty: \"bodyRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyJoin !== undefined) {\n            if (!(validate46(data.bodyJoin, {\n                instancePath: instancePath + \"/bodyJoin\",\n                parentData: data,\n                parentDataProperty: \"bodyJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.headerJoin !== undefined) {\n            if (!(validate46(data.headerJoin, {\n                instancePath: instancePath + \"/headerJoin\",\n                parentData: data,\n                parentDataProperty: \"headerJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinBody !== undefined) {\n            if (!(validate46(data.joinBody, {\n                instancePath: instancePath + \"/joinBody\",\n                parentData: data,\n                parentDataProperty: \"joinBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinLeft !== undefined) {\n            if (!(validate46(data.joinLeft, {\n                instancePath: instancePath + \"/joinLeft\",\n                parentData: data,\n                parentDataProperty: \"joinLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinRight !== undefined) {\n            if (!(validate46(data.joinRight, {\n                instancePath: instancePath + \"/joinRight\",\n                parentData: data,\n                parentDataProperty: \"joinRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinJoin !== undefined) {\n            if (!(validate46(data.joinJoin, {\n                instancePath: instancePath + \"/joinJoin\",\n                parentData: data,\n                parentDataProperty: \"joinJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n    }\n    else {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    validate45.errors = vErrors;\n    return errors === 0;\n}\nconst schema17 = {\n    \"type\": \"string\",\n    \"enum\": [\"left\", \"right\", \"center\", \"justify\"]\n};\nconst func0 = require(\"ajv/dist/runtime/equal\").default;\nfunction validate64(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (typeof data !== \"string\") {\n        const err0 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"string\"\n            },\n            message: \"must be string\"\n        };\n        if (vErrors === null) {\n            vErrors = [err0];\n        }\n        else {\n            vErrors.push(err0);\n        }\n        errors++;\n    }\n    if (!((((data === \"left\") || (data === \"right\")) || (data === \"center\")) || (data === \"justify\"))) {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/enum\",\n            keyword: \"enum\",\n            params: {\n                allowedValues: schema17.enum\n            },\n            message: \"must be equal to one of the allowed values\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    validate64.errors = vErrors;\n    return errors === 0;\n}\nconst schema18 = {\n    \"oneOf\": [{\n            \"type\": \"object\",\n            \"patternProperties\": {\n                \"^[0-9]+$\": {\n                    \"$ref\": \"#/definitions/column\"\n                }\n            },\n            \"additionalProperties\": false\n        }, {\n            \"type\": \"array\",\n            \"items\": {\n                \"$ref\": \"#/definitions/column\"\n            }\n        }]\n};\nconst pattern0 = new RegExp(\"^[0-9]+$\", \"u\");\nconst schema19 = {\n    \"type\": \"object\",\n    \"properties\": {\n        \"alignment\": {\n            \"$ref\": \"#/definitions/alignment\"\n        },\n        \"verticalAlignment\": {\n            \"type\": \"string\",\n            \"enum\": [\"top\", \"middle\", \"bottom\"]\n        },\n        \"width\": {\n            \"type\": \"integer\",\n            \"minimum\": 1\n        },\n        \"wrapWord\": {\n            \"type\": \"boolean\"\n        },\n        \"truncate\": {\n            \"type\": \"integer\"\n        },\n        \"paddingLeft\": {\n            \"type\": \"integer\"\n        },\n        \"paddingRight\": {\n            \"type\": \"integer\"\n        }\n    },\n    \"additionalProperties\": false\n};\nfunction validate68(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (typeof data !== \"string\") {\n        const err0 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"string\"\n            },\n            message: \"must be string\"\n        };\n        if (vErrors === null) {\n            vErrors = [err0];\n        }\n        else {\n            vErrors.push(err0);\n        }\n        errors++;\n    }\n    if (!((((data === \"left\") || (data === \"right\")) || (data === \"center\")) || (data === \"justify\"))) {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/enum\",\n            keyword: \"enum\",\n            params: {\n                allowedValues: schema17.enum\n            },\n            message: \"must be equal to one of the allowed values\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    validate68.errors = vErrors;\n    return errors === 0;\n}\nfunction validate67(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(((((((key0 === \"alignment\") || (key0 === \"verticalAlignment\")) || (key0 === \"width\")) || (key0 === \"wrapWord\")) || (key0 === \"truncate\")) || (key0 === \"paddingLeft\")) || (key0 === \"paddingRight\"))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.alignment !== undefined) {\n            if (!(validate68(data.alignment, {\n                instancePath: instancePath + \"/alignment\",\n                parentData: data,\n                parentDataProperty: \"alignment\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.verticalAlignment !== undefined) {\n            let data1 = data.verticalAlignment;\n            if (typeof data1 !== \"string\") {\n                const err1 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"string\"\n                    },\n                    message: \"must be string\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err1];\n                }\n                else {\n                    vErrors.push(err1);\n                }\n                errors++;\n            }\n            if (!(((data1 === \"top\") || (data1 === \"middle\")) || (data1 === \"bottom\"))) {\n                const err2 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/enum\",\n                    keyword: \"enum\",\n                    params: {\n                        allowedValues: schema19.properties.verticalAlignment.enum\n                    },\n                    message: \"must be equal to one of the allowed values\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err2];\n                }\n                else {\n                    vErrors.push(err2);\n                }\n                errors++;\n            }\n        }\n        if (data.width !== undefined) {\n            let data2 = data.width;\n            if (!(((typeof data2 == \"number\") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {\n                const err3 = {\n                    instancePath: instancePath + \"/width\",\n                    schemaPath: \"#/properties/width/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err3];\n                }\n                else {\n                    vErrors.push(err3);\n                }\n                errors++;\n            }\n            if ((typeof data2 == \"number\") && (isFinite(data2))) {\n                if (data2 < 1 || isNaN(data2)) {\n                    const err4 = {\n                        instancePath: instancePath + \"/width\",\n                        schemaPath: \"#/properties/width/minimum\",\n                        keyword: \"minimum\",\n                        params: {\n                            comparison: \">=\",\n                            limit: 1\n                        },\n                        message: \"must be >= 1\"\n                    };\n                    if (vErrors === null) {\n                        vErrors = [err4];\n                    }\n                    else {\n                        vErrors.push(err4);\n                    }\n                    errors++;\n                }\n            }\n        }\n        if (data.wrapWord !== undefined) {\n            if (typeof data.wrapWord !== \"boolean\") {\n                const err5 = {\n                    instancePath: instancePath + \"/wrapWord\",\n                    schemaPath: \"#/properties/wrapWord/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"boolean\"\n                    },\n                    message: \"must be boolean\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err5];\n                }\n                else {\n                    vErrors.push(err5);\n                }\n                errors++;\n            }\n        }\n        if (data.truncate !== undefined) {\n            let data4 = data.truncate;\n            if (!(((typeof data4 == \"number\") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {\n                const err6 = {\n                    instancePath: instancePath + \"/truncate\",\n                    schemaPath: \"#/properties/truncate/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err6];\n                }\n                else {\n                    vErrors.push(err6);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingLeft !== undefined) {\n            let data5 = data.paddingLeft;\n            if (!(((typeof data5 == \"number\") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {\n                const err7 = {\n                    instancePath: instancePath + \"/paddingLeft\",\n                    schemaPath: \"#/properties/paddingLeft/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err7];\n                }\n                else {\n                    vErrors.push(err7);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingRight !== undefined) {\n            let data6 = data.paddingRight;\n            if (!(((typeof data6 == \"number\") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {\n                const err8 = {\n                    instancePath: instancePath + \"/paddingRight\",\n                    schemaPath: \"#/properties/paddingRight/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err8];\n                }\n                else {\n                    vErrors.push(err8);\n                }\n                errors++;\n            }\n        }\n    }\n    else {\n        const err9 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err9];\n        }\n        else {\n            vErrors.push(err9);\n        }\n        errors++;\n    }\n    validate67.errors = vErrors;\n    return errors === 0;\n}\nfunction validate66(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    const _errs0 = errors;\n    let valid0 = false;\n    let passing0 = null;\n    const _errs1 = errors;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(pattern0.test(key0))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/oneOf/0/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        for (const key1 in data) {\n            if (pattern0.test(key1)) {\n                if (!(validate67(data[key1], {\n                    instancePath: instancePath + \"/\" + key1.replace(/~/g, \"~0\").replace(/\\//g, \"~1\"),\n                    parentData: data,\n                    parentDataProperty: key1,\n                    rootData\n                }))) {\n                    vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);\n                    errors = vErrors.length;\n                }\n            }\n        }\n    }\n    else {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/oneOf/0/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    var _valid0 = _errs1 === errors;\n    if (_valid0) {\n        valid0 = true;\n        passing0 = 0;\n    }\n    const _errs5 = errors;\n    if (Array.isArray(data)) {\n        const len0 = data.length;\n        for (let i0 = 0; i0 < len0; i0++) {\n            if (!(validate67(data[i0], {\n                instancePath: instancePath + \"/\" + i0,\n                parentData: data,\n                parentDataProperty: i0,\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);\n                errors = vErrors.length;\n            }\n        }\n    }\n    else {\n        const err2 = {\n            instancePath,\n            schemaPath: \"#/oneOf/1/type\",\n            keyword: \"type\",\n            params: {\n                type: \"array\"\n            },\n            message: \"must be array\"\n        };\n        if (vErrors === null) {\n            vErrors = [err2];\n        }\n        else {\n            vErrors.push(err2);\n        }\n        errors++;\n    }\n    var _valid0 = _errs5 === errors;\n    if (_valid0 && valid0) {\n        valid0 = false;\n        passing0 = [passing0, 1];\n    }\n    else {\n        if (_valid0) {\n            valid0 = true;\n            passing0 = 1;\n        }\n    }\n    if (!valid0) {\n        const err3 = {\n            instancePath,\n            schemaPath: \"#/oneOf\",\n            keyword: \"oneOf\",\n            params: {\n                passingSchemas: passing0\n            },\n            message: \"must match exactly one schema in oneOf\"\n        };\n        if (vErrors === null) {\n            vErrors = [err3];\n        }\n        else {\n            vErrors.push(err3);\n        }\n        errors++;\n    }\n    else {\n        errors = _errs0;\n        if (vErrors !== null) {\n            if (_errs0) {\n                vErrors.length = _errs0;\n            }\n            else {\n                vErrors = null;\n            }\n        }\n    }\n    validate66.errors = vErrors;\n    return errors === 0;\n}\nfunction validate73(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(((((((key0 === \"alignment\") || (key0 === \"verticalAlignment\")) || (key0 === \"width\")) || (key0 === \"wrapWord\")) || (key0 === \"truncate\")) || (key0 === \"paddingLeft\")) || (key0 === \"paddingRight\"))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.alignment !== undefined) {\n            if (!(validate68(data.alignment, {\n                instancePath: instancePath + \"/alignment\",\n                parentData: data,\n                parentDataProperty: \"alignment\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.verticalAlignment !== undefined) {\n            let data1 = data.verticalAlignment;\n            if (typeof data1 !== \"string\") {\n                const err1 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"string\"\n                    },\n                    message: \"must be string\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err1];\n                }\n                else {\n                    vErrors.push(err1);\n                }\n                errors++;\n            }\n            if (!(((data1 === \"top\") || (data1 === \"middle\")) || (data1 === \"bottom\"))) {\n                const err2 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/enum\",\n                    keyword: \"enum\",\n                    params: {\n                        allowedValues: schema19.properties.verticalAlignment.enum\n                    },\n                    message: \"must be equal to one of the allowed values\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err2];\n                }\n                else {\n                    vErrors.push(err2);\n                }\n                errors++;\n            }\n        }\n        if (data.width !== undefined) {\n            let data2 = data.width;\n            if (!(((typeof data2 == \"number\") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {\n                const err3 = {\n                    instancePath: instancePath + \"/width\",\n                    schemaPath: \"#/properties/width/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err3];\n                }\n                else {\n                    vErrors.push(err3);\n                }\n                errors++;\n            }\n            if ((typeof data2 == \"number\") && (isFinite(data2))) {\n                if (data2 < 1 || isNaN(data2)) {\n                    const err4 = {\n                        instancePath: instancePath + \"/width\",\n                        schemaPath: \"#/properties/width/minimum\",\n                        keyword: \"minimum\",\n                        params: {\n                            comparison: \">=\",\n                            limit: 1\n                        },\n                        message: \"must be >= 1\"\n                    };\n                    if (vErrors === null) {\n                        vErrors = [err4];\n                    }\n                    else {\n                        vErrors.push(err4);\n                    }\n                    errors++;\n                }\n            }\n        }\n        if (data.wrapWord !== undefined) {\n            if (typeof data.wrapWord !== \"boolean\") {\n                const err5 = {\n                    instancePath: instancePath + \"/wrapWord\",\n                    schemaPath: \"#/properties/wrapWord/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"boolean\"\n                    },\n                    message: \"must be boolean\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err5];\n                }\n                else {\n                    vErrors.push(err5);\n                }\n                errors++;\n            }\n        }\n        if (data.truncate !== undefined) {\n            let data4 = data.truncate;\n            if (!(((typeof data4 == \"number\") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {\n                const err6 = {\n                    instancePath: instancePath + \"/truncate\",\n                    schemaPath: \"#/properties/truncate/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err6];\n                }\n                else {\n                    vErrors.push(err6);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingLeft !== undefined) {\n            let data5 = data.paddingLeft;\n            if (!(((typeof data5 == \"number\") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {\n                const err7 = {\n                    instancePath: instancePath + \"/paddingLeft\",\n                    schemaPath: \"#/properties/paddingLeft/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err7];\n                }\n                else {\n                    vErrors.push(err7);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingRight !== undefined) {\n            let data6 = data.paddingRight;\n            if (!(((typeof data6 == \"number\") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {\n                const err8 = {\n                    instancePath: instancePath + \"/paddingRight\",\n                    schemaPath: \"#/properties/paddingRight/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err8];\n                }\n                else {\n                    vErrors.push(err8);\n                }\n                errors++;\n            }\n        }\n    }\n    else {\n        const err9 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err9];\n        }\n        else {\n            vErrors.push(err9);\n        }\n        errors++;\n    }\n    validate73.errors = vErrors;\n    return errors === 0;\n}\nfunction validate43(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    /*# sourceURL=\"config.json\" */ ;\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(((((((key0 === \"border\") || (key0 === \"header\")) || (key0 === \"columns\")) || (key0 === \"columnDefault\")) || (key0 === \"drawVerticalLine\")) || (key0 === \"drawHorizontalLine\")) || (key0 === \"singleLine\"))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.border !== undefined) {\n            if (!(validate45(data.border, {\n                instancePath: instancePath + \"/border\",\n                parentData: data,\n                parentDataProperty: \"border\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate45.errors : vErrors.concat(validate45.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.header !== undefined) {\n            let data1 = data.header;\n            if (data1 && typeof data1 == \"object\" && !Array.isArray(data1)) {\n                if (data1.content === undefined) {\n                    const err1 = {\n                        instancePath: instancePath + \"/header\",\n                        schemaPath: \"#/properties/header/required\",\n                        keyword: \"required\",\n                        params: {\n                            missingProperty: \"content\"\n                        },\n                        message: \"must have required property '\" + \"content\" + \"'\"\n                    };\n                    if (vErrors === null) {\n                        vErrors = [err1];\n                    }\n                    else {\n                        vErrors.push(err1);\n                    }\n                    errors++;\n                }\n                for (const key1 in data1) {\n                    if (!((((((key1 === \"content\") || (key1 === \"alignment\")) || (key1 === \"wrapWord\")) || (key1 === \"truncate\")) || (key1 === \"paddingLeft\")) || (key1 === \"paddingRight\"))) {\n                        const err2 = {\n                            instancePath: instancePath + \"/header\",\n                            schemaPath: \"#/properties/header/additionalProperties\",\n                            keyword: \"additionalProperties\",\n                            params: {\n                                additionalProperty: key1\n                            },\n                            message: \"must NOT have additional properties\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err2];\n                        }\n                        else {\n                            vErrors.push(err2);\n                        }\n                        errors++;\n                    }\n                }\n                if (data1.content !== undefined) {\n                    if (typeof data1.content !== \"string\") {\n                        const err3 = {\n                            instancePath: instancePath + \"/header/content\",\n                            schemaPath: \"#/properties/header/properties/content/type\",\n                            keyword: \"type\",\n                            params: {\n                                type: \"string\"\n                            },\n                            message: \"must be string\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err3];\n                        }\n                        else {\n                            vErrors.push(err3);\n                        }\n                        errors++;\n                    }\n                }\n                if (data1.alignment !== undefined) {\n                    if (!(validate64(data1.alignment, {\n                        instancePath: instancePath + \"/header/alignment\",\n                        parentData: data1,\n                        parentDataProperty: \"alignment\",\n                        rootData\n                    }))) {\n                        vErrors = vErrors === null ? validate64.errors : vErrors.concat(validate64.errors);\n                        errors = vErrors.length;\n                    }\n                }\n                if (data1.wrapWord !== undefined) {\n                    if (typeof data1.wrapWord !== \"boolean\") {\n                        const err4 = {\n                            instancePath: instancePath + \"/header/wrapWord\",\n                            schemaPath: \"#/properties/header/properties/wrapWord/type\",\n                            keyword: \"type\",\n                            params: {\n                                type: \"boolean\"\n                            },\n                            message: \"must be boolean\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err4];\n                        }\n                        else {\n                            vErrors.push(err4);\n                        }\n                        errors++;\n                    }\n                }\n                if (data1.truncate !== undefined) {\n                    let data5 = data1.truncate;\n                    if (!(((typeof data5 == \"number\") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {\n                        const err5 = {\n                            instancePath: instancePath + \"/header/truncate\",\n                            schemaPath: \"#/properties/header/properties/truncate/type\",\n                            keyword: \"type\",\n                            params: {\n                                type: \"integer\"\n                            },\n                            message: \"must be integer\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err5];\n                        }\n                        else {\n                            vErrors.push(err5);\n                        }\n                        errors++;\n                    }\n                }\n                if (data1.paddingLeft !== undefined) {\n                    let data6 = data1.paddingLeft;\n                    if (!(((typeof data6 == \"number\") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {\n                        const err6 = {\n                            instancePath: instancePath + \"/header/paddingLeft\",\n                            schemaPath: \"#/properties/header/properties/paddingLeft/type\",\n                            keyword: \"type\",\n                            params: {\n                                type: \"integer\"\n                            },\n                            message: \"must be integer\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err6];\n                        }\n                        else {\n                            vErrors.push(err6);\n                        }\n                        errors++;\n                    }\n                }\n                if (data1.paddingRight !== undefined) {\n                    let data7 = data1.paddingRight;\n                    if (!(((typeof data7 == \"number\") && (!(data7 % 1) && !isNaN(data7))) && (isFinite(data7)))) {\n                        const err7 = {\n                            instancePath: instancePath + \"/header/paddingRight\",\n                            schemaPath: \"#/properties/header/properties/paddingRight/type\",\n                            keyword: \"type\",\n                            params: {\n                                type: \"integer\"\n                            },\n                            message: \"must be integer\"\n                        };\n                        if (vErrors === null) {\n                            vErrors = [err7];\n                        }\n                        else {\n                            vErrors.push(err7);\n                        }\n                        errors++;\n                    }\n                }\n            }\n            else {\n                const err8 = {\n                    instancePath: instancePath + \"/header\",\n                    schemaPath: \"#/properties/header/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"object\"\n                    },\n                    message: \"must be object\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err8];\n                }\n                else {\n                    vErrors.push(err8);\n                }\n                errors++;\n            }\n        }\n        if (data.columns !== undefined) {\n            if (!(validate66(data.columns, {\n                instancePath: instancePath + \"/columns\",\n                parentData: data,\n                parentDataProperty: \"columns\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate66.errors : vErrors.concat(validate66.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.columnDefault !== undefined) {\n            if (!(validate73(data.columnDefault, {\n                instancePath: instancePath + \"/columnDefault\",\n                parentData: data,\n                parentDataProperty: \"columnDefault\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate73.errors : vErrors.concat(validate73.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.drawVerticalLine !== undefined) {\n            if (typeof data.drawVerticalLine != \"function\") {\n                const err9 = {\n                    instancePath: instancePath + \"/drawVerticalLine\",\n                    schemaPath: \"#/properties/drawVerticalLine/typeof\",\n                    keyword: \"typeof\",\n                    params: {},\n                    message: \"should pass \\\"typeof\\\" keyword validation\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err9];\n                }\n                else {\n                    vErrors.push(err9);\n                }\n                errors++;\n            }\n        }\n        if (data.drawHorizontalLine !== undefined) {\n            if (typeof data.drawHorizontalLine != \"function\") {\n                const err10 = {\n                    instancePath: instancePath + \"/drawHorizontalLine\",\n                    schemaPath: \"#/properties/drawHorizontalLine/typeof\",\n                    keyword: \"typeof\",\n                    params: {},\n                    message: \"should pass \\\"typeof\\\" keyword validation\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err10];\n                }\n                else {\n                    vErrors.push(err10);\n                }\n                errors++;\n            }\n        }\n        if (data.singleLine !== undefined) {\n            if (typeof data.singleLine != \"boolean\") {\n                const err11 = {\n                    instancePath: instancePath + \"/singleLine\",\n                    schemaPath: \"#/properties/singleLine/typeof\",\n                    keyword: \"typeof\",\n                    params: {},\n                    message: \"should pass \\\"typeof\\\" keyword validation\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err11];\n                }\n                else {\n                    vErrors.push(err11);\n                }\n                errors++;\n            }\n        }\n    }\n    else {\n        const err12 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err12];\n        }\n        else {\n            vErrors.push(err12);\n        }\n        errors++;\n    }\n    validate43.errors = vErrors;\n    return errors === 0;\n}\nexports[\"streamConfig.json\"] = validate76;\nconst schema22 = {\n    \"$id\": \"streamConfig.json\",\n    \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n    \"type\": \"object\",\n    \"properties\": {\n        \"border\": {\n            \"$ref\": \"shared.json#/definitions/borders\"\n        },\n        \"columns\": {\n            \"$ref\": \"shared.json#/definitions/columns\"\n        },\n        \"columnDefault\": {\n            \"$ref\": \"shared.json#/definitions/column\"\n        },\n        \"columnCount\": {\n            \"type\": \"integer\",\n            \"minimum\": 1\n        },\n        \"drawVerticalLine\": {\n            \"typeof\": \"function\"\n        }\n    },\n    \"required\": [\"columnDefault\", \"columnCount\"],\n    \"additionalProperties\": false\n};\nfunction validate77(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(func8.call(schema15.properties, key0))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.topBody !== undefined) {\n            if (!(validate46(data.topBody, {\n                instancePath: instancePath + \"/topBody\",\n                parentData: data,\n                parentDataProperty: \"topBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topJoin !== undefined) {\n            if (!(validate46(data.topJoin, {\n                instancePath: instancePath + \"/topJoin\",\n                parentData: data,\n                parentDataProperty: \"topJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topLeft !== undefined) {\n            if (!(validate46(data.topLeft, {\n                instancePath: instancePath + \"/topLeft\",\n                parentData: data,\n                parentDataProperty: \"topLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.topRight !== undefined) {\n            if (!(validate46(data.topRight, {\n                instancePath: instancePath + \"/topRight\",\n                parentData: data,\n                parentDataProperty: \"topRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomBody !== undefined) {\n            if (!(validate46(data.bottomBody, {\n                instancePath: instancePath + \"/bottomBody\",\n                parentData: data,\n                parentDataProperty: \"bottomBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomJoin !== undefined) {\n            if (!(validate46(data.bottomJoin, {\n                instancePath: instancePath + \"/bottomJoin\",\n                parentData: data,\n                parentDataProperty: \"bottomJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomLeft !== undefined) {\n            if (!(validate46(data.bottomLeft, {\n                instancePath: instancePath + \"/bottomLeft\",\n                parentData: data,\n                parentDataProperty: \"bottomLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bottomRight !== undefined) {\n            if (!(validate46(data.bottomRight, {\n                instancePath: instancePath + \"/bottomRight\",\n                parentData: data,\n                parentDataProperty: \"bottomRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyLeft !== undefined) {\n            if (!(validate46(data.bodyLeft, {\n                instancePath: instancePath + \"/bodyLeft\",\n                parentData: data,\n                parentDataProperty: \"bodyLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyRight !== undefined) {\n            if (!(validate46(data.bodyRight, {\n                instancePath: instancePath + \"/bodyRight\",\n                parentData: data,\n                parentDataProperty: \"bodyRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.bodyJoin !== undefined) {\n            if (!(validate46(data.bodyJoin, {\n                instancePath: instancePath + \"/bodyJoin\",\n                parentData: data,\n                parentDataProperty: \"bodyJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.headerJoin !== undefined) {\n            if (!(validate46(data.headerJoin, {\n                instancePath: instancePath + \"/headerJoin\",\n                parentData: data,\n                parentDataProperty: \"headerJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinBody !== undefined) {\n            if (!(validate46(data.joinBody, {\n                instancePath: instancePath + \"/joinBody\",\n                parentData: data,\n                parentDataProperty: \"joinBody\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinLeft !== undefined) {\n            if (!(validate46(data.joinLeft, {\n                instancePath: instancePath + \"/joinLeft\",\n                parentData: data,\n                parentDataProperty: \"joinLeft\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinRight !== undefined) {\n            if (!(validate46(data.joinRight, {\n                instancePath: instancePath + \"/joinRight\",\n                parentData: data,\n                parentDataProperty: \"joinRight\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.joinJoin !== undefined) {\n            if (!(validate46(data.joinJoin, {\n                instancePath: instancePath + \"/joinJoin\",\n                parentData: data,\n                parentDataProperty: \"joinJoin\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);\n                errors = vErrors.length;\n            }\n        }\n    }\n    else {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    validate77.errors = vErrors;\n    return errors === 0;\n}\nfunction validate95(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    const _errs0 = errors;\n    let valid0 = false;\n    let passing0 = null;\n    const _errs1 = errors;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(pattern0.test(key0))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/oneOf/0/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        for (const key1 in data) {\n            if (pattern0.test(key1)) {\n                if (!(validate67(data[key1], {\n                    instancePath: instancePath + \"/\" + key1.replace(/~/g, \"~0\").replace(/\\//g, \"~1\"),\n                    parentData: data,\n                    parentDataProperty: key1,\n                    rootData\n                }))) {\n                    vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);\n                    errors = vErrors.length;\n                }\n            }\n        }\n    }\n    else {\n        const err1 = {\n            instancePath,\n            schemaPath: \"#/oneOf/0/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err1];\n        }\n        else {\n            vErrors.push(err1);\n        }\n        errors++;\n    }\n    var _valid0 = _errs1 === errors;\n    if (_valid0) {\n        valid0 = true;\n        passing0 = 0;\n    }\n    const _errs5 = errors;\n    if (Array.isArray(data)) {\n        const len0 = data.length;\n        for (let i0 = 0; i0 < len0; i0++) {\n            if (!(validate67(data[i0], {\n                instancePath: instancePath + \"/\" + i0,\n                parentData: data,\n                parentDataProperty: i0,\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);\n                errors = vErrors.length;\n            }\n        }\n    }\n    else {\n        const err2 = {\n            instancePath,\n            schemaPath: \"#/oneOf/1/type\",\n            keyword: \"type\",\n            params: {\n                type: \"array\"\n            },\n            message: \"must be array\"\n        };\n        if (vErrors === null) {\n            vErrors = [err2];\n        }\n        else {\n            vErrors.push(err2);\n        }\n        errors++;\n    }\n    var _valid0 = _errs5 === errors;\n    if (_valid0 && valid0) {\n        valid0 = false;\n        passing0 = [passing0, 1];\n    }\n    else {\n        if (_valid0) {\n            valid0 = true;\n            passing0 = 1;\n        }\n    }\n    if (!valid0) {\n        const err3 = {\n            instancePath,\n            schemaPath: \"#/oneOf\",\n            keyword: \"oneOf\",\n            params: {\n                passingSchemas: passing0\n            },\n            message: \"must match exactly one schema in oneOf\"\n        };\n        if (vErrors === null) {\n            vErrors = [err3];\n        }\n        else {\n            vErrors.push(err3);\n        }\n        errors++;\n    }\n    else {\n        errors = _errs0;\n        if (vErrors !== null) {\n            if (_errs0) {\n                vErrors.length = _errs0;\n            }\n            else {\n                vErrors = null;\n            }\n        }\n    }\n    validate95.errors = vErrors;\n    return errors === 0;\n}\nfunction validate99(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        for (const key0 in data) {\n            if (!(((((((key0 === \"alignment\") || (key0 === \"verticalAlignment\")) || (key0 === \"width\")) || (key0 === \"wrapWord\")) || (key0 === \"truncate\")) || (key0 === \"paddingLeft\")) || (key0 === \"paddingRight\"))) {\n                const err0 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err0];\n                }\n                else {\n                    vErrors.push(err0);\n                }\n                errors++;\n            }\n        }\n        if (data.alignment !== undefined) {\n            if (!(validate68(data.alignment, {\n                instancePath: instancePath + \"/alignment\",\n                parentData: data,\n                parentDataProperty: \"alignment\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.verticalAlignment !== undefined) {\n            let data1 = data.verticalAlignment;\n            if (typeof data1 !== \"string\") {\n                const err1 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"string\"\n                    },\n                    message: \"must be string\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err1];\n                }\n                else {\n                    vErrors.push(err1);\n                }\n                errors++;\n            }\n            if (!(((data1 === \"top\") || (data1 === \"middle\")) || (data1 === \"bottom\"))) {\n                const err2 = {\n                    instancePath: instancePath + \"/verticalAlignment\",\n                    schemaPath: \"#/properties/verticalAlignment/enum\",\n                    keyword: \"enum\",\n                    params: {\n                        allowedValues: schema19.properties.verticalAlignment.enum\n                    },\n                    message: \"must be equal to one of the allowed values\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err2];\n                }\n                else {\n                    vErrors.push(err2);\n                }\n                errors++;\n            }\n        }\n        if (data.width !== undefined) {\n            let data2 = data.width;\n            if (!(((typeof data2 == \"number\") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {\n                const err3 = {\n                    instancePath: instancePath + \"/width\",\n                    schemaPath: \"#/properties/width/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err3];\n                }\n                else {\n                    vErrors.push(err3);\n                }\n                errors++;\n            }\n            if ((typeof data2 == \"number\") && (isFinite(data2))) {\n                if (data2 < 1 || isNaN(data2)) {\n                    const err4 = {\n                        instancePath: instancePath + \"/width\",\n                        schemaPath: \"#/properties/width/minimum\",\n                        keyword: \"minimum\",\n                        params: {\n                            comparison: \">=\",\n                            limit: 1\n                        },\n                        message: \"must be >= 1\"\n                    };\n                    if (vErrors === null) {\n                        vErrors = [err4];\n                    }\n                    else {\n                        vErrors.push(err4);\n                    }\n                    errors++;\n                }\n            }\n        }\n        if (data.wrapWord !== undefined) {\n            if (typeof data.wrapWord !== \"boolean\") {\n                const err5 = {\n                    instancePath: instancePath + \"/wrapWord\",\n                    schemaPath: \"#/properties/wrapWord/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"boolean\"\n                    },\n                    message: \"must be boolean\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err5];\n                }\n                else {\n                    vErrors.push(err5);\n                }\n                errors++;\n            }\n        }\n        if (data.truncate !== undefined) {\n            let data4 = data.truncate;\n            if (!(((typeof data4 == \"number\") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {\n                const err6 = {\n                    instancePath: instancePath + \"/truncate\",\n                    schemaPath: \"#/properties/truncate/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err6];\n                }\n                else {\n                    vErrors.push(err6);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingLeft !== undefined) {\n            let data5 = data.paddingLeft;\n            if (!(((typeof data5 == \"number\") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {\n                const err7 = {\n                    instancePath: instancePath + \"/paddingLeft\",\n                    schemaPath: \"#/properties/paddingLeft/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err7];\n                }\n                else {\n                    vErrors.push(err7);\n                }\n                errors++;\n            }\n        }\n        if (data.paddingRight !== undefined) {\n            let data6 = data.paddingRight;\n            if (!(((typeof data6 == \"number\") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {\n                const err8 = {\n                    instancePath: instancePath + \"/paddingRight\",\n                    schemaPath: \"#/properties/paddingRight/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err8];\n                }\n                else {\n                    vErrors.push(err8);\n                }\n                errors++;\n            }\n        }\n    }\n    else {\n        const err9 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err9];\n        }\n        else {\n            vErrors.push(err9);\n        }\n        errors++;\n    }\n    validate99.errors = vErrors;\n    return errors === 0;\n}\nfunction validate76(data, { instancePath = \"\", parentData, parentDataProperty, rootData = data } = {}) {\n    /*# sourceURL=\"streamConfig.json\" */ ;\n    let vErrors = null;\n    let errors = 0;\n    if (data && typeof data == \"object\" && !Array.isArray(data)) {\n        if (data.columnDefault === undefined) {\n            const err0 = {\n                instancePath,\n                schemaPath: \"#/required\",\n                keyword: \"required\",\n                params: {\n                    missingProperty: \"columnDefault\"\n                },\n                message: \"must have required property '\" + \"columnDefault\" + \"'\"\n            };\n            if (vErrors === null) {\n                vErrors = [err0];\n            }\n            else {\n                vErrors.push(err0);\n            }\n            errors++;\n        }\n        if (data.columnCount === undefined) {\n            const err1 = {\n                instancePath,\n                schemaPath: \"#/required\",\n                keyword: \"required\",\n                params: {\n                    missingProperty: \"columnCount\"\n                },\n                message: \"must have required property '\" + \"columnCount\" + \"'\"\n            };\n            if (vErrors === null) {\n                vErrors = [err1];\n            }\n            else {\n                vErrors.push(err1);\n            }\n            errors++;\n        }\n        for (const key0 in data) {\n            if (!(((((key0 === \"border\") || (key0 === \"columns\")) || (key0 === \"columnDefault\")) || (key0 === \"columnCount\")) || (key0 === \"drawVerticalLine\"))) {\n                const err2 = {\n                    instancePath,\n                    schemaPath: \"#/additionalProperties\",\n                    keyword: \"additionalProperties\",\n                    params: {\n                        additionalProperty: key0\n                    },\n                    message: \"must NOT have additional properties\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err2];\n                }\n                else {\n                    vErrors.push(err2);\n                }\n                errors++;\n            }\n        }\n        if (data.border !== undefined) {\n            if (!(validate77(data.border, {\n                instancePath: instancePath + \"/border\",\n                parentData: data,\n                parentDataProperty: \"border\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate77.errors : vErrors.concat(validate77.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.columns !== undefined) {\n            if (!(validate95(data.columns, {\n                instancePath: instancePath + \"/columns\",\n                parentData: data,\n                parentDataProperty: \"columns\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate95.errors : vErrors.concat(validate95.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.columnDefault !== undefined) {\n            if (!(validate99(data.columnDefault, {\n                instancePath: instancePath + \"/columnDefault\",\n                parentData: data,\n                parentDataProperty: \"columnDefault\",\n                rootData\n            }))) {\n                vErrors = vErrors === null ? validate99.errors : vErrors.concat(validate99.errors);\n                errors = vErrors.length;\n            }\n        }\n        if (data.columnCount !== undefined) {\n            let data3 = data.columnCount;\n            if (!(((typeof data3 == \"number\") && (!(data3 % 1) && !isNaN(data3))) && (isFinite(data3)))) {\n                const err3 = {\n                    instancePath: instancePath + \"/columnCount\",\n                    schemaPath: \"#/properties/columnCount/type\",\n                    keyword: \"type\",\n                    params: {\n                        type: \"integer\"\n                    },\n                    message: \"must be integer\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err3];\n                }\n                else {\n                    vErrors.push(err3);\n                }\n                errors++;\n            }\n            if ((typeof data3 == \"number\") && (isFinite(data3))) {\n                if (data3 < 1 || isNaN(data3)) {\n                    const err4 = {\n                        instancePath: instancePath + \"/columnCount\",\n                        schemaPath: \"#/properties/columnCount/minimum\",\n                        keyword: \"minimum\",\n                        params: {\n                            comparison: \">=\",\n                            limit: 1\n                        },\n                        message: \"must be >= 1\"\n                    };\n                    if (vErrors === null) {\n                        vErrors = [err4];\n                    }\n                    else {\n                        vErrors.push(err4);\n                    }\n                    errors++;\n                }\n            }\n        }\n        if (data.drawVerticalLine !== undefined) {\n            if (typeof data.drawVerticalLine != \"function\") {\n                const err5 = {\n                    instancePath: instancePath + \"/drawVerticalLine\",\n                    schemaPath: \"#/properties/drawVerticalLine/typeof\",\n                    keyword: \"typeof\",\n                    params: {},\n                    message: \"should pass \\\"typeof\\\" keyword validation\"\n                };\n                if (vErrors === null) {\n                    vErrors = [err5];\n                }\n                else {\n                    vErrors.push(err5);\n                }\n                errors++;\n            }\n        }\n    }\n    else {\n        const err6 = {\n            instancePath,\n            schemaPath: \"#/type\",\n            keyword: \"type\",\n            params: {\n                type: \"object\"\n            },\n            message: \"must be object\"\n        };\n        if (vErrors === null) {\n            vErrors = [err6];\n        }\n        else {\n            vErrors.push(err6);\n        }\n        errors++;\n    }\n    validate76.errors = vErrors;\n    return errors === 0;\n}\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mapDataUsingRowHeights = void 0;\nconst wrapCell_1 = require(\"./wrapCell\");\nconst createEmptyStrings = (length) => {\n    return new Array(length).fill('');\n};\nconst padCellVertically = (lines, rowHeight, columnConfig) => {\n    const { verticalAlignment } = columnConfig;\n    const availableLines = rowHeight - lines.length;\n    if (verticalAlignment === 'top') {\n        return [...lines, ...createEmptyStrings(availableLines)];\n    }\n    if (verticalAlignment === 'bottom') {\n        return [...createEmptyStrings(availableLines), ...lines];\n    }\n    return [\n        ...createEmptyStrings(Math.floor(availableLines / 2)),\n        ...lines,\n        ...createEmptyStrings(Math.ceil(availableLines / 2)),\n    ];\n};\nconst flatten = (array) => {\n    return [].concat(...array);\n};\nconst mapDataUsingRowHeights = (unmappedRows, rowHeights, config) => {\n    const tableWidth = unmappedRows[0].length;\n    const mappedRows = unmappedRows.map((unmappedRow, unmappedRowIndex) => {\n        const outputRowHeight = rowHeights[unmappedRowIndex];\n        const outputRow = Array.from({ length: outputRowHeight }, () => {\n            return new Array(tableWidth).fill('');\n        });\n        unmappedRow.forEach((cell, cellIndex) => {\n            const cellLines = wrapCell_1.wrapCell(cell, config.columns[cellIndex].width, config.columns[cellIndex].wrapWord);\n            const paddedCellLines = padCellVertically(cellLines, outputRowHeight, config.columns[cellIndex]);\n            paddedCellLines.forEach((cellLine, cellLineIndex) => {\n                outputRow[cellLineIndex][cellIndex] = cellLine;\n            });\n        });\n        return outputRow;\n    });\n    return flatten(mappedRows);\n};\nexports.mapDataUsingRowHeights = mapDataUsingRowHeights;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.padTableData = exports.padString = void 0;\nconst padString = (input, paddingLeft, paddingRight) => {\n    return ' '.repeat(paddingLeft) + input + ' '.repeat(paddingRight);\n};\nexports.padString = padString;\nconst padTableData = (rows, config) => {\n    return rows.map((cells) => {\n        return cells.map((cell, cellIndex) => {\n            const { paddingLeft, paddingRight } = config.columns[cellIndex];\n            return exports.padString(cell, paddingLeft, paddingRight);\n        });\n    });\n};\nexports.padTableData = padTableData;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.stringifyTableData = void 0;\nconst utils_1 = require(\"./utils\");\nconst stringifyTableData = (rows) => {\n    return rows.map((cells) => {\n        return cells.map((cell) => {\n            return utils_1.normalizeString(String(cell));\n        });\n    });\n};\nexports.stringifyTableData = stringifyTableData;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.truncateTableData = exports.truncateString = void 0;\nconst lodash_truncate_1 = __importDefault(require(\"lodash.truncate\"));\nconst truncateString = (input, length) => {\n    return lodash_truncate_1.default(input, { length,\n        omission: '…' });\n};\nexports.truncateString = truncateString;\n/**\n * @todo Make it work with ASCII content.\n */\nconst truncateTableData = (rows, config) => {\n    return rows.map((cells) => {\n        return cells.map((cell, cellIndex) => {\n            return exports.truncateString(cell, config.columns[cellIndex].truncate);\n        });\n    });\n};\nexports.truncateTableData = truncateTableData;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.table = void 0;\nconst alignTableData_1 = require(\"./alignTableData\");\nconst calculateCellWidths_1 = require(\"./calculateCellWidths\");\nconst calculateRowHeights_1 = require(\"./calculateRowHeights\");\nconst drawTable_1 = require(\"./drawTable\");\nconst makeTableConfig_1 = require(\"./makeTableConfig\");\nconst mapDataUsingRowHeights_1 = require(\"./mapDataUsingRowHeights\");\nconst padTableData_1 = require(\"./padTableData\");\nconst stringifyTableData_1 = require(\"./stringifyTableData\");\nconst truncateTableData_1 = require(\"./truncateTableData\");\nconst validateTableData_1 = require(\"./validateTableData\");\nconst table = (data, userConfig = {}) => {\n    validateTableData_1.validateTableData(data);\n    let rows = stringifyTableData_1.stringifyTableData(data);\n    const config = makeTableConfig_1.makeTableConfig(rows, userConfig);\n    rows = truncateTableData_1.truncateTableData(rows, config);\n    const rowHeights = calculateRowHeights_1.calculateRowHeights(rows, config);\n    rows = mapDataUsingRowHeights_1.mapDataUsingRowHeights(rows, rowHeights, config);\n    rows = alignTableData_1.alignTableData(rows, config);\n    rows = padTableData_1.padTableData(rows, config);\n    const cellWidths = calculateCellWidths_1.calculateCellWidths(rows[0]);\n    return drawTable_1.drawTable(rows, cellWidths, rowHeights, config);\n};\nexports.table = table;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateCellWidths = void 0;\nconst string_width_1 = __importDefault(require(\"string-width\"));\n/**\n * Calculates width of each cell contents in a row.\n */\nconst calculateCellWidths = (cells) => {\n    return cells.map((cell) => {\n        return Math.max(...cell.split('\\n').map(string_width_1.default));\n    });\n};\nexports.calculateCellWidths = calculateCellWidths;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.drawTable = void 0;\nconst string_width_1 = __importDefault(require(\"string-width\"));\nconst drawBorder_1 = require(\"./drawBorder\");\nconst drawContent_1 = require(\"./drawContent\");\nconst drawHeader_1 = require(\"./drawHeader\");\nconst drawRow_1 = require(\"./drawRow\");\nconst utils_1 = require(\"./utils\");\nconst drawTable = (rows, columnWidths, rowHeights, config) => {\n    const { drawHorizontalLine, singleLine, } = config;\n    const contents = utils_1.groupBySizes(rows, rowHeights).map((group) => {\n        return group.map((row) => {\n            return drawRow_1.drawRow(row, config);\n        }).join('');\n    });\n    if (config.header) {\n        // assume that topLeft/right border have width = 1\n        const headerWidth = string_width_1.default(drawRow_1.drawRow(rows[0], config)) - 2 -\n            config.header.paddingLeft - config.header.paddingRight;\n        const header = drawHeader_1.drawHeader(headerWidth, config);\n        contents.unshift(header);\n    }\n    return drawContent_1.drawContent(contents, {\n        drawSeparator: (index, size) => {\n            // Top/bottom border\n            if (index === 0 || index === size) {\n                return drawHorizontalLine(index, size);\n            }\n            return !singleLine && drawHorizontalLine(index, size);\n        },\n        separatorGetter: drawBorder_1.createTableBorderGetter(columnWidths, config),\n    });\n};\nexports.drawTable = drawTable;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.drawHeader = void 0;\nconst alignString_1 = require(\"./alignString\");\nconst drawRow_1 = require(\"./drawRow\");\nconst padTableData_1 = require(\"./padTableData\");\nconst truncateTableData_1 = require(\"./truncateTableData\");\nconst wrapCell_1 = require(\"./wrapCell\");\nconst drawHeader = (width, config) => {\n    if (!config.header) {\n        throw new Error('Can not draw header without header configuration');\n    }\n    const { alignment, paddingRight, paddingLeft, wrapWord } = config.header;\n    let content = config.header.content;\n    content = truncateTableData_1.truncateString(content, config.header.truncate);\n    const headerLines = wrapCell_1.wrapCell(content, width, wrapWord);\n    return headerLines.map((headerLine) => {\n        let line = alignString_1.alignString(headerLine, width, alignment);\n        line = padTableData_1.padString(line, paddingLeft, paddingRight);\n        return drawRow_1.drawRow([line], {\n            ...config,\n            drawVerticalLine: (index) => {\n                const columnCount = config.columns.length;\n                return config.drawVerticalLine(index === 0 ? 0 : columnCount, columnCount);\n            },\n        });\n    }).join('');\n};\nexports.drawHeader = drawHeader;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.makeTableConfig = void 0;\nconst lodash_clonedeep_1 = __importDefault(require(\"lodash.clonedeep\"));\nconst calculateColumnWidths_1 = __importDefault(require(\"./calculateColumnWidths\"));\nconst utils_1 = require(\"./utils\");\nconst validateConfig_1 = require(\"./validateConfig\");\n/**\n * Creates a configuration for every column using default\n * values for the missing configuration properties.\n */\nconst makeColumnsConfig = (rows, columns, columnDefault) => {\n    const columnWidths = calculateColumnWidths_1.default(rows);\n    return rows[0].map((_, columnIndex) => {\n        return {\n            alignment: 'left',\n            paddingLeft: 1,\n            paddingRight: 1,\n            truncate: Number.POSITIVE_INFINITY,\n            verticalAlignment: 'top',\n            width: columnWidths[columnIndex],\n            wrapWord: false,\n            ...columnDefault,\n            ...columns === null || columns === void 0 ? void 0 : columns[columnIndex],\n        };\n    });\n};\nconst makeHeaderConfig = (config) => {\n    if (!config.header) {\n        return undefined;\n    }\n    return {\n        alignment: 'center',\n        paddingLeft: 1,\n        paddingRight: 1,\n        truncate: Number.POSITIVE_INFINITY,\n        wrapWord: false,\n        ...config.header,\n    };\n};\n/**\n * Makes a new configuration object out of the userConfig object\n * using default values for the missing configuration properties.\n */\nconst makeTableConfig = (rows, userConfig = {}) => {\n    var _a, _b, _c;\n    validateConfig_1.validateConfig('config.json', userConfig);\n    const config = lodash_clonedeep_1.default(userConfig);\n    return {\n        ...config,\n        border: utils_1.makeBorderConfig(config.border),\n        columns: makeColumnsConfig(rows, config.columns, config.columnDefault),\n        drawHorizontalLine: (_a = config.drawHorizontalLine) !== null && _a !== void 0 ? _a : (() => {\n            return true;\n        }),\n        drawVerticalLine: (_b = config.drawVerticalLine) !== null && _b !== void 0 ? _b : (() => {\n            return true;\n        }),\n        header: makeHeaderConfig(config),\n        singleLine: (_c = config.singleLine) !== null && _c !== void 0 ? _c : false,\n    };\n};\nexports.makeTableConfig = makeTableConfig;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst calculateCellWidths_1 = require(\"./calculateCellWidths\");\n/**\n * Produces an array of values that describe the largest value length (width) in every column.\n */\nexports.default = (rows) => {\n    const columnWidths = new Array(rows[0].length).fill(0);\n    rows.forEach((row) => {\n        const cellWidths = calculateCellWidths_1.calculateCellWidths(row);\n        cellWidths.forEach((cellWidth, cellIndex) => {\n            columnWidths[cellIndex] = Math.max(columnWidths[cellIndex], cellWidth);\n        });\n    });\n    return columnWidths;\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateTableData = void 0;\nconst utils_1 = require(\"./utils\");\nconst validateTableData = (rows) => {\n    if (!Array.isArray(rows)) {\n        throw new TypeError('Table data must be an array.');\n    }\n    if (rows.length === 0) {\n        throw new Error('Table must define at least one row.');\n    }\n    if (rows[0].length === 0) {\n        throw new Error('Table must define at least one column.');\n    }\n    const columnNumber = rows[0].length;\n    for (const row of rows) {\n        if (!Array.isArray(row)) {\n            throw new TypeError('Table row data must be an array.');\n        }\n        if (row.length !== columnNumber) {\n            throw new Error('Table must have a consistent number of cells.');\n        }\n        for (const cell of row) {\n            // eslint-disable-next-line no-control-regex\n            if (/[\\u0001-\\u0006\\u0008\\u0009\\u000B-\\u001A]/.test(utils_1.normalizeString(String(cell)))) {\n                throw new Error('Table data must not contain control characters.');\n            }\n        }\n    }\n};\nexports.validateTableData = validateTableData;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\n"]}