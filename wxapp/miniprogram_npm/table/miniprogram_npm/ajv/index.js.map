{"version": 3, "sources": ["ajv.js", "core.js", "compile/validate/index.js", "compile/validate/boolSchema.js", "compile/errors.js", "compile/codegen/index.js", "compile/codegen/code.js", "compile/codegen/scope.js", "compile/util.js", "compile/names.js", "compile/validate/dataType.js", "compile/rules.js", "compile/validate/applicability.js", "compile/validate/defaults.js", "compile/validate/keyword.js", "vocabularies/code.js", "compile/validate/subschema.js", "compile/resolve.js", "runtime/validation_error.js", "compile/ref_error.js", "compile/index.js", "refs/data.json", "vocabularies/draft7.js", "vocabularies/core/index.js", "vocabularies/core/id.js", "vocabularies/core/ref.js", "vocabularies/validation/index.js", "vocabularies/validation/limitNumber.js", "vocabularies/validation/multipleOf.js", "vocabularies/validation/limitLength.js", "runtime/ucs2length.js", "vocabularies/validation/pattern.js", "vocabularies/validation/limitProperties.js", "vocabularies/validation/required.js", "vocabularies/validation/limitItems.js", "vocabularies/validation/uniqueItems.js", "runtime/equal.js", "vocabularies/validation/const.js", "vocabularies/validation/enum.js", "vocabularies/applicator/index.js", "vocabularies/applicator/additionalItems.js", "vocabularies/applicator/prefixItems.js", "vocabularies/applicator/items.js", "vocabularies/applicator/items2020.js", "vocabularies/applicator/contains.js", "vocabularies/applicator/dependencies.js", "vocabularies/applicator/propertyNames.js", "vocabularies/applicator/additionalProperties.js", "vocabularies/applicator/properties.js", "vocabularies/applicator/patternProperties.js", "vocabularies/applicator/not.js", "vocabularies/applicator/anyOf.js", "vocabularies/applicator/oneOf.js", "vocabularies/applicator/allOf.js", "vocabularies/applicator/if.js", "vocabularies/applicator/thenElse.js", "vocabularies/format/index.js", "vocabularies/format/format.js", "vocabularies/metadata.js", "vocabularies/discriminator/index.js", "vocabularies/discriminator/types.js", "refs/json-schema-draft-07.json"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AIZA,ADGA,ADGA,ADGA;ADIA,AIZA,ADGA,ADGA,ADGA;ADIA,AIZA,ADGA,ADGA,ADGA;ADIA,AKfA,ADGA,ADGA,ADGA,ADGA;ADIA,AKfA,ADGA,ADGA,ADGA,ADGA;ADIA,AKfA,ADGA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,ADGA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,ADGA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,ADGA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,ADGA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AIZA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AIZA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AIZA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,ADGA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,ADGA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,ALeA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,ARwBA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,ADGA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA;ADIA,AMlBA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA;AKdA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA;AKdA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AbuCA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AENA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AQxBA,ANkBA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AQxBA,ANkBA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AQxBA,ANkBA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,Ac1CA;AT4BA,ADGA,AENA,AHSA,AKfA,AQxBA,ANkBA,AHSA,AIZA,AT2BA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AavCA,ANkBA,AHSA,ALeA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AavCA,ANkBA,AHSA,ALeA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,Ae7CA,AFMA,ANkBA,AHSA,ALeA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,Ae7CA,AFMA,ANkBA,AHSA,ALeA,AOrBA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,Ae7CA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AiBnDA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,AHSA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,AHSA,AHSA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA;AT4BA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AOrBA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AOrBA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,ADGA,AFMA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AOrBA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AQxBA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AQxBA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AQxBA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AS3BA,ADGA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,AGTA,AXiCA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AS3BA,ADGA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AS3BA,ADGA,ADGA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AS3BA,ADGA,AENA,AHSA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AoB5DA,ANkBA,AS3BA,ADGA,AENA,AHSA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AS3BA,ADGA,AENA,AHSA;AhBiDA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AS3BA,ADGA,AENA,AHSA,AIZA;ApB6DA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AS3BA,ADGA,AENA,AHSA,AIZA;ApB6DA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AS3BA,ADGA,AENA,AHSA,AIZA;ApB6DA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AS3BA,ADGA,AENA,AHSA,AIZA,ACHA;ArBgEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AQxBA,AENA,AHSA,AIZA,ACHA;ArBgEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AQxBA,AENA,ACHA,ACHA;ArBgEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AQxBA,AENA,ACHA,ACHA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,ACHA,ACHA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,ACHA,ACHA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA;AtBmEA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,ACHA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA;AzB4EA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AGTA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA;A3BkFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,A6BvFA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AFMA,AKfA,AJYA,AGTA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AGTA,AJYA,AGTA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AGTA,AJYA,AGTA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,ACHA,AQxBA,ALeA,AGTA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,AYpCA,AHSA,ALeA,AGTA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,AYpCA,AHSA,ALeA,AGTA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ANkBA,Af6CA,AU9BA,AYpCA,AHSA,ALeA,AGTA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AmCzGA,ArB+DA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,Ac1CA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ADGA,AENA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AsClHA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ACHA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AsClHA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ACHA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AsClHA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,AFMA,ACHA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AxBwEA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AENA,A1B8EA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AENA,A1B8EA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AENA,A1B8EA,AU9BA,AYpCA,ACHA,AJYA,ADGA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AGTA,ADGA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AGTA,ADGA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AENA,Af6CA,AuCrHA,ADGA,AGTA,ADGA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,ADGA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,ADGA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,ADGA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AYpCA,ACHA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ALeA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ALeA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AHSA,AgBhDA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ALeA,AGTA,ACHA,AFMA,A1B8EA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,A5BoFA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,A5BoFA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,A5BoFA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AIZA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AGTA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AKfA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AKfA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AKfA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AMlBA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AMlBA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AMlBA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AHSA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,ADGA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AIZA,APqBA,ACHA,ANkBA,AGTA,ACHA,AOrBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,ANkBA,AGTA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,ANkBA,AGTA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AavCA,ALeA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,AHSA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AQxBA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AQxBA,AENA;A7BwFA,ADGA,AENA,AavCA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AQxBA,AENA;A7BwFA,ADGA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AFMA,APqBA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,A/B6FA,AU9BA,AQxBA,AENA;A9B2FA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,AYpCA,AbuCA,AuCrHA,AOrBA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,AQxBA,AENA;A9B2FA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,AuCrHA,AOrBA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,AQxBA,AENA;A9B2FA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,AuCrHA,AOrBA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,AQxBA,AENA;A9B2FA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,AuCrHA,AOrBA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,A+B7FA,AvBqEA,AENA;A9B2FA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,A+B7FA,AvBqEA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,A+B7FA,AvBqEA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,AxCwHA,AU9BA,AgChGA,ADGA,AvBqEA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,A9B0FA,AgChGA,ADGA,AvBqEA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,A9B0FA,AgChGA,ADGA,AvBqEA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,A9B0FA,AgChGA,ACHA,AzB2EA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AQxBA,AENA,AHSA,ADGA,AFMA,AS3BA,A9B0FA,AgChGA,ACHA,AzB2EA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AU9BA,AHSA,ADGA,AOrBA,A9B0FA,AgChGA,ACHA,AzB2EA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AZoCA,AU9BA,AHSA,ADGA,AOrBA,A9B0FA,AkCtGA,AFMA,ACHA,AzB2EA;A5BqFA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AMlBA,AT2BA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A8C1IA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,ACHA,AHSA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA,ACHA;ArDgKA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AvBqEA,AkCtGA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,ADGA,AWjCA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AHSA,AT2BA,AENA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AHSA,APqBA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AFMA,AHSA,AU9BA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AHSA,ACHA,AS3BA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AFMA,AS3BA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AOrBA,AKfA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AV8BA,ARwBA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,Ad0CA,AYpCA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA,AFMA;ApD6JA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA,AFMA;AtDmKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,Ae7CA,AlBsDA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA,A4DpLA;AxDyKA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AHSA,ADGA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA,AJYA;AIXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = void 0;\nconst core_1 = require(\"./core\");\nconst draft7_1 = require(\"./vocabularies/draft7\");\nconst discriminator_1 = require(\"./vocabularies/discriminator\");\nconst draft7MetaSchema = require(\"./refs/json-schema-draft-07.json\");\nconst META_SUPPORT_DATA = [\"/properties\"];\nconst META_SCHEMA_ID = \"http://json-schema.org/draft-07/schema\";\nclass Ajv extends core_1.default {\n    _addVocabularies() {\n        super._addVocabularies();\n        draft7_1.default.forEach((v) => this.addVocabulary(v));\n        if (this.opts.discriminator)\n            this.addKeyword(discriminator_1.default);\n    }\n    _addDefaultMetaSchema() {\n        super._addDefaultMetaSchema();\n        if (!this.opts.meta)\n            return;\n        const metaSchema = this.opts.$data\n            ? this.$dataMetaSchema(draft7MetaSchema, META_SUPPORT_DATA)\n            : draft7MetaSchema;\n        this.addMetaSchema(metaSchema, META_SCHEMA_ID, false);\n        this.refs[\"http://json-schema.org/schema\"] = META_SCHEMA_ID;\n    }\n    defaultMeta() {\n        return (this.opts.defaultMeta =\n            super.defaultMeta() || (this.getSchema(META_SCHEMA_ID) ? META_SCHEMA_ID : undefined));\n    }\n}\nmodule.exports = exports = Ajv;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = Ajv;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\n//# sourceMappingURL=ajv.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = void 0;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\nconst validation_error_1 = require(\"./runtime/validation_error\");\nconst ref_error_1 = require(\"./compile/ref_error\");\nconst rules_1 = require(\"./compile/rules\");\nconst compile_1 = require(\"./compile\");\nconst codegen_2 = require(\"./compile/codegen\");\nconst resolve_1 = require(\"./compile/resolve\");\nconst dataType_1 = require(\"./compile/validate/dataType\");\nconst util_1 = require(\"./compile/util\");\nconst $dataRefSchema = require(\"./refs/data.json\");\nconst META_IGNORE_OPTIONS = [\"removeAdditional\", \"useDefaults\", \"coerceTypes\"];\nconst EXT_SCOPE_NAMES = new Set([\n    \"validate\",\n    \"serialize\",\n    \"parse\",\n    \"wrapper\",\n    \"root\",\n    \"schema\",\n    \"keyword\",\n    \"pattern\",\n    \"formats\",\n    \"validate$data\",\n    \"func\",\n    \"obj\",\n    \"Error\",\n]);\nconst removedOptions = {\n    errorDataPath: \"\",\n    format: \"`validateFormats: false` can be used instead.\",\n    nullable: '\"nullable\" keyword is supported by default.',\n    jsonPointers: \"Deprecated jsPropertySyntax can be used instead.\",\n    extendRefs: \"Deprecated ignoreKeywordsWithRef can be used instead.\",\n    missingRefs: \"Pass empty schema with $id that should be ignored to ajv.addSchema.\",\n    processCode: \"Use option `code: {process: (code, schemaEnv: object) => string}`\",\n    sourceCode: \"Use option `code: {source: true}`\",\n    schemaId: \"JSON Schema draft-04 is not supported in Ajv v7/8.\",\n    strictDefaults: \"It is default now, see option `strict`.\",\n    strictKeywords: \"It is default now, see option `strict`.\",\n    uniqueItems: '\"uniqueItems\" keyword is always validated.',\n    unknownFormats: \"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).\",\n    cache: \"Map is used as cache, schema object as key.\",\n    serialize: \"Map is used as cache, schema object as key.\",\n    ajvErrors: \"It is default now.\",\n};\nconst deprecatedOptions = {\n    ignoreKeywordsWithRef: \"\",\n    jsPropertySyntax: \"\",\n    unicode: '\"minLength\"/\"maxLength\" account for unicode characters by default.',\n};\nconst MAX_EXPRESSION = 200;\n// eslint-disable-next-line complexity\nfunction requiredOptions(o) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;\n    const s = o.strict;\n    const _optz = (_a = o.code) === null || _a === void 0 ? void 0 : _a.optimize;\n    const optimize = _optz === true || _optz === undefined ? 1 : _optz || 0;\n    return {\n        strictSchema: (_c = (_b = o.strictSchema) !== null && _b !== void 0 ? _b : s) !== null && _c !== void 0 ? _c : true,\n        strictNumbers: (_e = (_d = o.strictNumbers) !== null && _d !== void 0 ? _d : s) !== null && _e !== void 0 ? _e : true,\n        strictTypes: (_g = (_f = o.strictTypes) !== null && _f !== void 0 ? _f : s) !== null && _g !== void 0 ? _g : \"log\",\n        strictTuples: (_j = (_h = o.strictTuples) !== null && _h !== void 0 ? _h : s) !== null && _j !== void 0 ? _j : \"log\",\n        strictRequired: (_l = (_k = o.strictRequired) !== null && _k !== void 0 ? _k : s) !== null && _l !== void 0 ? _l : false,\n        code: o.code ? { ...o.code, optimize } : { optimize },\n        loopRequired: (_m = o.loopRequired) !== null && _m !== void 0 ? _m : MAX_EXPRESSION,\n        loopEnum: (_o = o.loopEnum) !== null && _o !== void 0 ? _o : MAX_EXPRESSION,\n        meta: (_p = o.meta) !== null && _p !== void 0 ? _p : true,\n        messages: (_q = o.messages) !== null && _q !== void 0 ? _q : true,\n        inlineRefs: (_r = o.inlineRefs) !== null && _r !== void 0 ? _r : true,\n        addUsedSchema: (_s = o.addUsedSchema) !== null && _s !== void 0 ? _s : true,\n        validateSchema: (_t = o.validateSchema) !== null && _t !== void 0 ? _t : true,\n        validateFormats: (_u = o.validateFormats) !== null && _u !== void 0 ? _u : true,\n        unicodeRegExp: (_v = o.unicodeRegExp) !== null && _v !== void 0 ? _v : true,\n    };\n}\nclass Ajv {\n    constructor(opts = {}) {\n        this.schemas = {};\n        this.refs = {};\n        this.formats = {};\n        this._compilations = new Set();\n        this._loading = {};\n        this._cache = new Map();\n        opts = this.opts = { ...opts, ...requiredOptions(opts) };\n        const { es5, lines } = this.opts.code;\n        this.scope = new codegen_2.ValueScope({ scope: {}, prefixes: EXT_SCOPE_NAMES, es5, lines });\n        this.logger = getLogger(opts.logger);\n        const formatOpt = opts.validateFormats;\n        opts.validateFormats = false;\n        this.RULES = rules_1.getRules();\n        checkOptions.call(this, removedOptions, opts, \"NOT SUPPORTED\");\n        checkOptions.call(this, deprecatedOptions, opts, \"DEPRECATED\", \"warn\");\n        this._metaOpts = getMetaSchemaOptions.call(this);\n        if (opts.formats)\n            addInitialFormats.call(this);\n        this._addVocabularies();\n        this._addDefaultMetaSchema();\n        if (opts.keywords)\n            addInitialKeywords.call(this, opts.keywords);\n        if (typeof opts.meta == \"object\")\n            this.addMetaSchema(opts.meta);\n        addInitialSchemas.call(this);\n        opts.validateFormats = formatOpt;\n    }\n    _addVocabularies() {\n        this.addKeyword(\"$async\");\n    }\n    _addDefaultMetaSchema() {\n        const { $data, meta } = this.opts;\n        if (meta && $data)\n            this.addMetaSchema($dataRefSchema, $dataRefSchema.$id, false);\n    }\n    defaultMeta() {\n        const { meta } = this.opts;\n        return (this.opts.defaultMeta = typeof meta == \"object\" ? meta.$id || meta : undefined);\n    }\n    validate(schemaKeyRef, // key, ref or schema object\n    data // to be validated\n    ) {\n        let v;\n        if (typeof schemaKeyRef == \"string\") {\n            v = this.getSchema(schemaKeyRef);\n            if (!v)\n                throw new Error(`no schema with key or ref \"${schemaKeyRef}\"`);\n        }\n        else {\n            v = this.compile(schemaKeyRef);\n        }\n        const valid = v(data);\n        if (!(\"$async\" in v))\n            this.errors = v.errors;\n        return valid;\n    }\n    compile(schema, _meta) {\n        const sch = this._addSchema(schema, _meta);\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    compileAsync(schema, meta) {\n        if (typeof this.opts.loadSchema != \"function\") {\n            throw new Error(\"options.loadSchema should be a function\");\n        }\n        const { loadSchema } = this.opts;\n        return runCompileAsync.call(this, schema, meta);\n        async function runCompileAsync(_schema, _meta) {\n            await loadMetaSchema.call(this, _schema.$schema);\n            const sch = this._addSchema(_schema, _meta);\n            return sch.validate || _compileAsync.call(this, sch);\n        }\n        async function loadMetaSchema($ref) {\n            if ($ref && !this.getSchema($ref)) {\n                await runCompileAsync.call(this, { $ref }, true);\n            }\n        }\n        async function _compileAsync(sch) {\n            try {\n                return this._compileSchemaEnv(sch);\n            }\n            catch (e) {\n                if (!(e instanceof ref_error_1.default))\n                    throw e;\n                checkLoaded.call(this, e);\n                await loadMissingSchema.call(this, e.missingSchema);\n                return _compileAsync.call(this, sch);\n            }\n        }\n        function checkLoaded({ missingSchema: ref, missingRef }) {\n            if (this.refs[ref]) {\n                throw new Error(`AnySchema ${ref} is loaded but ${missingRef} cannot be resolved`);\n            }\n        }\n        async function loadMissingSchema(ref) {\n            const _schema = await _loadSchema.call(this, ref);\n            if (!this.refs[ref])\n                await loadMetaSchema.call(this, _schema.$schema);\n            if (!this.refs[ref])\n                this.addSchema(_schema, ref, meta);\n        }\n        async function _loadSchema(ref) {\n            const p = this._loading[ref];\n            if (p)\n                return p;\n            try {\n                return await (this._loading[ref] = loadSchema(ref));\n            }\n            finally {\n                delete this._loading[ref];\n            }\n        }\n    }\n    // Adds schema to the instance\n    addSchema(schema, // If array is passed, `key` will be ignored\n    key, // Optional schema key. Can be passed to `validate` method instead of schema object or id/ref. One schema per instance can have empty `id` and `key`.\n    _meta, // true if schema is a meta-schema. Used internally, addMetaSchema should be used instead.\n    _validateSchema = this.opts.validateSchema // false to skip schema validation. Used internally, option validateSchema should be used instead.\n    ) {\n        if (Array.isArray(schema)) {\n            for (const sch of schema)\n                this.addSchema(sch, undefined, _meta, _validateSchema);\n            return this;\n        }\n        let id;\n        if (typeof schema === \"object\") {\n            id = schema.$id;\n            if (id !== undefined && typeof id != \"string\")\n                throw new Error(\"schema $id must be string\");\n        }\n        key = resolve_1.normalizeId(key || id);\n        this._checkUnique(key);\n        this.schemas[key] = this._addSchema(schema, _meta, key, _validateSchema, true);\n        return this;\n    }\n    // Add schema that will be used to validate other schemas\n    // options in META_IGNORE_OPTIONS are alway set to false\n    addMetaSchema(schema, key, // schema key\n    _validateSchema = this.opts.validateSchema // false to skip schema validation, can be used to override validateSchema option for meta-schema\n    ) {\n        this.addSchema(schema, key, true, _validateSchema);\n        return this;\n    }\n    //  Validate schema against its meta-schema\n    validateSchema(schema, throwOrLogError) {\n        if (typeof schema == \"boolean\")\n            return true;\n        let $schema;\n        $schema = schema.$schema;\n        if ($schema !== undefined && typeof $schema != \"string\") {\n            throw new Error(\"$schema must be a string\");\n        }\n        $schema = $schema || this.opts.defaultMeta || this.defaultMeta();\n        if (!$schema) {\n            this.logger.warn(\"meta-schema not available\");\n            this.errors = null;\n            return true;\n        }\n        const valid = this.validate($schema, schema);\n        if (!valid && throwOrLogError) {\n            const message = \"schema is invalid: \" + this.errorsText();\n            if (this.opts.validateSchema === \"log\")\n                this.logger.error(message);\n            else\n                throw new Error(message);\n        }\n        return valid;\n    }\n    // Get compiled schema by `key` or `ref`.\n    // (`key` that was passed to `addSchema` or full schema reference - `schema.$id` or resolved id)\n    getSchema(keyRef) {\n        let sch;\n        while (typeof (sch = getSchEnv.call(this, keyRef)) == \"string\")\n            keyRef = sch;\n        if (sch === undefined) {\n            const root = new compile_1.SchemaEnv({ schema: {} });\n            sch = compile_1.resolveSchema.call(this, root, keyRef);\n            if (!sch)\n                return;\n            this.refs[keyRef] = sch;\n        }\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    // Remove cached schema(s).\n    // If no parameter is passed all schemas but meta-schemas are removed.\n    // If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.\n    // Even if schema is referenced by other schemas it still can be removed as other schemas have local references.\n    removeSchema(schemaKeyRef) {\n        if (schemaKeyRef instanceof RegExp) {\n            this._removeAllSchemas(this.schemas, schemaKeyRef);\n            this._removeAllSchemas(this.refs, schemaKeyRef);\n            return this;\n        }\n        switch (typeof schemaKeyRef) {\n            case \"undefined\":\n                this._removeAllSchemas(this.schemas);\n                this._removeAllSchemas(this.refs);\n                this._cache.clear();\n                return this;\n            case \"string\": {\n                const sch = getSchEnv.call(this, schemaKeyRef);\n                if (typeof sch == \"object\")\n                    this._cache.delete(sch.schema);\n                delete this.schemas[schemaKeyRef];\n                delete this.refs[schemaKeyRef];\n                return this;\n            }\n            case \"object\": {\n                const cacheKey = schemaKeyRef;\n                this._cache.delete(cacheKey);\n                let id = schemaKeyRef.$id;\n                if (id) {\n                    id = resolve_1.normalizeId(id);\n                    delete this.schemas[id];\n                    delete this.refs[id];\n                }\n                return this;\n            }\n            default:\n                throw new Error(\"ajv.removeSchema: invalid parameter\");\n        }\n    }\n    // add \"vocabulary\" - a collection of keywords\n    addVocabulary(definitions) {\n        for (const def of definitions)\n            this.addKeyword(def);\n        return this;\n    }\n    addKeyword(kwdOrDef, def // deprecated\n    ) {\n        let keyword;\n        if (typeof kwdOrDef == \"string\") {\n            keyword = kwdOrDef;\n            if (typeof def == \"object\") {\n                this.logger.warn(\"these parameters are deprecated, see docs for addKeyword\");\n                def.keyword = keyword;\n            }\n        }\n        else if (typeof kwdOrDef == \"object\" && def === undefined) {\n            def = kwdOrDef;\n            keyword = def.keyword;\n            if (Array.isArray(keyword) && !keyword.length) {\n                throw new Error(\"addKeywords: keyword must be string or non-empty array\");\n            }\n        }\n        else {\n            throw new Error(\"invalid addKeywords parameters\");\n        }\n        checkKeyword.call(this, keyword, def);\n        if (!def) {\n            util_1.eachItem(keyword, (kwd) => addRule.call(this, kwd));\n            return this;\n        }\n        keywordMetaschema.call(this, def);\n        const definition = {\n            ...def,\n            type: dataType_1.getJSONTypes(def.type),\n            schemaType: dataType_1.getJSONTypes(def.schemaType),\n        };\n        util_1.eachItem(keyword, definition.type.length === 0\n            ? (k) => addRule.call(this, k, definition)\n            : (k) => definition.type.forEach((t) => addRule.call(this, k, definition, t)));\n        return this;\n    }\n    getKeyword(keyword) {\n        const rule = this.RULES.all[keyword];\n        return typeof rule == \"object\" ? rule.definition : !!rule;\n    }\n    // Remove keyword\n    removeKeyword(keyword) {\n        // TODO return type should be Ajv\n        const { RULES } = this;\n        delete RULES.keywords[keyword];\n        delete RULES.all[keyword];\n        for (const group of RULES.rules) {\n            const i = group.rules.findIndex((rule) => rule.keyword === keyword);\n            if (i >= 0)\n                group.rules.splice(i, 1);\n        }\n        return this;\n    }\n    // Add format\n    addFormat(name, format) {\n        if (typeof format == \"string\")\n            format = new RegExp(format);\n        this.formats[name] = format;\n        return this;\n    }\n    errorsText(errors = this.errors, // optional array of validation errors\n    { separator = \", \", dataVar = \"data\" } = {} // optional options with properties `separator` and `dataVar`\n    ) {\n        if (!errors || errors.length === 0)\n            return \"No errors\";\n        return errors\n            .map((e) => `${dataVar}${e.instancePath} ${e.message}`)\n            .reduce((text, msg) => text + separator + msg);\n    }\n    $dataMetaSchema(metaSchema, keywordsJsonPointers) {\n        const rules = this.RULES.all;\n        metaSchema = JSON.parse(JSON.stringify(metaSchema));\n        for (const jsonPointer of keywordsJsonPointers) {\n            const segments = jsonPointer.split(\"/\").slice(1); // first segment is an empty string\n            let keywords = metaSchema;\n            for (const seg of segments)\n                keywords = keywords[seg];\n            for (const key in rules) {\n                const rule = rules[key];\n                if (typeof rule != \"object\")\n                    continue;\n                const { $data } = rule.definition;\n                const schema = keywords[key];\n                if ($data && schema)\n                    keywords[key] = schemaOrData(schema);\n            }\n        }\n        return metaSchema;\n    }\n    _removeAllSchemas(schemas, regex) {\n        for (const keyRef in schemas) {\n            const sch = schemas[keyRef];\n            if (!regex || regex.test(keyRef)) {\n                if (typeof sch == \"string\") {\n                    delete schemas[keyRef];\n                }\n                else if (sch && !sch.meta) {\n                    this._cache.delete(sch.schema);\n                    delete schemas[keyRef];\n                }\n            }\n        }\n    }\n    _addSchema(schema, meta, baseId, validateSchema = this.opts.validateSchema, addSchema = this.opts.addUsedSchema) {\n        let id;\n        if (typeof schema == \"object\") {\n            id = schema.$id;\n        }\n        else {\n            if (this.opts.jtd)\n                throw new Error(\"schema must be object\");\n            else if (typeof schema != \"boolean\")\n                throw new Error(\"schema must be object or boolean\");\n        }\n        let sch = this._cache.get(schema);\n        if (sch !== undefined)\n            return sch;\n        const localRefs = resolve_1.getSchemaRefs.call(this, schema);\n        baseId = resolve_1.normalizeId(id || baseId);\n        sch = new compile_1.SchemaEnv({ schema, meta, baseId, localRefs });\n        this._cache.set(sch.schema, sch);\n        if (addSchema && !baseId.startsWith(\"#\")) {\n            // TODO atm it is allowed to overwrite schemas without id (instead of not adding them)\n            if (baseId)\n                this._checkUnique(baseId);\n            this.refs[baseId] = sch;\n        }\n        if (validateSchema)\n            this.validateSchema(schema, true);\n        return sch;\n    }\n    _checkUnique(id) {\n        if (this.schemas[id] || this.refs[id]) {\n            throw new Error(`schema with key or id \"${id}\" already exists`);\n        }\n    }\n    _compileSchemaEnv(sch) {\n        if (sch.meta)\n            this._compileMetaSchema(sch);\n        else\n            compile_1.compileSchema.call(this, sch);\n        /* istanbul ignore if */\n        if (!sch.validate)\n            throw new Error(\"ajv implementation error\");\n        return sch.validate;\n    }\n    _compileMetaSchema(sch) {\n        const currentOpts = this.opts;\n        this.opts = this._metaOpts;\n        try {\n            compile_1.compileSchema.call(this, sch);\n        }\n        finally {\n            this.opts = currentOpts;\n        }\n    }\n}\nexports.default = Ajv;\nAjv.ValidationError = validation_error_1.default;\nAjv.MissingRefError = ref_error_1.default;\nfunction checkOptions(checkOpts, options, msg, log = \"error\") {\n    for (const key in checkOpts) {\n        const opt = key;\n        if (opt in options)\n            this.logger[log](`${msg}: option ${key}. ${checkOpts[opt]}`);\n    }\n}\nfunction getSchEnv(keyRef) {\n    keyRef = resolve_1.normalizeId(keyRef); // TODO tests fail without this line\n    return this.schemas[keyRef] || this.refs[keyRef];\n}\nfunction addInitialSchemas() {\n    const optsSchemas = this.opts.schemas;\n    if (!optsSchemas)\n        return;\n    if (Array.isArray(optsSchemas))\n        this.addSchema(optsSchemas);\n    else\n        for (const key in optsSchemas)\n            this.addSchema(optsSchemas[key], key);\n}\nfunction addInitialFormats() {\n    for (const name in this.opts.formats) {\n        const format = this.opts.formats[name];\n        if (format)\n            this.addFormat(name, format);\n    }\n}\nfunction addInitialKeywords(defs) {\n    if (Array.isArray(defs)) {\n        this.addVocabulary(defs);\n        return;\n    }\n    this.logger.warn(\"keywords option as map is deprecated, pass array\");\n    for (const keyword in defs) {\n        const def = defs[keyword];\n        if (!def.keyword)\n            def.keyword = keyword;\n        this.addKeyword(def);\n    }\n}\nfunction getMetaSchemaOptions() {\n    const metaOpts = { ...this.opts };\n    for (const opt of META_IGNORE_OPTIONS)\n        delete metaOpts[opt];\n    return metaOpts;\n}\nconst noLogs = { log() { }, warn() { }, error() { } };\nfunction getLogger(logger) {\n    if (logger === false)\n        return noLogs;\n    if (logger === undefined)\n        return console;\n    if (logger.log && logger.warn && logger.error)\n        return logger;\n    throw new Error(\"logger must implement log, warn and error methods\");\n}\nconst KEYWORD_NAME = /^[a-z_$][a-z0-9_$:-]*$/i;\nfunction checkKeyword(keyword, def) {\n    const { RULES } = this;\n    util_1.eachItem(keyword, (kwd) => {\n        if (RULES.keywords[kwd])\n            throw new Error(`Keyword ${kwd} is already defined`);\n        if (!KEYWORD_NAME.test(kwd))\n            throw new Error(`Keyword ${kwd} has invalid name`);\n    });\n    if (!def)\n        return;\n    if (def.$data && !(\"code\" in def || \"validate\" in def)) {\n        throw new Error('$data keyword must have \"code\" or \"validate\" function');\n    }\n}\nfunction addRule(keyword, definition, dataType) {\n    var _a;\n    const post = definition === null || definition === void 0 ? void 0 : definition.post;\n    if (dataType && post)\n        throw new Error('keyword with \"post\" flag cannot have \"type\"');\n    const { RULES } = this;\n    let ruleGroup = post ? RULES.post : RULES.rules.find(({ type: t }) => t === dataType);\n    if (!ruleGroup) {\n        ruleGroup = { type: dataType, rules: [] };\n        RULES.rules.push(ruleGroup);\n    }\n    RULES.keywords[keyword] = true;\n    if (!definition)\n        return;\n    const rule = {\n        keyword,\n        definition: {\n            ...definition,\n            type: dataType_1.getJSONTypes(definition.type),\n            schemaType: dataType_1.getJSONTypes(definition.schemaType),\n        },\n    };\n    if (definition.before)\n        addBeforeRule.call(this, ruleGroup, rule, definition.before);\n    else\n        ruleGroup.rules.push(rule);\n    RULES.all[keyword] = rule;\n    (_a = definition.implements) === null || _a === void 0 ? void 0 : _a.forEach((kwd) => this.addKeyword(kwd));\n}\nfunction addBeforeRule(ruleGroup, rule, before) {\n    const i = ruleGroup.rules.findIndex((_rule) => _rule.keyword === before);\n    if (i >= 0) {\n        ruleGroup.rules.splice(i, 0, rule);\n    }\n    else {\n        ruleGroup.rules.push(rule);\n        this.logger.warn(`rule ${before} is not defined`);\n    }\n}\nfunction keywordMetaschema(def) {\n    let { metaSchema } = def;\n    if (metaSchema === undefined)\n        return;\n    if (def.$data && this.opts.$data)\n        metaSchema = schemaOrData(metaSchema);\n    def.validateSchema = this.compile(metaSchema, true);\n}\nconst $dataRef = {\n    $ref: \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n};\nfunction schemaOrData(schema) {\n    return { anyOf: [schema, $dataRef] };\n}\n//# sourceMappingURL=core.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getData = exports.KeywordCxt = exports.validateFunctionCode = void 0;\nconst boolSchema_1 = require(\"./boolSchema\");\nconst dataType_1 = require(\"./dataType\");\nconst applicability_1 = require(\"./applicability\");\nconst dataType_2 = require(\"./dataType\");\nconst defaults_1 = require(\"./defaults\");\nconst keyword_1 = require(\"./keyword\");\nconst subschema_1 = require(\"./subschema\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst resolve_1 = require(\"../resolve\");\nconst util_1 = require(\"../util\");\nconst errors_1 = require(\"../errors\");\n// schema compilation - generates validation function, subschemaCode (below) is used for subschemas\nfunction validateFunctionCode(it) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            topSchemaObjCode(it);\n            return;\n        }\n    }\n    validateFunction(it, () => boolSchema_1.topBoolOrEmptySchema(it));\n}\nexports.validateFunctionCode = validateFunctionCode;\nfunction validateFunction({ gen, validateName, schema, schemaEnv, opts }, body) {\n    if (opts.code.es5) {\n        gen.func(validateName, codegen_1._ `${names_1.default.data}, ${names_1.default.valCxt}`, schemaEnv.$async, () => {\n            gen.code(codegen_1._ `\"use strict\"; ${funcSourceUrl(schema, opts)}`);\n            destructureValCxtES5(gen, opts);\n            gen.code(body);\n        });\n    }\n    else {\n        gen.func(validateName, codegen_1._ `${names_1.default.data}, ${destructureValCxt(opts)}`, schemaEnv.$async, () => gen.code(funcSourceUrl(schema, opts)).code(body));\n    }\n}\nfunction destructureValCxt(opts) {\n    return codegen_1._ `{${names_1.default.instancePath}=\"\", ${names_1.default.parentData}, ${names_1.default.parentDataProperty}, ${names_1.default.rootData}=${names_1.default.data}${opts.dynamicRef ? codegen_1._ `, ${names_1.default.dynamicAnchors}={}` : codegen_1.nil}}={}`;\n}\nfunction destructureValCxtES5(gen, opts) {\n    gen.if(names_1.default.valCxt, () => {\n        gen.var(names_1.default.instancePath, codegen_1._ `${names_1.default.valCxt}.${names_1.default.instancePath}`);\n        gen.var(names_1.default.parentData, codegen_1._ `${names_1.default.valCxt}.${names_1.default.parentData}`);\n        gen.var(names_1.default.parentDataProperty, codegen_1._ `${names_1.default.valCxt}.${names_1.default.parentDataProperty}`);\n        gen.var(names_1.default.rootData, codegen_1._ `${names_1.default.valCxt}.${names_1.default.rootData}`);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, codegen_1._ `${names_1.default.valCxt}.${names_1.default.dynamicAnchors}`);\n    }, () => {\n        gen.var(names_1.default.instancePath, codegen_1._ `\"\"`);\n        gen.var(names_1.default.parentData, codegen_1._ `undefined`);\n        gen.var(names_1.default.parentDataProperty, codegen_1._ `undefined`);\n        gen.var(names_1.default.rootData, names_1.default.data);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, codegen_1._ `{}`);\n    });\n}\nfunction topSchemaObjCode(it) {\n    const { schema, opts, gen } = it;\n    validateFunction(it, () => {\n        if (opts.$comment && schema.$comment)\n            commentKeyword(it);\n        checkNoDefault(it);\n        gen.let(names_1.default.vErrors, null);\n        gen.let(names_1.default.errors, 0);\n        if (opts.unevaluated)\n            resetEvaluated(it);\n        typeAndKeywords(it);\n        returnResults(it);\n    });\n    return;\n}\nfunction resetEvaluated(it) {\n    // TODO maybe some hook to execute it in the end to check whether props/items are Name, as in assignEvaluated\n    const { gen, validateName } = it;\n    it.evaluated = gen.const(\"evaluated\", codegen_1._ `${validateName}.evaluated`);\n    gen.if(codegen_1._ `${it.evaluated}.dynamicProps`, () => gen.assign(codegen_1._ `${it.evaluated}.props`, codegen_1._ `undefined`));\n    gen.if(codegen_1._ `${it.evaluated}.dynamicItems`, () => gen.assign(codegen_1._ `${it.evaluated}.items`, codegen_1._ `undefined`));\n}\nfunction funcSourceUrl(schema, opts) {\n    return typeof schema == \"object\" && schema.$id && (opts.code.source || opts.code.process)\n        ? codegen_1._ `/*# sourceURL=${schema.$id} */`\n        : codegen_1.nil;\n}\n// schema compilation - this function is used recursively to generate code for sub-schemas\nfunction subschemaCode(it, valid) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            subSchemaObjCode(it, valid);\n            return;\n        }\n    }\n    boolSchema_1.boolOrEmptySchema(it, valid);\n}\nfunction schemaCxtHasRules({ schema, self }) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (self.RULES.all[key])\n            return true;\n    return false;\n}\nfunction isSchemaObj(it) {\n    return typeof it.schema != \"boolean\";\n}\nfunction subSchemaObjCode(it, valid) {\n    const { schema, gen, opts } = it;\n    if (opts.$comment && schema.$comment)\n        commentKeyword(it);\n    updateContext(it);\n    checkAsyncSchema(it);\n    const errsCount = gen.const(\"_errs\", names_1.default.errors);\n    typeAndKeywords(it, errsCount);\n    // TODO var\n    gen.var(valid, codegen_1._ `${errsCount} === ${names_1.default.errors}`);\n}\nfunction checkKeywords(it) {\n    util_1.checkUnknownRules(it);\n    checkRefsAndKeywords(it);\n}\nfunction typeAndKeywords(it, errsCount) {\n    if (it.opts.jtd)\n        return schemaKeywords(it, [], false, errsCount);\n    const types = dataType_1.getSchemaTypes(it.schema);\n    const checkedTypes = dataType_1.coerceAndCheckDataType(it, types);\n    schemaKeywords(it, types, !checkedTypes, errsCount);\n}\nfunction checkRefsAndKeywords(it) {\n    const { schema, errSchemaPath, opts, self } = it;\n    if (schema.$ref && opts.ignoreKeywordsWithRef && util_1.schemaHasRulesButRef(schema, self.RULES)) {\n        self.logger.warn(`$ref: keywords ignored in schema at path \"${errSchemaPath}\"`);\n    }\n}\nfunction checkNoDefault(it) {\n    const { schema, opts } = it;\n    if (schema.default !== undefined && opts.useDefaults && opts.strictSchema) {\n        util_1.checkStrictMode(it, \"default is ignored in the schema root\");\n    }\n}\nfunction updateContext(it) {\n    if (it.schema.$id)\n        it.baseId = resolve_1.resolveUrl(it.baseId, it.schema.$id);\n}\nfunction checkAsyncSchema(it) {\n    if (it.schema.$async && !it.schemaEnv.$async)\n        throw new Error(\"async schema in sync schema\");\n}\nfunction commentKeyword({ gen, schemaEnv, schema, errSchemaPath, opts }) {\n    const msg = schema.$comment;\n    if (opts.$comment === true) {\n        gen.code(codegen_1._ `${names_1.default.self}.logger.log(${msg})`);\n    }\n    else if (typeof opts.$comment == \"function\") {\n        const schemaPath = codegen_1.str `${errSchemaPath}/$comment`;\n        const rootName = gen.scopeValue(\"root\", { ref: schemaEnv.root });\n        gen.code(codegen_1._ `${names_1.default.self}.opts.$comment(${msg}, ${schemaPath}, ${rootName}.schema)`);\n    }\n}\nfunction returnResults(it) {\n    const { gen, schemaEnv, validateName, ValidationError, opts } = it;\n    if (schemaEnv.$async) {\n        // TODO assign unevaluated\n        gen.if(codegen_1._ `${names_1.default.errors} === 0`, () => gen.return(names_1.default.data), () => gen.throw(codegen_1._ `new ${ValidationError}(${names_1.default.vErrors})`));\n    }\n    else {\n        gen.assign(codegen_1._ `${validateName}.errors`, names_1.default.vErrors);\n        if (opts.unevaluated)\n            assignEvaluated(it);\n        gen.return(codegen_1._ `${names_1.default.errors} === 0`);\n    }\n}\nfunction assignEvaluated({ gen, evaluated, props, items }) {\n    if (props instanceof codegen_1.Name)\n        gen.assign(codegen_1._ `${evaluated}.props`, props);\n    if (items instanceof codegen_1.Name)\n        gen.assign(codegen_1._ `${evaluated}.items`, items);\n}\nfunction schemaKeywords(it, types, typeErrors, errsCount) {\n    const { gen, schema, data, allErrors, opts, self } = it;\n    const { RULES } = self;\n    if (schema.$ref && (opts.ignoreKeywordsWithRef || !util_1.schemaHasRulesButRef(schema, RULES))) {\n        gen.block(() => keywordCode(it, \"$ref\", RULES.all.$ref.definition)); // TODO typecast\n        return;\n    }\n    if (!opts.jtd)\n        checkStrictTypes(it, types);\n    gen.block(() => {\n        for (const group of RULES.rules)\n            groupKeywords(group);\n        groupKeywords(RULES.post);\n    });\n    function groupKeywords(group) {\n        if (!applicability_1.shouldUseGroup(schema, group))\n            return;\n        if (group.type) {\n            gen.if(dataType_2.checkDataType(group.type, data, opts.strictNumbers));\n            iterateKeywords(it, group);\n            if (types.length === 1 && types[0] === group.type && typeErrors) {\n                gen.else();\n                dataType_2.reportTypeError(it);\n            }\n            gen.endIf();\n        }\n        else {\n            iterateKeywords(it, group);\n        }\n        // TODO make it \"ok\" call?\n        if (!allErrors)\n            gen.if(codegen_1._ `${names_1.default.errors} === ${errsCount || 0}`);\n    }\n}\nfunction iterateKeywords(it, group) {\n    const { gen, schema, opts: { useDefaults }, } = it;\n    if (useDefaults)\n        defaults_1.assignDefaults(it, group.type);\n    gen.block(() => {\n        for (const rule of group.rules) {\n            if (applicability_1.shouldUseRule(schema, rule)) {\n                keywordCode(it, rule.keyword, rule.definition, group.type);\n            }\n        }\n    });\n}\nfunction checkStrictTypes(it, types) {\n    if (it.schemaEnv.meta || !it.opts.strictTypes)\n        return;\n    checkContextTypes(it, types);\n    if (!it.opts.allowUnionTypes)\n        checkMultipleTypes(it, types);\n    checkKeywordTypes(it, it.dataTypes);\n}\nfunction checkContextTypes(it, types) {\n    if (!types.length)\n        return;\n    if (!it.dataTypes.length) {\n        it.dataTypes = types;\n        return;\n    }\n    types.forEach((t) => {\n        if (!includesType(it.dataTypes, t)) {\n            strictTypesError(it, `type \"${t}\" not allowed by context \"${it.dataTypes.join(\",\")}\"`);\n        }\n    });\n    it.dataTypes = it.dataTypes.filter((t) => includesType(types, t));\n}\nfunction checkMultipleTypes(it, ts) {\n    if (ts.length > 1 && !(ts.length === 2 && ts.includes(\"null\"))) {\n        strictTypesError(it, \"use allowUnionTypes to allow union type keyword\");\n    }\n}\nfunction checkKeywordTypes(it, ts) {\n    const rules = it.self.RULES.all;\n    for (const keyword in rules) {\n        const rule = rules[keyword];\n        if (typeof rule == \"object\" && applicability_1.shouldUseRule(it.schema, rule)) {\n            const { type } = rule.definition;\n            if (type.length && !type.some((t) => hasApplicableType(ts, t))) {\n                strictTypesError(it, `missing type \"${type.join(\",\")}\" for keyword \"${keyword}\"`);\n            }\n        }\n    }\n}\nfunction hasApplicableType(schTs, kwdT) {\n    return schTs.includes(kwdT) || (kwdT === \"number\" && schTs.includes(\"integer\"));\n}\nfunction includesType(ts, t) {\n    return ts.includes(t) || (t === \"integer\" && ts.includes(\"number\"));\n}\nfunction strictTypesError(it, msg) {\n    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n    msg += ` at \"${schemaPath}\" (strictTypes)`;\n    util_1.checkStrictMode(it, msg, it.opts.strictTypes);\n}\nclass KeywordCxt {\n    constructor(it, def, keyword) {\n        keyword_1.validateKeywordUsage(it, def, keyword);\n        this.gen = it.gen;\n        this.allErrors = it.allErrors;\n        this.keyword = keyword;\n        this.data = it.data;\n        this.schema = it.schema[keyword];\n        this.$data = def.$data && it.opts.$data && this.schema && this.schema.$data;\n        this.schemaValue = util_1.schemaRefOrVal(it, this.schema, keyword, this.$data);\n        this.schemaType = def.schemaType;\n        this.parentSchema = it.schema;\n        this.params = {};\n        this.it = it;\n        this.def = def;\n        if (this.$data) {\n            this.schemaCode = it.gen.const(\"vSchema\", getData(this.$data, it));\n        }\n        else {\n            this.schemaCode = this.schemaValue;\n            if (!keyword_1.validSchemaType(this.schema, def.schemaType, def.allowUndefined)) {\n                throw new Error(`${keyword} value must be ${JSON.stringify(def.schemaType)}`);\n            }\n        }\n        if (\"code\" in def ? def.trackErrors : def.errors !== false) {\n            this.errsCount = it.gen.const(\"_errs\", names_1.default.errors);\n        }\n    }\n    result(condition, successAction, failAction) {\n        this.gen.if(codegen_1.not(condition));\n        if (failAction)\n            failAction();\n        else\n            this.error();\n        if (successAction) {\n            this.gen.else();\n            successAction();\n            if (this.allErrors)\n                this.gen.endIf();\n        }\n        else {\n            if (this.allErrors)\n                this.gen.endIf();\n            else\n                this.gen.else();\n        }\n    }\n    pass(condition, failAction) {\n        this.result(condition, undefined, failAction);\n    }\n    fail(condition) {\n        if (condition === undefined) {\n            this.error();\n            if (!this.allErrors)\n                this.gen.if(false); // this branch will be removed by gen.optimize\n            return;\n        }\n        this.gen.if(condition);\n        this.error();\n        if (this.allErrors)\n            this.gen.endIf();\n        else\n            this.gen.else();\n    }\n    fail$data(condition) {\n        if (!this.$data)\n            return this.fail(condition);\n        const { schemaCode } = this;\n        this.fail(codegen_1._ `${schemaCode} !== undefined && (${codegen_1.or(this.invalid$data(), condition)})`);\n    }\n    error(append, errorParams, errorPaths) {\n        if (errorParams) {\n            this.setParams(errorParams);\n            this._error(append, errorPaths);\n            this.setParams({});\n            return;\n        }\n        this._error(append, errorPaths);\n    }\n    _error(append, errorPaths) {\n        ;\n        (append ? errors_1.reportExtraError : errors_1.reportError)(this, this.def.error, errorPaths);\n    }\n    $dataError() {\n        errors_1.reportError(this, this.def.$dataError || errors_1.keyword$DataError);\n    }\n    reset() {\n        if (this.errsCount === undefined)\n            throw new Error('add \"trackErrors\" to keyword definition');\n        errors_1.resetErrorsCount(this.gen, this.errsCount);\n    }\n    ok(cond) {\n        if (!this.allErrors)\n            this.gen.if(cond);\n    }\n    setParams(obj, assign) {\n        if (assign)\n            Object.assign(this.params, obj);\n        else\n            this.params = obj;\n    }\n    block$data(valid, codeBlock, $dataValid = codegen_1.nil) {\n        this.gen.block(() => {\n            this.check$data(valid, $dataValid);\n            codeBlock();\n        });\n    }\n    check$data(valid = codegen_1.nil, $dataValid = codegen_1.nil) {\n        if (!this.$data)\n            return;\n        const { gen, schemaCode, schemaType, def } = this;\n        gen.if(codegen_1.or(codegen_1._ `${schemaCode} === undefined`, $dataValid));\n        if (valid !== codegen_1.nil)\n            gen.assign(valid, true);\n        if (schemaType.length || def.validateSchema) {\n            gen.elseIf(this.invalid$data());\n            this.$dataError();\n            if (valid !== codegen_1.nil)\n                gen.assign(valid, false);\n        }\n        gen.else();\n    }\n    invalid$data() {\n        const { gen, schemaCode, schemaType, def, it } = this;\n        return codegen_1.or(wrong$DataType(), invalid$DataSchema());\n        function wrong$DataType() {\n            if (schemaType.length) {\n                /* istanbul ignore if */\n                if (!(schemaCode instanceof codegen_1.Name))\n                    throw new Error(\"ajv implementation error\");\n                const st = Array.isArray(schemaType) ? schemaType : [schemaType];\n                return codegen_1._ `${dataType_2.checkDataTypes(st, schemaCode, it.opts.strictNumbers, dataType_2.DataType.Wrong)}`;\n            }\n            return codegen_1.nil;\n        }\n        function invalid$DataSchema() {\n            if (def.validateSchema) {\n                const validateSchemaRef = gen.scopeValue(\"validate$data\", { ref: def.validateSchema }); // TODO value.code for standalone\n                return codegen_1._ `!${validateSchemaRef}(${schemaCode})`;\n            }\n            return codegen_1.nil;\n        }\n    }\n    subschema(appl, valid) {\n        const subschema = subschema_1.getSubschema(this.it, appl);\n        subschema_1.extendSubschemaData(subschema, this.it, appl);\n        subschema_1.extendSubschemaMode(subschema, appl);\n        const nextContext = { ...this.it, ...subschema, items: undefined, props: undefined };\n        subschemaCode(nextContext, valid);\n        return nextContext;\n    }\n    mergeEvaluated(schemaCxt, toName) {\n        const { it, gen } = this;\n        if (!it.opts.unevaluated)\n            return;\n        if (it.props !== true && schemaCxt.props !== undefined) {\n            it.props = util_1.mergeEvaluated.props(gen, schemaCxt.props, it.props, toName);\n        }\n        if (it.items !== true && schemaCxt.items !== undefined) {\n            it.items = util_1.mergeEvaluated.items(gen, schemaCxt.items, it.items, toName);\n        }\n    }\n    mergeValidEvaluated(schemaCxt, valid) {\n        const { it, gen } = this;\n        if (it.opts.unevaluated && (it.props !== true || it.items !== true)) {\n            gen.if(valid, () => this.mergeEvaluated(schemaCxt, codegen_1.Name));\n            return true;\n        }\n    }\n}\nexports.KeywordCxt = KeywordCxt;\nfunction keywordCode(it, keyword, def, ruleType) {\n    const cxt = new KeywordCxt(it, def, keyword);\n    if (\"code\" in def) {\n        def.code(cxt, ruleType);\n    }\n    else if (cxt.$data && def.validate) {\n        keyword_1.funcKeywordCode(cxt, def);\n    }\n    else if (\"macro\" in def) {\n        keyword_1.macroKeywordCode(cxt, def);\n    }\n    else if (def.compile || def.validate) {\n        keyword_1.funcKeywordCode(cxt, def);\n    }\n}\nconst JSON_POINTER = /^\\/(?:[^~]|~0|~1)*$/;\nconst RELATIVE_JSON_POINTER = /^([0-9]+)(#|\\/(?:[^~]|~0|~1)*)?$/;\nfunction getData($data, { dataLevel, dataNames, dataPathArr }) {\n    let jsonPointer;\n    let data;\n    if ($data === \"\")\n        return names_1.default.rootData;\n    if ($data[0] === \"/\") {\n        if (!JSON_POINTER.test($data))\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        jsonPointer = $data;\n        data = names_1.default.rootData;\n    }\n    else {\n        const matches = RELATIVE_JSON_POINTER.exec($data);\n        if (!matches)\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        const up = +matches[1];\n        jsonPointer = matches[2];\n        if (jsonPointer === \"#\") {\n            if (up >= dataLevel)\n                throw new Error(errorMsg(\"property/index\", up));\n            return dataPathArr[dataLevel - up];\n        }\n        if (up > dataLevel)\n            throw new Error(errorMsg(\"data\", up));\n        data = dataNames[dataLevel - up];\n        if (!jsonPointer)\n            return data;\n    }\n    let expr = data;\n    const segments = jsonPointer.split(\"/\");\n    for (const segment of segments) {\n        if (segment) {\n            data = codegen_1._ `${data}${codegen_1.getProperty(util_1.unescapeJsonPointer(segment))}`;\n            expr = codegen_1._ `${expr} && ${data}`;\n        }\n    }\n    return expr;\n    function errorMsg(pointerType, up) {\n        return `Cannot access ${pointerType} ${up} levels up, current level is ${dataLevel}`;\n    }\n}\nexports.getData = getData;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.boolOrEmptySchema = exports.topBoolOrEmptySchema = void 0;\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst boolError = {\n    message: \"boolean schema is false\",\n};\nfunction topBoolOrEmptySchema(it) {\n    const { gen, schema, validateName } = it;\n    if (schema === false) {\n        falseSchemaError(it, false);\n    }\n    else if (typeof schema == \"object\" && schema.$async === true) {\n        gen.return(names_1.default.data);\n    }\n    else {\n        gen.assign(codegen_1._ `${validateName}.errors`, null);\n        gen.return(true);\n    }\n}\nexports.topBoolOrEmptySchema = topBoolOrEmptySchema;\nfunction boolOrEmptySchema(it, valid) {\n    const { gen, schema } = it;\n    if (schema === false) {\n        gen.var(valid, false); // TODO var\n        falseSchemaError(it);\n    }\n    else {\n        gen.var(valid, true); // TODO var\n    }\n}\nexports.boolOrEmptySchema = boolOrEmptySchema;\nfunction falseSchemaError(it, overrideAllErrors) {\n    const { gen, data } = it;\n    // TODO maybe some other interface should be used for non-keyword validation errors...\n    const cxt = {\n        gen,\n        keyword: \"false schema\",\n        data,\n        schema: false,\n        schemaCode: false,\n        schemaValue: false,\n        params: {},\n        it,\n    };\n    errors_1.reportError(cxt, boolError, undefined, overrideAllErrors);\n}\n//# sourceMappingURL=boolSchema.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendErrors = exports.resetErrorsCount = exports.reportExtraError = exports.reportError = exports.keyword$DataError = exports.keywordError = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst util_1 = require(\"./util\");\nconst names_1 = require(\"./names\");\nexports.keywordError = {\n    message: ({ keyword }) => codegen_1.str `should pass \"${keyword}\" keyword validation`,\n};\nexports.keyword$DataError = {\n    message: ({ keyword, schemaType }) => schemaType\n        ? codegen_1.str `\"${keyword}\" keyword must be ${schemaType} ($data)`\n        : codegen_1.str `\"${keyword}\" keyword is invalid ($data)`,\n};\nfunction reportError(cxt, error = exports.keywordError, errorPaths, overrideAllErrors) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    if (overrideAllErrors !== null && overrideAllErrors !== void 0 ? overrideAllErrors : (compositeRule || allErrors)) {\n        addError(gen, errObj);\n    }\n    else {\n        returnErrors(it, codegen_1._ `[${errObj}]`);\n    }\n}\nexports.reportError = reportError;\nfunction reportExtraError(cxt, error = exports.keywordError, errorPaths) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    addError(gen, errObj);\n    if (!(compositeRule || allErrors)) {\n        returnErrors(it, names_1.default.vErrors);\n    }\n}\nexports.reportExtraError = reportExtraError;\nfunction resetErrorsCount(gen, errsCount) {\n    gen.assign(names_1.default.errors, errsCount);\n    gen.if(codegen_1._ `${names_1.default.vErrors} !== null`, () => gen.if(errsCount, () => gen.assign(codegen_1._ `${names_1.default.vErrors}.length`, errsCount), () => gen.assign(names_1.default.vErrors, null)));\n}\nexports.resetErrorsCount = resetErrorsCount;\nfunction extendErrors({ gen, keyword, schemaValue, data, errsCount, it, }) {\n    /* istanbul ignore if */\n    if (errsCount === undefined)\n        throw new Error(\"ajv implementation error\");\n    const err = gen.name(\"err\");\n    gen.forRange(\"i\", errsCount, names_1.default.errors, (i) => {\n        gen.const(err, codegen_1._ `${names_1.default.vErrors}[${i}]`);\n        gen.if(codegen_1._ `${err}.instancePath === undefined`, () => gen.assign(codegen_1._ `${err}.instancePath`, codegen_1.strConcat(names_1.default.instancePath, it.errorPath)));\n        gen.assign(codegen_1._ `${err}.schemaPath`, codegen_1.str `${it.errSchemaPath}/${keyword}`);\n        if (it.opts.verbose) {\n            gen.assign(codegen_1._ `${err}.schema`, schemaValue);\n            gen.assign(codegen_1._ `${err}.data`, data);\n        }\n    });\n}\nexports.extendErrors = extendErrors;\nfunction addError(gen, errObj) {\n    const err = gen.const(\"err\", errObj);\n    gen.if(codegen_1._ `${names_1.default.vErrors} === null`, () => gen.assign(names_1.default.vErrors, codegen_1._ `[${err}]`), codegen_1._ `${names_1.default.vErrors}.push(${err})`);\n    gen.code(codegen_1._ `${names_1.default.errors}++`);\n}\nfunction returnErrors(it, errs) {\n    const { gen, validateName, schemaEnv } = it;\n    if (schemaEnv.$async) {\n        gen.throw(codegen_1._ `new ${it.ValidationError}(${errs})`);\n    }\n    else {\n        gen.assign(codegen_1._ `${validateName}.errors`, errs);\n        gen.return(false);\n    }\n}\nconst E = {\n    keyword: new codegen_1.Name(\"keyword\"),\n    schemaPath: new codegen_1.Name(\"schemaPath\"),\n    params: new codegen_1.Name(\"params\"),\n    propertyName: new codegen_1.Name(\"propertyName\"),\n    message: new codegen_1.Name(\"message\"),\n    schema: new codegen_1.Name(\"schema\"),\n    parentSchema: new codegen_1.Name(\"parentSchema\"),\n};\nfunction errorObjectCode(cxt, error, errorPaths) {\n    const { createErrors } = cxt.it;\n    if (createErrors === false)\n        return codegen_1._ `{}`;\n    return errorObject(cxt, error, errorPaths);\n}\nfunction errorObject(cxt, error, errorPaths = {}) {\n    const { gen, it } = cxt;\n    const keyValues = [\n        errorInstancePath(it, errorPaths),\n        errorSchemaPath(cxt, errorPaths),\n    ];\n    extraErrorProps(cxt, error, keyValues);\n    return gen.object(...keyValues);\n}\nfunction errorInstancePath({ errorPath }, { instancePath }) {\n    const instPath = instancePath\n        ? codegen_1.str `${errorPath}${util_1.getErrorPath(instancePath, util_1.Type.Str)}`\n        : errorPath;\n    return [names_1.default.instancePath, codegen_1.strConcat(names_1.default.instancePath, instPath)];\n}\nfunction errorSchemaPath({ keyword, it: { errSchemaPath } }, { schemaPath, parentSchema }) {\n    let schPath = parentSchema ? errSchemaPath : codegen_1.str `${errSchemaPath}/${keyword}`;\n    if (schemaPath) {\n        schPath = codegen_1.str `${schPath}${util_1.getErrorPath(schemaPath, util_1.Type.Str)}`;\n    }\n    return [E.schemaPath, schPath];\n}\nfunction extraErrorProps(cxt, { params, message }, keyValues) {\n    const { keyword, data, schemaValue, it } = cxt;\n    const { opts, propertyName, topSchemaRef, schemaPath } = it;\n    keyValues.push([E.keyword, keyword], [E.params, typeof params == \"function\" ? params(cxt) : params || codegen_1._ `{}`]);\n    if (opts.messages) {\n        keyValues.push([E.message, typeof message == \"function\" ? message(cxt) : message]);\n    }\n    if (opts.verbose) {\n        keyValues.push([E.schema, schemaValue], [E.parentSchema, codegen_1._ `${topSchemaRef}${schemaPath}`], [names_1.default.data, data]);\n    }\n    if (propertyName)\n        keyValues.push([E.propertyName, propertyName]);\n}\n//# sourceMappingURL=errors.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.or = exports.and = exports.not = exports.CodeGen = exports.operators = exports.varKinds = exports.ValueScopeName = exports.ValueScope = exports.Scope = exports.Name = exports.regexpCode = exports.stringify = exports.getProperty = exports.nil = exports.strConcat = exports.str = exports._ = void 0;\nconst code_1 = require(\"./code\");\nconst scope_1 = require(\"./scope\");\nvar code_2 = require(\"./code\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return code_2._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return code_2.str; } });\nObject.defineProperty(exports, \"strConcat\", { enumerable: true, get: function () { return code_2.strConcat; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return code_2.nil; } });\nObject.defineProperty(exports, \"getProperty\", { enumerable: true, get: function () { return code_2.getProperty; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return code_2.stringify; } });\nObject.defineProperty(exports, \"regexpCode\", { enumerable: true, get: function () { return code_2.regexpCode; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return code_2.Name; } });\nvar scope_2 = require(\"./scope\");\nObject.defineProperty(exports, \"Scope\", { enumerable: true, get: function () { return scope_2.Scope; } });\nObject.defineProperty(exports, \"ValueScope\", { enumerable: true, get: function () { return scope_2.ValueScope; } });\nObject.defineProperty(exports, \"ValueScopeName\", { enumerable: true, get: function () { return scope_2.ValueScopeName; } });\nObject.defineProperty(exports, \"varKinds\", { enumerable: true, get: function () { return scope_2.varKinds; } });\nexports.operators = {\n    GT: new code_1._Code(\">\"),\n    GTE: new code_1._Code(\">=\"),\n    LT: new code_1._Code(\"<\"),\n    LTE: new code_1._Code(\"<=\"),\n    EQ: new code_1._Code(\"===\"),\n    NEQ: new code_1._Code(\"!==\"),\n    NOT: new code_1._Code(\"!\"),\n    OR: new code_1._Code(\"||\"),\n    AND: new code_1._Code(\"&&\"),\n    ADD: new code_1._Code(\"+\"),\n};\nclass Node {\n    optimizeNodes() {\n        return this;\n    }\n    optimizeNames(_names, _constants) {\n        return this;\n    }\n}\nclass Def extends Node {\n    constructor(varKind, name, rhs) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.rhs = rhs;\n    }\n    render({ es5, _n }) {\n        const varKind = es5 ? scope_1.varKinds.var : this.varKind;\n        const rhs = this.rhs === undefined ? \"\" : ` = ${this.rhs}`;\n        return `${varKind} ${this.name}${rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (!names[this.name.str])\n            return;\n        if (this.rhs)\n            this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        return this.rhs instanceof code_1._CodeOrName ? this.rhs.names : {};\n    }\n}\nclass Assign extends Node {\n    constructor(lhs, rhs, sideEffects) {\n        super();\n        this.lhs = lhs;\n        this.rhs = rhs;\n        this.sideEffects = sideEffects;\n    }\n    render({ _n }) {\n        return `${this.lhs} = ${this.rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (this.lhs instanceof code_1.Name && !names[this.lhs.str] && !this.sideEffects)\n            return;\n        this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        const names = this.lhs instanceof code_1.Name ? {} : { ...this.lhs.names };\n        return addExprNames(names, this.rhs);\n    }\n}\nclass AssignOp extends Assign {\n    constructor(lhs, op, rhs, sideEffects) {\n        super(lhs, rhs, sideEffects);\n        this.op = op;\n    }\n    render({ _n }) {\n        return `${this.lhs} ${this.op}= ${this.rhs};` + _n;\n    }\n}\nclass Label extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        return `${this.label}:` + _n;\n    }\n}\nclass Break extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        const label = this.label ? ` ${this.label}` : \"\";\n        return `break${label};` + _n;\n    }\n}\nclass Throw extends Node {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render({ _n }) {\n        return `throw ${this.error};` + _n;\n    }\n    get names() {\n        return this.error.names;\n    }\n}\nclass AnyCode extends Node {\n    constructor(code) {\n        super();\n        this.code = code;\n    }\n    render({ _n }) {\n        return `${this.code};` + _n;\n    }\n    optimizeNodes() {\n        return `${this.code}` ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        this.code = optimizeExpr(this.code, names, constants);\n        return this;\n    }\n    get names() {\n        return this.code instanceof code_1._CodeOrName ? this.code.names : {};\n    }\n}\nclass ParentNode extends Node {\n    constructor(nodes = []) {\n        super();\n        this.nodes = nodes;\n    }\n    render(opts) {\n        return this.nodes.reduce((code, n) => code + n.render(opts), \"\");\n    }\n    optimizeNodes() {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            const n = nodes[i].optimizeNodes();\n            if (Array.isArray(n))\n                nodes.splice(i, 1, ...n);\n            else if (n)\n                nodes[i] = n;\n            else\n                nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            // iterating backwards improves 1-pass optimization\n            const n = nodes[i];\n            if (n.optimizeNames(names, constants))\n                continue;\n            subtractNames(names, n.names);\n            nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    get names() {\n        return this.nodes.reduce((names, n) => addNames(names, n.names), {});\n    }\n}\nclass BlockNode extends ParentNode {\n    render(opts) {\n        return \"{\" + opts._n + super.render(opts) + \"}\" + opts._n;\n    }\n}\nclass Root extends ParentNode {\n}\nclass Else extends BlockNode {\n}\nElse.kind = \"else\";\nclass If extends BlockNode {\n    constructor(condition, nodes) {\n        super(nodes);\n        this.condition = condition;\n    }\n    render(opts) {\n        let code = `if(${this.condition})` + super.render(opts);\n        if (this.else)\n            code += \"else \" + this.else.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        super.optimizeNodes();\n        const cond = this.condition;\n        if (cond === true)\n            return this.nodes; // else is ignored here\n        let e = this.else;\n        if (e) {\n            const ns = e.optimizeNodes();\n            e = this.else = Array.isArray(ns) ? new Else(ns) : ns;\n        }\n        if (e) {\n            if (cond === false)\n                return e instanceof If ? e : e.nodes;\n            if (this.nodes.length)\n                return this;\n            return new If(not(cond), e instanceof If ? [e] : e.nodes);\n        }\n        if (cond === false || !this.nodes.length)\n            return undefined;\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a;\n        this.else = (_a = this.else) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        if (!(super.optimizeNames(names, constants) || this.else))\n            return;\n        this.condition = optimizeExpr(this.condition, names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        addExprNames(names, this.condition);\n        if (this.else)\n            addNames(names, this.else.names);\n        return names;\n    }\n}\nIf.kind = \"if\";\nclass For extends BlockNode {\n}\nFor.kind = \"for\";\nclass ForLoop extends For {\n    constructor(iteration) {\n        super();\n        this.iteration = iteration;\n    }\n    render(opts) {\n        return `for(${this.iteration})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iteration = optimizeExpr(this.iteration, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iteration.names);\n    }\n}\nclass ForRange extends For {\n    constructor(varKind, name, from, to) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.from = from;\n        this.to = to;\n    }\n    render(opts) {\n        const varKind = opts.es5 ? scope_1.varKinds.var : this.varKind;\n        const { name, from, to } = this;\n        return `for(${varKind} ${name}=${from}; ${name}<${to}; ${name}++)` + super.render(opts);\n    }\n    get names() {\n        const names = addExprNames(super.names, this.from);\n        return addExprNames(names, this.to);\n    }\n}\nclass ForIter extends For {\n    constructor(loop, varKind, name, iterable) {\n        super();\n        this.loop = loop;\n        this.varKind = varKind;\n        this.name = name;\n        this.iterable = iterable;\n    }\n    render(opts) {\n        return `for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iterable = optimizeExpr(this.iterable, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iterable.names);\n    }\n}\nclass Func extends BlockNode {\n    constructor(name, args, async) {\n        super();\n        this.name = name;\n        this.args = args;\n        this.async = async;\n    }\n    render(opts) {\n        const _async = this.async ? \"async \" : \"\";\n        return `${_async}function ${this.name}(${this.args})` + super.render(opts);\n    }\n}\nFunc.kind = \"func\";\nclass Return extends ParentNode {\n    render(opts) {\n        return \"return \" + super.render(opts);\n    }\n}\nReturn.kind = \"return\";\nclass Try extends BlockNode {\n    render(opts) {\n        let code = \"try\" + super.render(opts);\n        if (this.catch)\n            code += this.catch.render(opts);\n        if (this.finally)\n            code += this.finally.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        var _a, _b;\n        super.optimizeNodes();\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNodes();\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNodes();\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a, _b;\n        super.optimizeNames(names, constants);\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNames(names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        if (this.catch)\n            addNames(names, this.catch.names);\n        if (this.finally)\n            addNames(names, this.finally.names);\n        return names;\n    }\n}\nclass Catch extends BlockNode {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render(opts) {\n        return `catch(${this.error})` + super.render(opts);\n    }\n}\nCatch.kind = \"catch\";\nclass Finally extends BlockNode {\n    render(opts) {\n        return \"finally\" + super.render(opts);\n    }\n}\nFinally.kind = \"finally\";\nclass CodeGen {\n    constructor(extScope, opts = {}) {\n        this._values = {};\n        this._blockStarts = [];\n        this._constants = {};\n        this.opts = { ...opts, _n: opts.lines ? \"\\n\" : \"\" };\n        this._extScope = extScope;\n        this._scope = new scope_1.Scope({ parent: extScope });\n        this._nodes = [new Root()];\n    }\n    toString() {\n        return this._root.render(this.opts);\n    }\n    // returns unique name in the internal scope\n    name(prefix) {\n        return this._scope.name(prefix);\n    }\n    // reserves unique name in the external scope\n    scopeName(prefix) {\n        return this._extScope.name(prefix);\n    }\n    // reserves unique name in the external scope and assigns value to it\n    scopeValue(prefixOrName, value) {\n        const name = this._extScope.value(prefixOrName, value);\n        const vs = this._values[name.prefix] || (this._values[name.prefix] = new Set());\n        vs.add(name);\n        return name;\n    }\n    getScopeValue(prefix, keyOrRef) {\n        return this._extScope.getValue(prefix, keyOrRef);\n    }\n    // return code that assigns values in the external scope to the names that are used internally\n    // (same names that were returned by gen.scopeName or gen.scopeValue)\n    scopeRefs(scopeName) {\n        return this._extScope.scopeRefs(scopeName, this._values);\n    }\n    scopeCode() {\n        return this._extScope.scopeCode(this._values);\n    }\n    _def(varKind, nameOrPrefix, rhs, constant) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (rhs !== undefined && constant)\n            this._constants[name.str] = rhs;\n        this._leafNode(new Def(varKind, name, rhs));\n        return name;\n    }\n    // `const` declaration (`var` in es5 mode)\n    const(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.const, nameOrPrefix, rhs, _constant);\n    }\n    // `let` declaration with optional assignment (`var` in es5 mode)\n    let(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.let, nameOrPrefix, rhs, _constant);\n    }\n    // `var` declaration with optional assignment\n    var(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.var, nameOrPrefix, rhs, _constant);\n    }\n    // assignment code\n    assign(lhs, rhs, sideEffects) {\n        return this._leafNode(new Assign(lhs, rhs, sideEffects));\n    }\n    // `+=` code\n    add(lhs, rhs) {\n        return this._leafNode(new AssignOp(lhs, exports.operators.ADD, rhs));\n    }\n    // appends passed SafeExpr to code or executes Block\n    code(c) {\n        if (typeof c == \"function\")\n            c();\n        else if (c !== code_1.nil)\n            this._leafNode(new AnyCode(c));\n        return this;\n    }\n    // returns code for object literal for the passed argument list of key-value pairs\n    object(...keyValues) {\n        const code = [\"{\"];\n        for (const [key, value] of keyValues) {\n            if (code.length > 1)\n                code.push(\",\");\n            code.push(key);\n            if (key !== value || this.opts.es5) {\n                code.push(\":\");\n                code_1.addCodeArg(code, value);\n            }\n        }\n        code.push(\"}\");\n        return new code_1._Code(code);\n    }\n    // `if` clause (or statement if `thenBody` and, optionally, `elseBody` are passed)\n    if(condition, thenBody, elseBody) {\n        this._blockNode(new If(condition));\n        if (thenBody && elseBody) {\n            this.code(thenBody).else().code(elseBody).endIf();\n        }\n        else if (thenBody) {\n            this.code(thenBody).endIf();\n        }\n        else if (elseBody) {\n            throw new Error('CodeGen: \"else\" body without \"then\" body');\n        }\n        return this;\n    }\n    // `else if` clause - invalid without `if` or after `else` clauses\n    elseIf(condition) {\n        return this._elseNode(new If(condition));\n    }\n    // `else` clause - only valid after `if` or `else if` clauses\n    else() {\n        return this._elseNode(new Else());\n    }\n    // end `if` statement (needed if gen.if was used only with condition)\n    endIf() {\n        return this._endBlockNode(If, Else);\n    }\n    _for(node, forBody) {\n        this._blockNode(node);\n        if (forBody)\n            this.code(forBody).endFor();\n        return this;\n    }\n    // a generic `for` clause (or statement if `forBody` is passed)\n    for(iteration, forBody) {\n        return this._for(new ForLoop(iteration), forBody);\n    }\n    // `for` statement for a range of values\n    forRange(nameOrPrefix, from, to, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.let) {\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForRange(varKind, name, from, to), () => forBody(name));\n    }\n    // `for-of` statement (in es5 mode replace with a normal for loop)\n    forOf(nameOrPrefix, iterable, forBody, varKind = scope_1.varKinds.const) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (this.opts.es5) {\n            const arr = iterable instanceof code_1.Name ? iterable : this.var(\"_arr\", iterable);\n            return this.forRange(\"_i\", 0, code_1._ `${arr}.length`, (i) => {\n                this.var(name, code_1._ `${arr}[${i}]`);\n                forBody(name);\n            });\n        }\n        return this._for(new ForIter(\"of\", varKind, name, iterable), () => forBody(name));\n    }\n    // `for-in` statement.\n    // With option `ownProperties` replaced with a `for-of` loop for object keys\n    forIn(nameOrPrefix, obj, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.const) {\n        if (this.opts.ownProperties) {\n            return this.forOf(nameOrPrefix, code_1._ `Object.keys(${obj})`, forBody);\n        }\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForIter(\"in\", varKind, name, obj), () => forBody(name));\n    }\n    // end `for` loop\n    endFor() {\n        return this._endBlockNode(For);\n    }\n    // `label` statement\n    label(label) {\n        return this._leafNode(new Label(label));\n    }\n    // `break` statement\n    break(label) {\n        return this._leafNode(new Break(label));\n    }\n    // `return` statement\n    return(value) {\n        const node = new Return();\n        this._blockNode(node);\n        this.code(value);\n        if (node.nodes.length !== 1)\n            throw new Error('CodeGen: \"return\" should have one node');\n        return this._endBlockNode(Return);\n    }\n    // `try` statement\n    try(tryBody, catchCode, finallyCode) {\n        if (!catchCode && !finallyCode)\n            throw new Error('CodeGen: \"try\" without \"catch\" and \"finally\"');\n        const node = new Try();\n        this._blockNode(node);\n        this.code(tryBody);\n        if (catchCode) {\n            const error = this.name(\"e\");\n            this._currNode = node.catch = new Catch(error);\n            catchCode(error);\n        }\n        if (finallyCode) {\n            this._currNode = node.finally = new Finally();\n            this.code(finallyCode);\n        }\n        return this._endBlockNode(Catch, Finally);\n    }\n    // `throw` statement\n    throw(error) {\n        return this._leafNode(new Throw(error));\n    }\n    // start self-balancing block\n    block(body, nodeCount) {\n        this._blockStarts.push(this._nodes.length);\n        if (body)\n            this.code(body).endBlock(nodeCount);\n        return this;\n    }\n    // end the current self-balancing block\n    endBlock(nodeCount) {\n        const len = this._blockStarts.pop();\n        if (len === undefined)\n            throw new Error(\"CodeGen: not in self-balancing block\");\n        const toClose = this._nodes.length - len;\n        if (toClose < 0 || (nodeCount !== undefined && toClose !== nodeCount)) {\n            throw new Error(`CodeGen: wrong number of nodes: ${toClose} vs ${nodeCount} expected`);\n        }\n        this._nodes.length = len;\n        return this;\n    }\n    // `function` heading (or definition if funcBody is passed)\n    func(name, args = code_1.nil, async, funcBody) {\n        this._blockNode(new Func(name, args, async));\n        if (funcBody)\n            this.code(funcBody).endFunc();\n        return this;\n    }\n    // end function definition\n    endFunc() {\n        return this._endBlockNode(Func);\n    }\n    optimize(n = 1) {\n        while (n-- > 0) {\n            this._root.optimizeNodes();\n            this._root.optimizeNames(this._root.names, this._constants);\n        }\n    }\n    _leafNode(node) {\n        this._currNode.nodes.push(node);\n        return this;\n    }\n    _blockNode(node) {\n        this._currNode.nodes.push(node);\n        this._nodes.push(node);\n    }\n    _endBlockNode(N1, N2) {\n        const n = this._currNode;\n        if (n instanceof N1 || (N2 && n instanceof N2)) {\n            this._nodes.pop();\n            return this;\n        }\n        throw new Error(`CodeGen: not in block \"${N2 ? `${N1.kind}/${N2.kind}` : N1.kind}\"`);\n    }\n    _elseNode(node) {\n        const n = this._currNode;\n        if (!(n instanceof If)) {\n            throw new Error('CodeGen: \"else\" without \"if\"');\n        }\n        this._currNode = n.else = node;\n        return this;\n    }\n    get _root() {\n        return this._nodes[0];\n    }\n    get _currNode() {\n        const ns = this._nodes;\n        return ns[ns.length - 1];\n    }\n    set _currNode(node) {\n        const ns = this._nodes;\n        ns[ns.length - 1] = node;\n    }\n}\nexports.CodeGen = CodeGen;\nfunction addNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) + (from[n] || 0);\n    return names;\n}\nfunction addExprNames(names, from) {\n    return from instanceof code_1._CodeOrName ? addNames(names, from.names) : names;\n}\nfunction optimizeExpr(expr, names, constants) {\n    if (expr instanceof code_1.Name)\n        return replaceName(expr);\n    if (!canOptimize(expr))\n        return expr;\n    return new code_1._Code(expr._items.reduce((items, c) => {\n        if (c instanceof code_1.Name)\n            c = replaceName(c);\n        if (c instanceof code_1._Code)\n            items.push(...c._items);\n        else\n            items.push(c);\n        return items;\n    }, []));\n    function replaceName(n) {\n        const c = constants[n.str];\n        if (c === undefined || names[n.str] !== 1)\n            return n;\n        delete names[n.str];\n        return c;\n    }\n    function canOptimize(e) {\n        return (e instanceof code_1._Code &&\n            e._items.some((c) => c instanceof code_1.Name && names[c.str] === 1 && constants[c.str] !== undefined));\n    }\n}\nfunction subtractNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) - (from[n] || 0);\n}\nfunction not(x) {\n    return typeof x == \"boolean\" || typeof x == \"number\" || x === null ? !x : code_1._ `!${par(x)}`;\n}\nexports.not = not;\nconst andCode = mappend(exports.operators.AND);\n// boolean AND (&&) expression with the passed arguments\nfunction and(...args) {\n    return args.reduce(andCode);\n}\nexports.and = and;\nconst orCode = mappend(exports.operators.OR);\n// boolean OR (||) expression with the passed arguments\nfunction or(...args) {\n    return args.reduce(orCode);\n}\nexports.or = or;\nfunction mappend(op) {\n    return (x, y) => (x === code_1.nil ? y : y === code_1.nil ? x : code_1._ `${par(x)} ${op} ${par(y)}`);\n}\nfunction par(x) {\n    return x instanceof code_1.Name ? x : code_1._ `(${x})`;\n}\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.regexpCode = exports.getProperty = exports.safeStringify = exports.stringify = exports.strConcat = exports.addCodeArg = exports.str = exports._ = exports.nil = exports._Code = exports.Name = exports.IDENTIFIER = exports._CodeOrName = void 0;\nclass _CodeOrName {\n}\nexports._CodeOrName = _CodeOrName;\nexports.IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i;\nclass Name extends _CodeOrName {\n    constructor(s) {\n        super();\n        if (!exports.IDENTIFIER.test(s))\n            throw new Error(\"CodeGen: name must be a valid identifier\");\n        this.str = s;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        return false;\n    }\n    get names() {\n        return { [this.str]: 1 };\n    }\n}\nexports.Name = Name;\nclass _Code extends _CodeOrName {\n    constructor(code) {\n        super();\n        this._items = typeof code === \"string\" ? [code] : code;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        if (this._items.length > 1)\n            return false;\n        const item = this._items[0];\n        return item === \"\" || item === '\"\"';\n    }\n    get str() {\n        var _a;\n        return ((_a = this._str) !== null && _a !== void 0 ? _a : (this._str = this._items.reduce((s, c) => `${s}${c}`, \"\")));\n    }\n    get names() {\n        var _a;\n        return ((_a = this._names) !== null && _a !== void 0 ? _a : (this._names = this._items.reduce((names, c) => {\n            if (c instanceof Name)\n                names[c.str] = (names[c.str] || 0) + 1;\n            return names;\n        }, {})));\n    }\n}\nexports._Code = _Code;\nexports.nil = new _Code(\"\");\nfunction _(strs, ...args) {\n    const code = [strs[0]];\n    let i = 0;\n    while (i < args.length) {\n        addCodeArg(code, args[i]);\n        code.push(strs[++i]);\n    }\n    return new _Code(code);\n}\nexports._ = _;\nconst plus = new _Code(\"+\");\nfunction str(strs, ...args) {\n    const expr = [safeStringify(strs[0])];\n    let i = 0;\n    while (i < args.length) {\n        expr.push(plus);\n        addCodeArg(expr, args[i]);\n        expr.push(plus, safeStringify(strs[++i]));\n    }\n    optimize(expr);\n    return new _Code(expr);\n}\nexports.str = str;\nfunction addCodeArg(code, arg) {\n    if (arg instanceof _Code)\n        code.push(...arg._items);\n    else if (arg instanceof Name)\n        code.push(arg);\n    else\n        code.push(interpolate(arg));\n}\nexports.addCodeArg = addCodeArg;\nfunction optimize(expr) {\n    let i = 1;\n    while (i < expr.length - 1) {\n        if (expr[i] === plus) {\n            const res = mergeExprItems(expr[i - 1], expr[i + 1]);\n            if (res !== undefined) {\n                expr.splice(i - 1, 3, res);\n                continue;\n            }\n            expr[i++] = \"+\";\n        }\n        i++;\n    }\n}\nfunction mergeExprItems(a, b) {\n    if (b === '\"\"')\n        return a;\n    if (a === '\"\"')\n        return b;\n    if (typeof a == \"string\") {\n        if (b instanceof Name || a[a.length - 1] !== '\"')\n            return;\n        if (typeof b != \"string\")\n            return `${a.slice(0, -1)}${b}\"`;\n        if (b[0] === '\"')\n            return a.slice(0, -1) + b.slice(1);\n        return;\n    }\n    if (typeof b == \"string\" && b[0] === '\"' && !(a instanceof Name))\n        return `\"${a}${b.slice(1)}`;\n    return;\n}\nfunction strConcat(c1, c2) {\n    return c2.emptyStr() ? c1 : c1.emptyStr() ? c2 : str `${c1}${c2}`;\n}\nexports.strConcat = strConcat;\n// TODO do not allow arrays here\nfunction interpolate(x) {\n    return typeof x == \"number\" || typeof x == \"boolean\" || x === null\n        ? x\n        : safeStringify(Array.isArray(x) ? x.join(\",\") : x);\n}\nfunction stringify(x) {\n    return new _Code(safeStringify(x));\n}\nexports.stringify = stringify;\nfunction safeStringify(x) {\n    return JSON.stringify(x)\n        .replace(/\\u2028/g, \"\\\\u2028\")\n        .replace(/\\u2029/g, \"\\\\u2029\");\n}\nexports.safeStringify = safeStringify;\nfunction getProperty(key) {\n    return typeof key == \"string\" && exports.IDENTIFIER.test(key) ? new _Code(`.${key}`) : _ `[${key}]`;\n}\nexports.getProperty = getProperty;\nfunction regexpCode(rx) {\n    return new _Code(rx.toString());\n}\nexports.regexpCode = regexpCode;\n//# sourceMappingURL=code.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ValueScope = exports.ValueScopeName = exports.Scope = exports.varKinds = exports.UsedValueState = void 0;\nconst code_1 = require(\"./code\");\nclass ValueError extends Error {\n    constructor(name) {\n        super(`CodeGen: \"code\" for ${name} not defined`);\n        this.value = name.value;\n    }\n}\nvar UsedValueState;\n(function (UsedValueState) {\n    UsedValueState[UsedValueState[\"Started\"] = 0] = \"Started\";\n    UsedValueState[UsedValueState[\"Completed\"] = 1] = \"Completed\";\n})(UsedValueState = exports.UsedValueState || (exports.UsedValueState = {}));\nexports.varKinds = {\n    const: new code_1.Name(\"const\"),\n    let: new code_1.Name(\"let\"),\n    var: new code_1.Name(\"var\"),\n};\nclass Scope {\n    constructor({ prefixes, parent } = {}) {\n        this._names = {};\n        this._prefixes = prefixes;\n        this._parent = parent;\n    }\n    toName(nameOrPrefix) {\n        return nameOrPrefix instanceof code_1.Name ? nameOrPrefix : this.name(nameOrPrefix);\n    }\n    name(prefix) {\n        return new code_1.Name(this._newName(prefix));\n    }\n    _newName(prefix) {\n        const ng = this._names[prefix] || this._nameGroup(prefix);\n        return `${prefix}${ng.index++}`;\n    }\n    _nameGroup(prefix) {\n        var _a, _b;\n        if (((_b = (_a = this._parent) === null || _a === void 0 ? void 0 : _a._prefixes) === null || _b === void 0 ? void 0 : _b.has(prefix)) || (this._prefixes && !this._prefixes.has(prefix))) {\n            throw new Error(`CodeGen: prefix \"${prefix}\" is not allowed in this scope`);\n        }\n        return (this._names[prefix] = { prefix, index: 0 });\n    }\n}\nexports.Scope = Scope;\nclass ValueScopeName extends code_1.Name {\n    constructor(prefix, nameStr) {\n        super(nameStr);\n        this.prefix = prefix;\n    }\n    setValue(value, { property, itemIndex }) {\n        this.value = value;\n        this.scopePath = code_1._ `.${new code_1.Name(property)}[${itemIndex}]`;\n    }\n}\nexports.ValueScopeName = ValueScopeName;\nconst line = code_1._ `\\n`;\nclass ValueScope extends Scope {\n    constructor(opts) {\n        super(opts);\n        this._values = {};\n        this._scope = opts.scope;\n        this.opts = { ...opts, _n: opts.lines ? line : code_1.nil };\n    }\n    get() {\n        return this._scope;\n    }\n    name(prefix) {\n        return new ValueScopeName(prefix, this._newName(prefix));\n    }\n    value(nameOrPrefix, value) {\n        var _a;\n        if (value.ref === undefined)\n            throw new Error(\"CodeGen: ref must be passed in value\");\n        const name = this.toName(nameOrPrefix);\n        const { prefix } = name;\n        const valueKey = (_a = value.key) !== null && _a !== void 0 ? _a : value.ref;\n        let vs = this._values[prefix];\n        if (vs) {\n            const _name = vs.get(valueKey);\n            if (_name)\n                return _name;\n        }\n        else {\n            vs = this._values[prefix] = new Map();\n        }\n        vs.set(valueKey, name);\n        const s = this._scope[prefix] || (this._scope[prefix] = []);\n        const itemIndex = s.length;\n        s[itemIndex] = value.ref;\n        name.setValue(value, { property: prefix, itemIndex });\n        return name;\n    }\n    getValue(prefix, keyOrRef) {\n        const vs = this._values[prefix];\n        if (!vs)\n            return;\n        return vs.get(keyOrRef);\n    }\n    scopeRefs(scopeName, values = this._values) {\n        return this._reduceValues(values, (name) => {\n            if (name.scopePath === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return code_1._ `${scopeName}${name.scopePath}`;\n        });\n    }\n    scopeCode(values = this._values, usedValues, getCode) {\n        return this._reduceValues(values, (name) => {\n            if (name.value === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return name.value.code;\n        }, usedValues, getCode);\n    }\n    _reduceValues(values, valueCode, usedValues = {}, getCode) {\n        let code = code_1.nil;\n        for (const prefix in values) {\n            const vs = values[prefix];\n            if (!vs)\n                continue;\n            const nameSet = (usedValues[prefix] = usedValues[prefix] || new Map());\n            vs.forEach((name) => {\n                if (nameSet.has(name))\n                    return;\n                nameSet.set(name, UsedValueState.Started);\n                let c = valueCode(name);\n                if (c) {\n                    const def = this.opts.es5 ? exports.varKinds.var : exports.varKinds.const;\n                    code = code_1._ `${code}${def} ${name} = ${c};${this.opts._n}`;\n                }\n                else if ((c = getCode === null || getCode === void 0 ? void 0 : getCode(name))) {\n                    code = code_1._ `${code}${c}${this.opts._n}`;\n                }\n                else {\n                    throw new ValueError(name);\n                }\n                nameSet.set(name, UsedValueState.Completed);\n            });\n        }\n        return code;\n    }\n}\nexports.ValueScope = ValueScope;\n//# sourceMappingURL=scope.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.checkStrictMode = exports.getErrorPath = exports.Type = exports.useFunc = exports.setEvaluated = exports.evaluatedPropsToName = exports.mergeEvaluated = exports.eachItem = exports.unescapeJsonPointer = exports.escapeJsonPointer = exports.escapeFragment = exports.unescapeFragment = exports.schemaRefOrVal = exports.schemaHasRulesButRef = exports.schemaHasRules = exports.checkUnknownRules = exports.alwaysValidSchema = exports.toHash = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst code_1 = require(\"./codegen/code\");\n// TODO refactor to use Set\nfunction toHash(arr) {\n    const hash = {};\n    for (const item of arr)\n        hash[item] = true;\n    return hash;\n}\nexports.toHash = toHash;\nfunction alwaysValidSchema(it, schema) {\n    if (typeof schema == \"boolean\")\n        return schema;\n    if (Object.keys(schema).length === 0)\n        return true;\n    checkUnknownRules(it, schema);\n    return !schemaHasRules(schema, it.self.RULES.all);\n}\nexports.alwaysValidSchema = alwaysValidSchema;\nfunction checkUnknownRules(it, schema = it.schema) {\n    const { opts, self } = it;\n    if (!opts.strictSchema)\n        return;\n    if (typeof schema === \"boolean\")\n        return;\n    const rules = self.RULES.keywords;\n    for (const key in schema) {\n        if (!rules[key])\n            checkStrictMode(it, `unknown keyword: \"${key}\"`);\n    }\n}\nexports.checkUnknownRules = checkUnknownRules;\nfunction schemaHasRules(schema, rules) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (rules[key])\n            return true;\n    return false;\n}\nexports.schemaHasRules = schemaHasRules;\nfunction schemaHasRulesButRef(schema, RULES) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (key !== \"$ref\" && RULES.all[key])\n            return true;\n    return false;\n}\nexports.schemaHasRulesButRef = schemaHasRulesButRef;\nfunction schemaRefOrVal({ topSchemaRef, schemaPath }, schema, keyword, $data) {\n    if (!$data) {\n        if (typeof schema == \"number\" || typeof schema == \"boolean\")\n            return schema;\n        if (typeof schema == \"string\")\n            return codegen_1._ `${schema}`;\n    }\n    return codegen_1._ `${topSchemaRef}${schemaPath}${codegen_1.getProperty(keyword)}`;\n}\nexports.schemaRefOrVal = schemaRefOrVal;\nfunction unescapeFragment(str) {\n    return unescapeJsonPointer(decodeURIComponent(str));\n}\nexports.unescapeFragment = unescapeFragment;\nfunction escapeFragment(str) {\n    return encodeURIComponent(escapeJsonPointer(str));\n}\nexports.escapeFragment = escapeFragment;\nfunction escapeJsonPointer(str) {\n    if (typeof str == \"number\")\n        return `${str}`;\n    return str.replace(/~/g, \"~0\").replace(/\\//g, \"~1\");\n}\nexports.escapeJsonPointer = escapeJsonPointer;\nfunction unescapeJsonPointer(str) {\n    return str.replace(/~1/g, \"/\").replace(/~0/g, \"~\");\n}\nexports.unescapeJsonPointer = unescapeJsonPointer;\nfunction eachItem(xs, f) {\n    if (Array.isArray(xs)) {\n        for (const x of xs)\n            f(x);\n    }\n    else {\n        f(xs);\n    }\n}\nexports.eachItem = eachItem;\nfunction makeMergeEvaluated({ mergeNames, mergeToName, mergeValues, resultToName, }) {\n    return (gen, from, to, toName) => {\n        const res = to === undefined\n            ? from\n            : to instanceof codegen_1.Name\n                ? (from instanceof codegen_1.Name ? mergeNames(gen, from, to) : mergeToName(gen, from, to), to)\n                : from instanceof codegen_1.Name\n                    ? (mergeToName(gen, to, from), from)\n                    : mergeValues(from, to);\n        return toName === codegen_1.Name && !(res instanceof codegen_1.Name) ? resultToName(gen, res) : res;\n    };\n}\nexports.mergeEvaluated = {\n    props: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if(codegen_1._ `${to} !== true && ${from} !== undefined`, () => {\n            gen.if(codegen_1._ `${from} === true`, () => gen.assign(to, true), () => gen.assign(to, codegen_1._ `${to} || {}`).code(codegen_1._ `Object.assign(${to}, ${from})`));\n        }),\n        mergeToName: (gen, from, to) => gen.if(codegen_1._ `${to} !== true`, () => {\n            if (from === true) {\n                gen.assign(to, true);\n            }\n            else {\n                gen.assign(to, codegen_1._ `${to} || {}`);\n                setEvaluated(gen, to, from);\n            }\n        }),\n        mergeValues: (from, to) => (from === true ? true : { ...from, ...to }),\n        resultToName: evaluatedPropsToName,\n    }),\n    items: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if(codegen_1._ `${to} !== true && ${from} !== undefined`, () => gen.assign(to, codegen_1._ `${from} === true ? true : ${to} > ${from} ? ${to} : ${from}`)),\n        mergeToName: (gen, from, to) => gen.if(codegen_1._ `${to} !== true`, () => gen.assign(to, from === true ? true : codegen_1._ `${to} > ${from} ? ${to} : ${from}`)),\n        mergeValues: (from, to) => (from === true ? true : Math.max(from, to)),\n        resultToName: (gen, items) => gen.var(\"items\", items),\n    }),\n};\nfunction evaluatedPropsToName(gen, ps) {\n    if (ps === true)\n        return gen.var(\"props\", true);\n    const props = gen.var(\"props\", codegen_1._ `{}`);\n    if (ps !== undefined)\n        setEvaluated(gen, props, ps);\n    return props;\n}\nexports.evaluatedPropsToName = evaluatedPropsToName;\nfunction setEvaluated(gen, props, ps) {\n    Object.keys(ps).forEach((p) => gen.assign(codegen_1._ `${props}${codegen_1.getProperty(p)}`, true));\n}\nexports.setEvaluated = setEvaluated;\nconst snippets = {};\nfunction useFunc(gen, f) {\n    return gen.scopeValue(\"func\", {\n        ref: f,\n        code: snippets[f.code] || (snippets[f.code] = new code_1._Code(f.code)),\n    });\n}\nexports.useFunc = useFunc;\nvar Type;\n(function (Type) {\n    Type[Type[\"Num\"] = 0] = \"Num\";\n    Type[Type[\"Str\"] = 1] = \"Str\";\n})(Type = exports.Type || (exports.Type = {}));\nfunction getErrorPath(dataProp, dataPropType, jsPropertySyntax) {\n    // let path\n    if (dataProp instanceof codegen_1.Name) {\n        const isNumber = dataPropType === Type.Num;\n        return jsPropertySyntax\n            ? isNumber\n                ? codegen_1._ `\"[\" + ${dataProp} + \"]\"`\n                : codegen_1._ `\"['\" + ${dataProp} + \"']\"`\n            : isNumber\n                ? codegen_1._ `\"/\" + ${dataProp}`\n                : codegen_1._ `\"/\" + ${dataProp}.replace(/~/g, \"~0\").replace(/\\\\//g, \"~1\")`; // TODO maybe use global escapePointer\n    }\n    return jsPropertySyntax ? codegen_1.getProperty(dataProp).toString() : \"/\" + escapeJsonPointer(dataProp);\n}\nexports.getErrorPath = getErrorPath;\nfunction checkStrictMode(it, msg, mode = it.opts.strictSchema) {\n    if (!mode)\n        return;\n    msg = `strict mode: ${msg}`;\n    if (mode === true)\n        throw new Error(msg);\n    it.self.logger.warn(msg);\n}\nexports.checkStrictMode = checkStrictMode;\n//# sourceMappingURL=util.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"./codegen\");\nconst names = {\n    // validation function arguments\n    data: new codegen_1.Name(\"data\"),\n    // args passed from referencing schema\n    valCxt: new codegen_1.Name(\"valCxt\"),\n    instancePath: new codegen_1.Name(\"instancePath\"),\n    parentData: new codegen_1.Name(\"parentData\"),\n    parentDataProperty: new codegen_1.Name(\"parentDataProperty\"),\n    rootData: new codegen_1.Name(\"rootData\"),\n    dynamicAnchors: new codegen_1.Name(\"dynamicAnchors\"),\n    // function scoped variables\n    vErrors: new codegen_1.Name(\"vErrors\"),\n    errors: new codegen_1.Name(\"errors\"),\n    this: new codegen_1.Name(\"this\"),\n    // \"globals\"\n    self: new codegen_1.Name(\"self\"),\n    scope: new codegen_1.Name(\"scope\"),\n    // JTD serialize/parse name for JSON string and position\n    json: new codegen_1.Name(\"json\"),\n    jsonPos: new codegen_1.Name(\"jsonPos\"),\n    jsonLen: new codegen_1.Name(\"jsonLen\"),\n    jsonPart: new codegen_1.Name(\"jsonPart\"),\n};\nexports.default = names;\n//# sourceMappingURL=names.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reportTypeError = exports.checkDataTypes = exports.checkDataType = exports.coerceAndCheckDataType = exports.getJSONTypes = exports.getSchemaTypes = exports.DataType = void 0;\nconst rules_1 = require(\"../rules\");\nconst applicability_1 = require(\"./applicability\");\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nvar DataType;\n(function (DataType) {\n    DataType[DataType[\"Correct\"] = 0] = \"Correct\";\n    DataType[DataType[\"Wrong\"] = 1] = \"Wrong\";\n})(DataType = exports.DataType || (exports.DataType = {}));\nfunction getSchemaTypes(schema) {\n    const types = getJSONTypes(schema.type);\n    const hasNull = types.includes(\"null\");\n    if (hasNull) {\n        if (schema.nullable === false)\n            throw new Error(\"type: null contradicts nullable: false\");\n    }\n    else {\n        if (!types.length && schema.nullable !== undefined) {\n            throw new Error('\"nullable\" cannot be used without \"type\"');\n        }\n        if (schema.nullable === true)\n            types.push(\"null\");\n    }\n    return types;\n}\nexports.getSchemaTypes = getSchemaTypes;\nfunction getJSONTypes(ts) {\n    const types = Array.isArray(ts) ? ts : ts ? [ts] : [];\n    if (types.every(rules_1.isJSONType))\n        return types;\n    throw new Error(\"type must be JSONType or JSONType[]: \" + types.join(\",\"));\n}\nexports.getJSONTypes = getJSONTypes;\nfunction coerceAndCheckDataType(it, types) {\n    const { gen, data, opts } = it;\n    const coerceTo = coerceToTypes(types, opts.coerceTypes);\n    const checkTypes = types.length > 0 &&\n        !(coerceTo.length === 0 && types.length === 1 && applicability_1.schemaHasRulesForType(it, types[0]));\n    if (checkTypes) {\n        const wrongType = checkDataTypes(types, data, opts.strictNumbers, DataType.Wrong);\n        gen.if(wrongType, () => {\n            if (coerceTo.length)\n                coerceData(it, types, coerceTo);\n            else\n                reportTypeError(it);\n        });\n    }\n    return checkTypes;\n}\nexports.coerceAndCheckDataType = coerceAndCheckDataType;\nconst COERCIBLE = new Set([\"string\", \"number\", \"integer\", \"boolean\", \"null\"]);\nfunction coerceToTypes(types, coerceTypes) {\n    return coerceTypes\n        ? types.filter((t) => COERCIBLE.has(t) || (coerceTypes === \"array\" && t === \"array\"))\n        : [];\n}\nfunction coerceData(it, types, coerceTo) {\n    const { gen, data, opts } = it;\n    const dataType = gen.let(\"dataType\", codegen_1._ `typeof ${data}`);\n    const coerced = gen.let(\"coerced\", codegen_1._ `undefined`);\n    if (opts.coerceTypes === \"array\") {\n        gen.if(codegen_1._ `${dataType} == 'object' && Array.isArray(${data}) && ${data}.length == 1`, () => gen\n            .assign(data, codegen_1._ `${data}[0]`)\n            .assign(dataType, codegen_1._ `typeof ${data}`)\n            .if(checkDataTypes(types, data, opts.strictNumbers), () => gen.assign(coerced, data)));\n    }\n    gen.if(codegen_1._ `${coerced} !== undefined`);\n    for (const t of coerceTo) {\n        if (COERCIBLE.has(t) || (t === \"array\" && opts.coerceTypes === \"array\")) {\n            coerceSpecificType(t);\n        }\n    }\n    gen.else();\n    reportTypeError(it);\n    gen.endIf();\n    gen.if(codegen_1._ `${coerced} !== undefined`, () => {\n        gen.assign(data, coerced);\n        assignParentData(it, coerced);\n    });\n    function coerceSpecificType(t) {\n        switch (t) {\n            case \"string\":\n                gen\n                    .elseIf(codegen_1._ `${dataType} == \"number\" || ${dataType} == \"boolean\"`)\n                    .assign(coerced, codegen_1._ `\"\" + ${data}`)\n                    .elseIf(codegen_1._ `${data} === null`)\n                    .assign(coerced, codegen_1._ `\"\"`);\n                return;\n            case \"number\":\n                gen\n                    .elseIf(codegen_1._ `${dataType} == \"boolean\" || ${data} === null\n              || (${dataType} == \"string\" && ${data} && ${data} == +${data})`)\n                    .assign(coerced, codegen_1._ `+${data}`);\n                return;\n            case \"integer\":\n                gen\n                    .elseIf(codegen_1._ `${dataType} === \"boolean\" || ${data} === null\n              || (${dataType} === \"string\" && ${data} && ${data} == +${data} && !(${data} % 1))`)\n                    .assign(coerced, codegen_1._ `+${data}`);\n                return;\n            case \"boolean\":\n                gen\n                    .elseIf(codegen_1._ `${data} === \"false\" || ${data} === 0 || ${data} === null`)\n                    .assign(coerced, false)\n                    .elseIf(codegen_1._ `${data} === \"true\" || ${data} === 1`)\n                    .assign(coerced, true);\n                return;\n            case \"null\":\n                gen.elseIf(codegen_1._ `${data} === \"\" || ${data} === 0 || ${data} === false`);\n                gen.assign(coerced, null);\n                return;\n            case \"array\":\n                gen\n                    .elseIf(codegen_1._ `${dataType} === \"string\" || ${dataType} === \"number\"\n              || ${dataType} === \"boolean\" || ${data} === null`)\n                    .assign(coerced, codegen_1._ `[${data}]`);\n        }\n    }\n}\nfunction assignParentData({ gen, parentData, parentDataProperty }, expr) {\n    // TODO use gen.property\n    gen.if(codegen_1._ `${parentData} !== undefined`, () => gen.assign(codegen_1._ `${parentData}[${parentDataProperty}]`, expr));\n}\nfunction checkDataType(dataType, data, strictNums, correct = DataType.Correct) {\n    const EQ = correct === DataType.Correct ? codegen_1.operators.EQ : codegen_1.operators.NEQ;\n    let cond;\n    switch (dataType) {\n        case \"null\":\n            return codegen_1._ `${data} ${EQ} null`;\n        case \"array\":\n            cond = codegen_1._ `Array.isArray(${data})`;\n            break;\n        case \"object\":\n            cond = codegen_1._ `${data} && typeof ${data} == \"object\" && !Array.isArray(${data})`;\n            break;\n        case \"integer\":\n            cond = numCond(codegen_1._ `!(${data} % 1) && !isNaN(${data})`);\n            break;\n        case \"number\":\n            cond = numCond();\n            break;\n        default:\n            return codegen_1._ `typeof ${data} ${EQ} ${dataType}`;\n    }\n    return correct === DataType.Correct ? cond : codegen_1.not(cond);\n    function numCond(_cond = codegen_1.nil) {\n        return codegen_1.and(codegen_1._ `typeof ${data} == \"number\"`, _cond, strictNums ? codegen_1._ `isFinite(${data})` : codegen_1.nil);\n    }\n}\nexports.checkDataType = checkDataType;\nfunction checkDataTypes(dataTypes, data, strictNums, correct) {\n    if (dataTypes.length === 1) {\n        return checkDataType(dataTypes[0], data, strictNums, correct);\n    }\n    let cond;\n    const types = util_1.toHash(dataTypes);\n    if (types.array && types.object) {\n        const notObj = codegen_1._ `typeof ${data} != \"object\"`;\n        cond = types.null ? notObj : codegen_1._ `!${data} || ${notObj}`;\n        delete types.null;\n        delete types.array;\n        delete types.object;\n    }\n    else {\n        cond = codegen_1.nil;\n    }\n    if (types.number)\n        delete types.integer;\n    for (const t in types)\n        cond = codegen_1.and(cond, checkDataType(t, data, strictNums, correct));\n    return cond;\n}\nexports.checkDataTypes = checkDataTypes;\nconst typeError = {\n    message: ({ schema }) => `must be ${schema}`,\n    params: ({ schema, schemaValue }) => typeof schema == \"string\" ? codegen_1._ `{type: ${schema}}` : codegen_1._ `{type: ${schemaValue}}`,\n};\nfunction reportTypeError(it) {\n    const cxt = getTypeErrorContext(it);\n    errors_1.reportError(cxt, typeError);\n}\nexports.reportTypeError = reportTypeError;\nfunction getTypeErrorContext(it) {\n    const { gen, data, schema } = it;\n    const schemaCode = util_1.schemaRefOrVal(it, schema, \"type\");\n    return {\n        gen,\n        keyword: \"type\",\n        data,\n        schema: schema.type,\n        schemaCode,\n        schemaValue: schemaCode,\n        parentSchema: schema,\n        params: {},\n        it,\n    };\n}\n//# sourceMappingURL=dataType.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRules = exports.isJSONType = void 0;\nconst _jsonTypes = [\"string\", \"number\", \"integer\", \"boolean\", \"null\", \"object\", \"array\"];\nconst jsonTypes = new Set(_jsonTypes);\nfunction isJSONType(x) {\n    return typeof x == \"string\" && jsonTypes.has(x);\n}\nexports.isJSONType = isJSONType;\nfunction getRules() {\n    const groups = {\n        number: { type: \"number\", rules: [] },\n        string: { type: \"string\", rules: [] },\n        array: { type: \"array\", rules: [] },\n        object: { type: \"object\", rules: [] },\n    };\n    return {\n        types: { ...groups, integer: true, boolean: true, null: true },\n        rules: [{ rules: [] }, groups.number, groups.string, groups.array, groups.object],\n        post: { rules: [] },\n        all: {},\n        keywords: {},\n    };\n}\nexports.getRules = getRules;\n//# sourceMappingURL=rules.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shouldUseRule = exports.shouldUseGroup = exports.schemaHasRulesForType = void 0;\nfunction schemaHasRulesForType({ schema, self }, type) {\n    const group = self.RULES.types[type];\n    return group && group !== true && shouldUseGroup(schema, group);\n}\nexports.schemaHasRulesForType = schemaHasRulesForType;\nfunction shouldUseGroup(schema, group) {\n    return group.rules.some((rule) => shouldUseRule(schema, rule));\n}\nexports.shouldUseGroup = shouldUseGroup;\nfunction shouldUseRule(schema, rule) {\n    var _a;\n    return (schema[rule.keyword] !== undefined ||\n        ((_a = rule.definition.implements) === null || _a === void 0 ? void 0 : _a.some((kwd) => schema[kwd] !== undefined)));\n}\nexports.shouldUseRule = shouldUseRule;\n//# sourceMappingURL=applicability.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assignDefaults = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction assignDefaults(it, ty) {\n    const { properties, items } = it.schema;\n    if (ty === \"object\" && properties) {\n        for (const key in properties) {\n            assignDefault(it, key, properties[key].default);\n        }\n    }\n    else if (ty === \"array\" && Array.isArray(items)) {\n        items.forEach((sch, i) => assignDefault(it, i, sch.default));\n    }\n}\nexports.assignDefaults = assignDefaults;\nfunction assignDefault(it, prop, defaultValue) {\n    const { gen, compositeRule, data, opts } = it;\n    if (defaultValue === undefined)\n        return;\n    const childData = codegen_1._ `${data}${codegen_1.getProperty(prop)}`;\n    if (compositeRule) {\n        util_1.checkStrictMode(it, `default is ignored for: ${childData}`);\n        return;\n    }\n    let condition = codegen_1._ `${childData} === undefined`;\n    if (opts.useDefaults === \"empty\") {\n        condition = codegen_1._ `${condition} || ${childData} === null || ${childData} === \"\"`;\n    }\n    // `${childData} === undefined` +\n    // (opts.useDefaults === \"empty\" ? ` || ${childData} === null || ${childData} === \"\"` : \"\")\n    gen.if(condition, codegen_1._ `${childData} = ${codegen_1.stringify(defaultValue)}`);\n}\n//# sourceMappingURL=defaults.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateKeywordUsage = exports.validSchemaType = exports.funcKeywordCode = exports.macroKeywordCode = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst code_1 = require(\"../../vocabularies/code\");\nconst errors_1 = require(\"../errors\");\nfunction macroKeywordCode(cxt, def) {\n    const { gen, keyword, schema, parentSchema, it } = cxt;\n    const macroSchema = def.macro.call(it.self, schema, parentSchema, it);\n    const schemaRef = useKeyword(gen, keyword, macroSchema);\n    if (it.opts.validateSchema !== false)\n        it.self.validateSchema(macroSchema, true);\n    const valid = gen.name(\"valid\");\n    cxt.subschema({\n        schema: macroSchema,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n        topSchemaRef: schemaRef,\n        compositeRule: true,\n    }, valid);\n    cxt.pass(valid, () => cxt.error(true));\n}\nexports.macroKeywordCode = macroKeywordCode;\nfunction funcKeywordCode(cxt, def) {\n    var _a;\n    const { gen, keyword, schema, parentSchema, $data, it } = cxt;\n    checkAsyncKeyword(it, def);\n    const validate = !$data && def.compile ? def.compile.call(it.self, schema, parentSchema, it) : def.validate;\n    const validateRef = useKeyword(gen, keyword, validate);\n    const valid = gen.let(\"valid\");\n    cxt.block$data(valid, validateKeyword);\n    cxt.ok((_a = def.valid) !== null && _a !== void 0 ? _a : valid);\n    function validateKeyword() {\n        if (def.errors === false) {\n            assignValid();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => cxt.error());\n        }\n        else {\n            const ruleErrs = def.async ? validateAsync() : validateSync();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => addErrs(cxt, ruleErrs));\n        }\n    }\n    function validateAsync() {\n        const ruleErrs = gen.let(\"ruleErrs\", null);\n        gen.try(() => assignValid(codegen_1._ `await `), (e) => gen.assign(valid, false).if(codegen_1._ `${e} instanceof ${it.ValidationError}`, () => gen.assign(ruleErrs, codegen_1._ `${e}.errors`), () => gen.throw(e)));\n        return ruleErrs;\n    }\n    function validateSync() {\n        const validateErrs = codegen_1._ `${validateRef}.errors`;\n        gen.assign(validateErrs, null);\n        assignValid(codegen_1.nil);\n        return validateErrs;\n    }\n    function assignValid(_await = def.async ? codegen_1._ `await ` : codegen_1.nil) {\n        const passCxt = it.opts.passContext ? names_1.default.this : names_1.default.self;\n        const passSchema = !((\"compile\" in def && !$data) || def.schema === false);\n        gen.assign(valid, codegen_1._ `${_await}${code_1.callValidateCode(cxt, validateRef, passCxt, passSchema)}`, def.modifying);\n    }\n    function reportErrs(errors) {\n        var _a;\n        gen.if(codegen_1.not((_a = def.valid) !== null && _a !== void 0 ? _a : valid), errors);\n    }\n}\nexports.funcKeywordCode = funcKeywordCode;\nfunction modifyData(cxt) {\n    const { gen, data, it } = cxt;\n    gen.if(it.parentData, () => gen.assign(data, codegen_1._ `${it.parentData}[${it.parentDataProperty}]`));\n}\nfunction addErrs(cxt, errs) {\n    const { gen } = cxt;\n    gen.if(codegen_1._ `Array.isArray(${errs})`, () => {\n        gen\n            .assign(names_1.default.vErrors, codegen_1._ `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`)\n            .assign(names_1.default.errors, codegen_1._ `${names_1.default.vErrors}.length`);\n        errors_1.extendErrors(cxt);\n    }, () => cxt.error());\n}\nfunction checkAsyncKeyword({ schemaEnv }, def) {\n    if (def.async && !schemaEnv.$async)\n        throw new Error(\"async keyword in sync schema\");\n}\nfunction useKeyword(gen, keyword, result) {\n    if (result === undefined)\n        throw new Error(`keyword \"${keyword}\" failed to compile`);\n    return gen.scopeValue(\"keyword\", typeof result == \"function\" ? { ref: result } : { ref: result, code: codegen_1.stringify(result) });\n}\nfunction validSchemaType(schema, schemaType, allowUndefined = false) {\n    // TODO add tests\n    return (!schemaType.length ||\n        schemaType.some((st) => st === \"array\"\n            ? Array.isArray(schema)\n            : st === \"object\"\n                ? schema && typeof schema == \"object\" && !Array.isArray(schema)\n                : typeof schema == st || (allowUndefined && typeof schema == \"undefined\")));\n}\nexports.validSchemaType = validSchemaType;\nfunction validateKeywordUsage({ schema, opts, self, errSchemaPath }, def, keyword) {\n    /* istanbul ignore if */\n    if (Array.isArray(def.keyword) ? !def.keyword.includes(keyword) : def.keyword !== keyword) {\n        throw new Error(\"ajv implementation error\");\n    }\n    const deps = def.dependencies;\n    if (deps === null || deps === void 0 ? void 0 : deps.some((kwd) => !Object.prototype.hasOwnProperty.call(schema, kwd))) {\n        throw new Error(`parent schema must have dependencies of ${keyword}: ${deps.join(\",\")}`);\n    }\n    if (def.validateSchema) {\n        const valid = def.validateSchema(schema[keyword]);\n        if (!valid) {\n            const msg = `keyword \"${keyword}\" value is invalid at path \"${errSchemaPath}\": ` +\n                self.errorsText(def.validateSchema.errors);\n            if (opts.validateSchema === \"log\")\n                self.logger.error(msg);\n            else\n                throw new Error(msg);\n        }\n    }\n}\nexports.validateKeywordUsage = validateKeywordUsage;\n//# sourceMappingURL=keyword.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUnion = exports.validateArray = exports.usePattern = exports.callValidateCode = exports.schemaProperties = exports.allSchemaProperties = exports.noPropertyInData = exports.propertyInData = exports.isOwnProperty = exports.hasPropFunc = exports.reportMissingProp = exports.checkMissingProp = exports.checkReportMissingProp = void 0;\nconst codegen_1 = require(\"../compile/codegen\");\nconst util_1 = require(\"../compile/util\");\nconst names_1 = require(\"../compile/names\");\nfunction checkReportMissingProp(cxt, prop) {\n    const { gen, data, it } = cxt;\n    gen.if(noPropertyInData(gen, data, prop, it.opts.ownProperties), () => {\n        cxt.setParams({ missingProperty: codegen_1._ `${prop}` }, true);\n        cxt.error();\n    });\n}\nexports.checkReportMissingProp = checkReportMissingProp;\nfunction checkMissingProp({ gen, data, it: { opts } }, properties, missing) {\n    return codegen_1.or(...properties.map((prop) => codegen_1.and(noPropertyInData(gen, data, prop, opts.ownProperties), codegen_1._ `${missing} = ${prop}`)));\n}\nexports.checkMissingProp = checkMissingProp;\nfunction reportMissingProp(cxt, missing) {\n    cxt.setParams({ missingProperty: missing }, true);\n    cxt.error();\n}\nexports.reportMissingProp = reportMissingProp;\nfunction hasPropFunc(gen) {\n    return gen.scopeValue(\"func\", {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        ref: Object.prototype.hasOwnProperty,\n        code: codegen_1._ `Object.prototype.hasOwnProperty`,\n    });\n}\nexports.hasPropFunc = hasPropFunc;\nfunction isOwnProperty(gen, data, property) {\n    return codegen_1._ `${hasPropFunc(gen)}.call(${data}, ${property})`;\n}\nexports.isOwnProperty = isOwnProperty;\nfunction propertyInData(gen, data, property, ownProperties) {\n    const cond = codegen_1._ `${data}${codegen_1.getProperty(property)} !== undefined`;\n    return ownProperties ? codegen_1._ `${cond} && ${isOwnProperty(gen, data, property)}` : cond;\n}\nexports.propertyInData = propertyInData;\nfunction noPropertyInData(gen, data, property, ownProperties) {\n    const cond = codegen_1._ `${data}${codegen_1.getProperty(property)} === undefined`;\n    return ownProperties ? codegen_1.or(cond, codegen_1.not(isOwnProperty(gen, data, property))) : cond;\n}\nexports.noPropertyInData = noPropertyInData;\nfunction allSchemaProperties(schemaMap) {\n    return schemaMap ? Object.keys(schemaMap).filter((p) => p !== \"__proto__\") : [];\n}\nexports.allSchemaProperties = allSchemaProperties;\nfunction schemaProperties(it, schemaMap) {\n    return allSchemaProperties(schemaMap).filter((p) => !util_1.alwaysValidSchema(it, schemaMap[p]));\n}\nexports.schemaProperties = schemaProperties;\nfunction callValidateCode({ schemaCode, data, it: { gen, topSchemaRef, schemaPath, errorPath }, it }, func, context, passSchema) {\n    const dataAndSchema = passSchema ? codegen_1._ `${schemaCode}, ${data}, ${topSchemaRef}${schemaPath}` : data;\n    const valCxt = [\n        [names_1.default.instancePath, codegen_1.strConcat(names_1.default.instancePath, errorPath)],\n        [names_1.default.parentData, it.parentData],\n        [names_1.default.parentDataProperty, it.parentDataProperty],\n        [names_1.default.rootData, names_1.default.rootData],\n    ];\n    if (it.opts.dynamicRef)\n        valCxt.push([names_1.default.dynamicAnchors, names_1.default.dynamicAnchors]);\n    const args = codegen_1._ `${dataAndSchema}, ${gen.object(...valCxt)}`;\n    return context !== codegen_1.nil ? codegen_1._ `${func}.call(${context}, ${args})` : codegen_1._ `${func}(${args})`;\n}\nexports.callValidateCode = callValidateCode;\nfunction usePattern({ gen, it: { opts } }, pattern) {\n    const u = opts.unicodeRegExp ? \"u\" : \"\";\n    return gen.scopeValue(\"pattern\", {\n        key: pattern,\n        ref: new RegExp(pattern, u),\n        code: codegen_1._ `new RegExp(${pattern}, ${u})`,\n    });\n}\nexports.usePattern = usePattern;\nfunction validateArray(cxt) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    if (it.allErrors) {\n        const validArr = gen.let(\"valid\", true);\n        validateItems(() => gen.assign(validArr, false));\n        return validArr;\n    }\n    gen.var(valid, true);\n    validateItems(() => gen.break());\n    return valid;\n    function validateItems(notValid) {\n        const len = gen.const(\"len\", codegen_1._ `${data}.length`);\n        gen.forRange(\"i\", 0, len, (i) => {\n            cxt.subschema({\n                keyword,\n                dataProp: i,\n                dataPropType: util_1.Type.Num,\n            }, valid);\n            gen.if(codegen_1.not(valid), notValid);\n        });\n    }\n}\nexports.validateArray = validateArray;\nfunction validateUnion(cxt) {\n    const { gen, schema, keyword, it } = cxt;\n    /* istanbul ignore if */\n    if (!Array.isArray(schema))\n        throw new Error(\"ajv implementation error\");\n    const alwaysValid = schema.some((sch) => util_1.alwaysValidSchema(it, sch));\n    if (alwaysValid && !it.opts.unevaluated)\n        return;\n    const valid = gen.let(\"valid\", false);\n    const schValid = gen.name(\"_valid\");\n    gen.block(() => schema.forEach((_sch, i) => {\n        const schCxt = cxt.subschema({\n            keyword,\n            schemaProp: i,\n            compositeRule: true,\n        }, schValid);\n        gen.assign(valid, codegen_1._ `${valid} || ${schValid}`);\n        const merged = cxt.mergeValidEvaluated(schCxt, schValid);\n        // can short-circuit if `unevaluatedProperties/Items` not supported (opts.unevaluated !== true)\n        // or if all properties and items were evaluated (it.props === true && it.items === true)\n        if (!merged)\n            gen.if(codegen_1.not(valid));\n    }));\n    cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n}\nexports.validateUnion = validateUnion;\n//# sourceMappingURL=code.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendSubschemaMode = exports.extendSubschemaData = exports.getSubschema = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction getSubschema(it, { keyword, schemaProp, schema, schemaPath, errSchemaPath, topSchemaRef }) {\n    if (keyword !== undefined && schema !== undefined) {\n        throw new Error('both \"keyword\" and \"schema\" passed, only one allowed');\n    }\n    if (keyword !== undefined) {\n        const sch = it.schema[keyword];\n        return schemaProp === undefined\n            ? {\n                schema: sch,\n                schemaPath: codegen_1._ `${it.schemaPath}${codegen_1.getProperty(keyword)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n            }\n            : {\n                schema: sch[schemaProp],\n                schemaPath: codegen_1._ `${it.schemaPath}${codegen_1.getProperty(keyword)}${codegen_1.getProperty(schemaProp)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}/${util_1.escapeFragment(schemaProp)}`,\n            };\n    }\n    if (schema !== undefined) {\n        if (schemaPath === undefined || errSchemaPath === undefined || topSchemaRef === undefined) {\n            throw new Error('\"schemaPath\", \"errSchemaPath\" and \"topSchemaRef\" are required with \"schema\"');\n        }\n        return {\n            schema,\n            schemaPath,\n            topSchemaRef,\n            errSchemaPath,\n        };\n    }\n    throw new Error('either \"keyword\" or \"schema\" must be passed');\n}\nexports.getSubschema = getSubschema;\nfunction extendSubschemaData(subschema, it, { dataProp, dataPropType: dpType, data, dataTypes, propertyName }) {\n    if (data !== undefined && dataProp !== undefined) {\n        throw new Error('both \"data\" and \"dataProp\" passed, only one allowed');\n    }\n    const { gen } = it;\n    if (dataProp !== undefined) {\n        const { errorPath, dataPathArr, opts } = it;\n        const nextData = gen.let(\"data\", codegen_1._ `${it.data}${codegen_1.getProperty(dataProp)}`, true);\n        dataContextProps(nextData);\n        subschema.errorPath = codegen_1.str `${errorPath}${util_1.getErrorPath(dataProp, dpType, opts.jsPropertySyntax)}`;\n        subschema.parentDataProperty = codegen_1._ `${dataProp}`;\n        subschema.dataPathArr = [...dataPathArr, subschema.parentDataProperty];\n    }\n    if (data !== undefined) {\n        const nextData = data instanceof codegen_1.Name ? data : gen.let(\"data\", data, true); // replaceable if used once?\n        dataContextProps(nextData);\n        if (propertyName !== undefined)\n            subschema.propertyName = propertyName;\n        // TODO something is possibly wrong here with not changing parentDataProperty and not appending dataPathArr\n    }\n    if (dataTypes)\n        subschema.dataTypes = dataTypes;\n    function dataContextProps(_nextData) {\n        subschema.data = _nextData;\n        subschema.dataLevel = it.dataLevel + 1;\n        subschema.dataTypes = [];\n        it.definedProperties = new Set();\n        subschema.parentData = it.data;\n        subschema.dataNames = [...it.dataNames, _nextData];\n    }\n}\nexports.extendSubschemaData = extendSubschemaData;\nfunction extendSubschemaMode(subschema, { jtdDiscriminator, jtdMetadata, compositeRule, createErrors, allErrors }) {\n    if (compositeRule !== undefined)\n        subschema.compositeRule = compositeRule;\n    if (createErrors !== undefined)\n        subschema.createErrors = createErrors;\n    if (allErrors !== undefined)\n        subschema.allErrors = allErrors;\n    subschema.jtdDiscriminator = jtdDiscriminator; // not inherited\n    subschema.jtdMetadata = jtdMetadata; // not inherited\n}\nexports.extendSubschemaMode = extendSubschemaMode;\n//# sourceMappingURL=subschema.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getSchemaRefs = exports.resolveUrl = exports.normalizeId = exports._getFullPath = exports.getFullPath = exports.inlineRef = void 0;\nconst util_1 = require(\"./util\");\nconst equal = require(\"fast-deep-equal\");\nconst traverse = require(\"json-schema-traverse\");\nconst URI = require(\"uri-js\");\n// TODO refactor to use keyword definitions\nconst SIMPLE_INLINED = new Set([\n    \"type\",\n    \"format\",\n    \"pattern\",\n    \"maxLength\",\n    \"minLength\",\n    \"maxProperties\",\n    \"minProperties\",\n    \"maxItems\",\n    \"minItems\",\n    \"maximum\",\n    \"minimum\",\n    \"uniqueItems\",\n    \"multipleOf\",\n    \"required\",\n    \"enum\",\n    \"const\",\n]);\nfunction inlineRef(schema, limit = true) {\n    if (typeof schema == \"boolean\")\n        return true;\n    if (limit === true)\n        return !hasRef(schema);\n    if (!limit)\n        return false;\n    return countKeys(schema) <= limit;\n}\nexports.inlineRef = inlineRef;\nconst REF_KEYWORDS = new Set([\n    \"$ref\",\n    \"$recursiveRef\",\n    \"$recursiveAnchor\",\n    \"$dynamicRef\",\n    \"$dynamicAnchor\",\n]);\nfunction hasRef(schema) {\n    for (const key in schema) {\n        if (REF_KEYWORDS.has(key))\n            return true;\n        const sch = schema[key];\n        if (Array.isArray(sch) && sch.some(hasRef))\n            return true;\n        if (typeof sch == \"object\" && hasRef(sch))\n            return true;\n    }\n    return false;\n}\nfunction countKeys(schema) {\n    let count = 0;\n    for (const key in schema) {\n        if (key === \"$ref\")\n            return Infinity;\n        count++;\n        if (SIMPLE_INLINED.has(key))\n            continue;\n        if (typeof schema[key] == \"object\") {\n            util_1.eachItem(schema[key], (sch) => (count += countKeys(sch)));\n        }\n        if (count === Infinity)\n            return Infinity;\n    }\n    return count;\n}\nfunction getFullPath(id = \"\", normalize) {\n    if (normalize !== false)\n        id = normalizeId(id);\n    const p = URI.parse(id);\n    return _getFullPath(p);\n}\nexports.getFullPath = getFullPath;\nfunction _getFullPath(p) {\n    return URI.serialize(p).split(\"#\")[0] + \"#\";\n}\nexports._getFullPath = _getFullPath;\nconst TRAILING_SLASH_HASH = /#\\/?$/;\nfunction normalizeId(id) {\n    return id ? id.replace(TRAILING_SLASH_HASH, \"\") : \"\";\n}\nexports.normalizeId = normalizeId;\nfunction resolveUrl(baseId, id) {\n    id = normalizeId(id);\n    return URI.resolve(baseId, id);\n}\nexports.resolveUrl = resolveUrl;\nconst ANCHOR = /^[a-z_][-a-z0-9._]*$/i;\nfunction getSchemaRefs(schema) {\n    if (typeof schema == \"boolean\")\n        return {};\n    const schemaId = normalizeId(schema.$id);\n    const baseIds = { \"\": schemaId };\n    const pathPrefix = getFullPath(schemaId, false);\n    const localRefs = {};\n    const schemaRefs = new Set();\n    traverse(schema, { allKeys: true }, (sch, jsonPtr, _, parentJsonPtr) => {\n        if (parentJsonPtr === undefined)\n            return;\n        const fullPath = pathPrefix + jsonPtr;\n        let baseId = baseIds[parentJsonPtr];\n        if (typeof sch.$id == \"string\")\n            baseId = addRef.call(this, sch.$id);\n        addAnchor.call(this, sch.$anchor);\n        addAnchor.call(this, sch.$dynamicAnchor);\n        baseIds[jsonPtr] = baseId;\n        function addRef(ref) {\n            ref = normalizeId(baseId ? URI.resolve(baseId, ref) : ref);\n            if (schemaRefs.has(ref))\n                throw ambiguos(ref);\n            schemaRefs.add(ref);\n            let schOrRef = this.refs[ref];\n            if (typeof schOrRef == \"string\")\n                schOrRef = this.refs[schOrRef];\n            if (typeof schOrRef == \"object\") {\n                checkAmbiguosRef(sch, schOrRef.schema, ref);\n            }\n            else if (ref !== normalizeId(fullPath)) {\n                if (ref[0] === \"#\") {\n                    checkAmbiguosRef(sch, localRefs[ref], ref);\n                    localRefs[ref] = sch;\n                }\n                else {\n                    this.refs[ref] = fullPath;\n                }\n            }\n            return ref;\n        }\n        function addAnchor(anchor) {\n            if (typeof anchor == \"string\") {\n                if (!ANCHOR.test(anchor))\n                    throw new Error(`invalid anchor \"${anchor}\"`);\n                addRef.call(this, `#${anchor}`);\n            }\n        }\n    });\n    return localRefs;\n    function checkAmbiguosRef(sch1, sch2, ref) {\n        if (sch2 !== undefined && !equal(sch1, sch2))\n            throw ambiguos(ref);\n    }\n    function ambiguos(ref) {\n        return new Error(`reference \"${ref}\" resolves to more than one schema`);\n    }\n}\nexports.getSchemaRefs = getSchemaRefs;\n//# sourceMappingURL=resolve.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass ValidationError extends Error {\n    constructor(errors) {\n        super(\"validation failed\");\n        this.errors = errors;\n        this.ajv = this.validation = true;\n    }\n}\nexports.default = ValidationError;\n//# sourceMappingURL=validation_error.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst resolve_1 = require(\"./resolve\");\nclass MissingRefError extends Error {\n    constructor(baseId, ref, msg) {\n        super(msg || `can't resolve reference ${ref} from id ${baseId}`);\n        this.missingRef = resolve_1.resolveUrl(baseId, ref);\n        this.missingSchema = resolve_1.normalizeId(resolve_1.getFullPath(this.missingRef));\n    }\n}\nexports.default = MissingRefError;\n//# sourceMappingURL=ref_error.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.resolveSchema = exports.getCompilingSchema = exports.resolveRef = exports.compileSchema = exports.SchemaEnv = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst validation_error_1 = require(\"../runtime/validation_error\");\nconst names_1 = require(\"./names\");\nconst resolve_1 = require(\"./resolve\");\nconst util_1 = require(\"./util\");\nconst validate_1 = require(\"./validate\");\nconst URI = require(\"uri-js\");\nclass SchemaEnv {\n    constructor(env) {\n        var _a;\n        this.refs = {};\n        this.dynamicAnchors = {};\n        let schema;\n        if (typeof env.schema == \"object\")\n            schema = env.schema;\n        this.schema = env.schema;\n        this.root = env.root || this;\n        this.baseId = (_a = env.baseId) !== null && _a !== void 0 ? _a : resolve_1.normalizeId(schema === null || schema === void 0 ? void 0 : schema.$id);\n        this.schemaPath = env.schemaPath;\n        this.localRefs = env.localRefs;\n        this.meta = env.meta;\n        this.$async = schema === null || schema === void 0 ? void 0 : schema.$async;\n        this.refs = {};\n    }\n}\nexports.SchemaEnv = SchemaEnv;\n// let codeSize = 0\n// let nodeCount = 0\n// Compiles schema in SchemaEnv\nfunction compileSchema(sch) {\n    // TODO refactor - remove compilations\n    const _sch = getCompilingSchema.call(this, sch);\n    if (_sch)\n        return _sch;\n    const rootId = resolve_1.getFullPath(sch.root.baseId); // TODO if getFullPath removed 1 tests fails\n    const { es5, lines } = this.opts.code;\n    const { ownProperties } = this.opts;\n    const gen = new codegen_1.CodeGen(this.scope, { es5, lines, ownProperties });\n    let _ValidationError;\n    if (sch.$async) {\n        _ValidationError = gen.scopeValue(\"Error\", {\n            ref: validation_error_1.default,\n            code: codegen_1._ `require(\"ajv/dist/runtime/validation_error\").default`,\n        });\n    }\n    const validateName = gen.scopeName(\"validate\");\n    sch.validateName = validateName;\n    const schemaCxt = {\n        gen,\n        allErrors: this.opts.allErrors,\n        data: names_1.default.data,\n        parentData: names_1.default.parentData,\n        parentDataProperty: names_1.default.parentDataProperty,\n        dataNames: [names_1.default.data],\n        dataPathArr: [codegen_1.nil],\n        dataLevel: 0,\n        dataTypes: [],\n        definedProperties: new Set(),\n        topSchemaRef: gen.scopeValue(\"schema\", this.opts.code.source === true\n            ? { ref: sch.schema, code: codegen_1.stringify(sch.schema) }\n            : { ref: sch.schema }),\n        validateName,\n        ValidationError: _ValidationError,\n        schema: sch.schema,\n        schemaEnv: sch,\n        rootId,\n        baseId: sch.baseId || rootId,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: sch.schemaPath || (this.opts.jtd ? \"\" : \"#\"),\n        errorPath: codegen_1._ `\"\"`,\n        opts: this.opts,\n        self: this,\n    };\n    let sourceCode;\n    try {\n        this._compilations.add(sch);\n        validate_1.validateFunctionCode(schemaCxt);\n        gen.optimize(this.opts.code.optimize);\n        // gen.optimize(1)\n        const validateCode = gen.toString();\n        sourceCode = `${gen.scopeRefs(names_1.default.scope)}return ${validateCode}`;\n        // console.log((codeSize += sourceCode.length), (nodeCount += gen.nodeCount))\n        if (this.opts.code.process)\n            sourceCode = this.opts.code.process(sourceCode, sch);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode)\n        const makeValidate = new Function(`${names_1.default.self}`, `${names_1.default.scope}`, sourceCode);\n        const validate = makeValidate(this, this.scope.get());\n        this.scope.value(validateName, { ref: validate });\n        validate.errors = null;\n        validate.schema = sch.schema;\n        validate.schemaEnv = sch;\n        if (sch.$async)\n            validate.$async = true;\n        if (this.opts.code.source === true) {\n            validate.source = { validateName, validateCode, scopeValues: gen._values };\n        }\n        if (this.opts.unevaluated) {\n            const { props, items } = schemaCxt;\n            validate.evaluated = {\n                props: props instanceof codegen_1.Name ? undefined : props,\n                items: items instanceof codegen_1.Name ? undefined : items,\n                dynamicProps: props instanceof codegen_1.Name,\n                dynamicItems: items instanceof codegen_1.Name,\n            };\n            if (validate.source)\n                validate.source.evaluated = codegen_1.stringify(validate.evaluated);\n        }\n        sch.validate = validate;\n        return sch;\n    }\n    catch (e) {\n        delete sch.validate;\n        delete sch.validateName;\n        if (sourceCode)\n            this.logger.error(\"Error compiling schema, function code:\", sourceCode);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode, this.opts)\n        throw e;\n    }\n    finally {\n        this._compilations.delete(sch);\n    }\n}\nexports.compileSchema = compileSchema;\nfunction resolveRef(root, baseId, ref) {\n    var _a;\n    ref = resolve_1.resolveUrl(baseId, ref);\n    const schOrFunc = root.refs[ref];\n    if (schOrFunc)\n        return schOrFunc;\n    let _sch = resolve.call(this, root, ref);\n    if (_sch === undefined) {\n        const schema = (_a = root.localRefs) === null || _a === void 0 ? void 0 : _a[ref]; // TODO maybe localRefs should hold SchemaEnv\n        if (schema)\n            _sch = new SchemaEnv({ schema, root, baseId });\n    }\n    if (_sch === undefined)\n        return;\n    return (root.refs[ref] = inlineOrCompile.call(this, _sch));\n}\nexports.resolveRef = resolveRef;\nfunction inlineOrCompile(sch) {\n    if (resolve_1.inlineRef(sch.schema, this.opts.inlineRefs))\n        return sch.schema;\n    return sch.validate ? sch : compileSchema.call(this, sch);\n}\n// Index of schema compilation in the currently compiled list\nfunction getCompilingSchema(schEnv) {\n    for (const sch of this._compilations) {\n        if (sameSchemaEnv(sch, schEnv))\n            return sch;\n    }\n}\nexports.getCompilingSchema = getCompilingSchema;\nfunction sameSchemaEnv(s1, s2) {\n    return s1.schema === s2.schema && s1.root === s2.root && s1.baseId === s2.baseId;\n}\n// resolve and compile the references ($ref)\n// TODO returns AnySchemaObject (if the schema can be inlined) or validation function\nfunction resolve(root, // information about the root schema for the current schema\nref // reference to resolve\n) {\n    let sch;\n    while (typeof (sch = this.refs[ref]) == \"string\")\n        ref = sch;\n    return sch || this.schemas[ref] || resolveSchema.call(this, root, ref);\n}\n// Resolve schema, its root and baseId\nfunction resolveSchema(root, // root object with properties schema, refs TODO below SchemaEnv is assigned to it\nref // reference to resolve\n) {\n    const p = URI.parse(ref);\n    const refPath = resolve_1._getFullPath(p);\n    let baseId = resolve_1.getFullPath(root.baseId);\n    // TODO `Object.keys(root.schema).length > 0` should not be needed - but removing breaks 2 tests\n    if (Object.keys(root.schema).length > 0 && refPath === baseId) {\n        return getJsonPointer.call(this, p, root);\n    }\n    const id = resolve_1.normalizeId(refPath);\n    const schOrRef = this.refs[id] || this.schemas[id];\n    if (typeof schOrRef == \"string\") {\n        const sch = resolveSchema.call(this, root, schOrRef);\n        if (typeof (sch === null || sch === void 0 ? void 0 : sch.schema) !== \"object\")\n            return;\n        return getJsonPointer.call(this, p, sch);\n    }\n    if (typeof (schOrRef === null || schOrRef === void 0 ? void 0 : schOrRef.schema) !== \"object\")\n        return;\n    if (!schOrRef.validate)\n        compileSchema.call(this, schOrRef);\n    if (id === resolve_1.normalizeId(ref)) {\n        const { schema } = schOrRef;\n        if (schema.$id)\n            baseId = resolve_1.resolveUrl(baseId, schema.$id);\n        return new SchemaEnv({ schema, root, baseId });\n    }\n    return getJsonPointer.call(this, p, schOrRef);\n}\nexports.resolveSchema = resolveSchema;\nconst PREVENT_SCOPE_CHANGE = new Set([\n    \"properties\",\n    \"patternProperties\",\n    \"enum\",\n    \"dependencies\",\n    \"definitions\",\n]);\nfunction getJsonPointer(parsedRef, { baseId, schema, root }) {\n    var _a;\n    if (((_a = parsedRef.fragment) === null || _a === void 0 ? void 0 : _a[0]) !== \"/\")\n        return;\n    for (const part of parsedRef.fragment.slice(1).split(\"/\")) {\n        if (typeof schema == \"boolean\")\n            return;\n        schema = schema[util_1.unescapeFragment(part)];\n        if (schema === undefined)\n            return;\n        // TODO PREVENT_SCOPE_CHANGE could be defined in keyword def?\n        if (!PREVENT_SCOPE_CHANGE.has(part) && typeof schema == \"object\" && schema.$id) {\n            baseId = resolve_1.resolveUrl(baseId, schema.$id);\n        }\n    }\n    let env;\n    if (typeof schema != \"boolean\" && schema.$ref && !util_1.schemaHasRulesButRef(schema, this.RULES)) {\n        const $ref = resolve_1.resolveUrl(baseId, schema.$ref);\n        env = resolveSchema.call(this, root, $ref);\n    }\n    // even though resolution failed we need to return SchemaEnv to throw exception\n    // so that compileAsync loads missing schema.\n    env = env || new SchemaEnv({ schema, root, baseId });\n    if (env.schema !== env.root.schema)\n        return env;\n    return undefined;\n}\n//# sourceMappingURL=index.js.map", "module.exports = {\n  \"$id\": \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n  \"description\": \"Meta-schema for $data reference (JSON AnySchema extension proposal)\",\n  \"type\": \"object\",\n  \"required\": [\"$data\"],\n  \"properties\": {\n    \"$data\": {\n      \"type\": \"string\",\n      \"anyOf\": [{\"format\": \"relative-json-pointer\"}, {\"format\": \"json-pointer\"}]\n    }\n  },\n  \"additionalProperties\": false\n}\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst core_1 = require(\"./core\");\nconst validation_1 = require(\"./validation\");\nconst applicator_1 = require(\"./applicator\");\nconst format_1 = require(\"./format\");\nconst metadata_1 = require(\"./metadata\");\nconst draft7Vocabularies = [\n    core_1.default,\n    validation_1.default,\n    applicator_1.default(),\n    format_1.default,\n    metadata_1.metadataVocabulary,\n    metadata_1.contentVocabulary,\n];\nexports.default = draft7Vocabularies;\n//# sourceMappingURL=draft7.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst id_1 = require(\"./id\");\nconst ref_1 = require(\"./ref\");\nconst core = [\n    \"$schema\",\n    \"$id\",\n    \"$defs\",\n    \"$vocabulary\",\n    { keyword: \"$comment\" },\n    \"definitions\",\n    id_1.default,\n    ref_1.default,\n];\nexports.default = core;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst def = {\n    keyword: \"id\",\n    code() {\n        throw new Error('NOT SUPPORTED: keyword \"id\", use \"$id\" for schema ID');\n    },\n};\nexports.default = def;\n//# sourceMappingURL=id.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callRef = exports.getValidate = void 0;\nconst ref_error_1 = require(\"../../compile/ref_error\");\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst compile_1 = require(\"../../compile\");\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"$ref\",\n    schemaType: \"string\",\n    code(cxt) {\n        const { gen, schema: $ref, it } = cxt;\n        const { baseId, schemaEnv: env, validateName, opts, self } = it;\n        const { root } = env;\n        if (($ref === \"#\" || $ref === \"#/\") && baseId === root.baseId)\n            return callRootRef();\n        const schOrEnv = compile_1.resolveRef.call(self, root, baseId, $ref);\n        if (schOrEnv === undefined)\n            throw new ref_error_1.default(baseId, $ref);\n        if (schOrEnv instanceof compile_1.SchemaEnv)\n            return callValidate(schOrEnv);\n        return inlineRefSchema(schOrEnv);\n        function callRootRef() {\n            if (env === root)\n                return callRef(cxt, validateName, env, env.$async);\n            const rootName = gen.scopeValue(\"root\", { ref: root });\n            return callRef(cxt, codegen_1._ `${rootName}.validate`, root, root.$async);\n        }\n        function callValidate(sch) {\n            const v = getValidate(cxt, sch);\n            callRef(cxt, v, sch, sch.$async);\n        }\n        function inlineRefSchema(sch) {\n            const schName = gen.scopeValue(\"schema\", opts.code.source === true ? { ref: sch, code: codegen_1.stringify(sch) } : { ref: sch });\n            const valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({\n                schema: sch,\n                dataTypes: [],\n                schemaPath: codegen_1.nil,\n                topSchemaRef: schName,\n                errSchemaPath: $ref,\n            }, valid);\n            cxt.mergeEvaluated(schCxt);\n            cxt.ok(valid);\n        }\n    },\n};\nfunction getValidate(cxt, sch) {\n    const { gen } = cxt;\n    return sch.validate\n        ? gen.scopeValue(\"validate\", { ref: sch.validate })\n        : codegen_1._ `${gen.scopeValue(\"wrapper\", { ref: sch })}.validate`;\n}\nexports.getValidate = getValidate;\nfunction callRef(cxt, v, sch, $async) {\n    const { gen, it } = cxt;\n    const { allErrors, schemaEnv: env, opts } = it;\n    const passCxt = opts.passContext ? names_1.default.this : codegen_1.nil;\n    if ($async)\n        callAsyncRef();\n    else\n        callSyncRef();\n    function callAsyncRef() {\n        if (!env.$async)\n            throw new Error(\"async schema referenced by sync schema\");\n        const valid = gen.let(\"valid\");\n        gen.try(() => {\n            gen.code(codegen_1._ `await ${code_1.callValidateCode(cxt, v, passCxt)}`);\n            addEvaluatedFrom(v); // TODO will not work with async, it has to be returned with the result\n            if (!allErrors)\n                gen.assign(valid, true);\n        }, (e) => {\n            gen.if(codegen_1._ `!(${e} instanceof ${it.ValidationError})`, () => gen.throw(e));\n            addErrorsFrom(e);\n            if (!allErrors)\n                gen.assign(valid, false);\n        });\n        cxt.ok(valid);\n    }\n    function callSyncRef() {\n        cxt.result(code_1.callValidateCode(cxt, v, passCxt), () => addEvaluatedFrom(v), () => addErrorsFrom(v));\n    }\n    function addErrorsFrom(source) {\n        const errs = codegen_1._ `${source}.errors`;\n        gen.assign(names_1.default.vErrors, codegen_1._ `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`); // TODO tagged\n        gen.assign(names_1.default.errors, codegen_1._ `${names_1.default.vErrors}.length`);\n    }\n    function addEvaluatedFrom(source) {\n        var _a;\n        if (!it.opts.unevaluated)\n            return;\n        const schEvaluated = (_a = sch === null || sch === void 0 ? void 0 : sch.validate) === null || _a === void 0 ? void 0 : _a.evaluated;\n        // TODO refactor\n        if (it.props !== true) {\n            if (schEvaluated && !schEvaluated.dynamicProps) {\n                if (schEvaluated.props !== undefined) {\n                    it.props = util_1.mergeEvaluated.props(gen, schEvaluated.props, it.props);\n                }\n            }\n            else {\n                const props = gen.var(\"props\", codegen_1._ `${source}.evaluated.props`);\n                it.props = util_1.mergeEvaluated.props(gen, props, it.props, codegen_1.Name);\n            }\n        }\n        if (it.items !== true) {\n            if (schEvaluated && !schEvaluated.dynamicItems) {\n                if (schEvaluated.items !== undefined) {\n                    it.items = util_1.mergeEvaluated.items(gen, schEvaluated.items, it.items);\n                }\n            }\n            else {\n                const items = gen.var(\"items\", codegen_1._ `${source}.evaluated.items`);\n                it.items = util_1.mergeEvaluated.items(gen, items, it.items, codegen_1.Name);\n            }\n        }\n    }\n}\nexports.callRef = callRef;\nexports.default = def;\n//# sourceMappingURL=ref.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst limitNumber_1 = require(\"./limitNumber\");\nconst multipleOf_1 = require(\"./multipleOf\");\nconst limitLength_1 = require(\"./limitLength\");\nconst pattern_1 = require(\"./pattern\");\nconst limitProperties_1 = require(\"./limitProperties\");\nconst required_1 = require(\"./required\");\nconst limitItems_1 = require(\"./limitItems\");\nconst uniqueItems_1 = require(\"./uniqueItems\");\nconst const_1 = require(\"./const\");\nconst enum_1 = require(\"./enum\");\nconst validation = [\n    // number\n    limitNumber_1.default,\n    multipleOf_1.default,\n    // string\n    limitLength_1.default,\n    pattern_1.default,\n    // object\n    limitProperties_1.default,\n    required_1.default,\n    // array\n    limitItems_1.default,\n    uniqueItems_1.default,\n    // any\n    { keyword: \"type\", schemaType: [\"string\", \"array\"] },\n    { keyword: \"nullable\", schemaType: \"boolean\" },\n    const_1.default,\n    enum_1.default,\n];\nexports.default = validation;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst ops = codegen_1.operators;\nconst KWDs = {\n    maximum: { okStr: \"<=\", ok: ops.LTE, fail: ops.GT },\n    minimum: { okStr: \">=\", ok: ops.GTE, fail: ops.LT },\n    exclusiveMaximum: { okStr: \"<\", ok: ops.LT, fail: ops.GTE },\n    exclusiveMinimum: { okStr: \">\", ok: ops.GT, fail: ops.LTE },\n};\nconst error = {\n    message: ({ keyword, schemaCode }) => codegen_1.str `must be ${KWDs[keyword].okStr} ${schemaCode}`,\n    params: ({ keyword, schemaCode }) => codegen_1._ `{comparison: ${KWDs[keyword].okStr}, limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: Object.keys(KWDs),\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        cxt.fail$data(codegen_1._ `${data} ${KWDs[keyword].fail} ${schemaCode} || isNaN(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitNumber.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => codegen_1.str `must be multiple of ${schemaCode}`,\n    params: ({ schemaCode }) => codegen_1._ `{multipleOf: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"multipleOf\",\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, schemaCode, it } = cxt;\n        // const bdt = bad$DataType(schemaCode, <string>def.schemaType, $data)\n        const prec = it.opts.multipleOfPrecision;\n        const res = gen.let(\"res\");\n        const invalid = prec\n            ? codegen_1._ `Math.abs(Math.round(${res}) - ${res}) > 1e-${prec}`\n            : codegen_1._ `${res} !== parseInt(${res})`;\n        cxt.fail$data(codegen_1._ `(${schemaCode} === 0 || (${res} = ${data}/${schemaCode}, ${invalid}))`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=multipleOf.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst ucs2length_1 = require(\"../../runtime/ucs2length\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxLength\" ? \"more\" : \"fewer\";\n        return codegen_1.str `must NOT have ${comp} than ${schemaCode} characters`;\n    },\n    params: ({ schemaCode }) => codegen_1._ `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxLength\", \"minLength\"],\n    type: \"string\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode, it } = cxt;\n        const op = keyword === \"maxLength\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        const len = it.opts.unicode === false ? codegen_1._ `${data}.length` : codegen_1._ `${util_1.useFunc(cxt.gen, ucs2length_1.default)}(${data})`;\n        cxt.fail$data(codegen_1._ `${len} ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitLength.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://mathiasbynens.be/notes/javascript-encoding\n// https://github.com/bestiejs/punycode.js - punycode.ucs2.decode\nfunction ucs2length(str) {\n    const len = str.length;\n    let length = 0;\n    let pos = 0;\n    let value;\n    while (pos < len) {\n        length++;\n        value = str.charCodeAt(pos++);\n        if (value >= 0xd800 && value <= 0xdbff && pos < len) {\n            // high surrogate, and there is a next character\n            value = str.charCodeAt(pos);\n            if ((value & 0xfc00) === 0xdc00)\n                pos++; // low surrogate\n        }\n    }\n    return length;\n}\nexports.default = ucs2length;\nucs2length.code = 'require(\"ajv/dist/runtime/ucs2length\").default';\n//# sourceMappingURL=ucs2length.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => codegen_1.str `must match pattern \"${schemaCode}\"`,\n    params: ({ schemaCode }) => codegen_1._ `{pattern: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"pattern\",\n    type: \"string\",\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { data, $data, schema, schemaCode, it } = cxt;\n        // TODO regexp should be wrapped in try/catchs\n        const u = it.opts.unicodeRegExp ? \"u\" : \"\";\n        const regExp = $data ? codegen_1._ `(new RegExp(${schemaCode}, ${u}))` : code_1.usePattern(cxt, schema);\n        cxt.fail$data(codegen_1._ `!${regExp}.test(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=pattern.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxProperties\" ? \"more\" : \"fewer\";\n        return codegen_1.str `must NOT have ${comp} than ${schemaCode} items`;\n    },\n    params: ({ schemaCode }) => codegen_1._ `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxProperties\", \"minProperties\"],\n    type: \"object\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxProperties\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data(codegen_1._ `Object.keys(${data}).length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitProperties.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { missingProperty } }) => codegen_1.str `must have required property '${missingProperty}'`,\n    params: ({ params: { missingProperty } }) => codegen_1._ `{missingProperty: ${missingProperty}}`,\n};\nconst def = {\n    keyword: \"required\",\n    type: \"object\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, schema, schemaCode, data, $data, it } = cxt;\n        const { opts } = it;\n        if (!$data && schema.length === 0)\n            return;\n        const useLoop = schema.length >= opts.loopRequired;\n        if (it.allErrors)\n            allErrorsMode();\n        else\n            exitOnErrorMode();\n        if (opts.strictRequired) {\n            const props = cxt.parentSchema.properties;\n            const { definedProperties } = cxt.it;\n            for (const requiredKey of schema) {\n                if ((props === null || props === void 0 ? void 0 : props[requiredKey]) === undefined && !definedProperties.has(requiredKey)) {\n                    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n                    const msg = `required property \"${requiredKey}\" is not defined at \"${schemaPath}\" (strictRequired)`;\n                    util_1.checkStrictMode(it, msg, it.opts.strictRequired);\n                }\n            }\n        }\n        function allErrorsMode() {\n            if (useLoop || $data) {\n                cxt.block$data(codegen_1.nil, loopAllRequired);\n            }\n            else {\n                for (const prop of schema) {\n                    code_1.checkReportMissingProp(cxt, prop);\n                }\n            }\n        }\n        function exitOnErrorMode() {\n            const missing = gen.let(\"missing\");\n            if (useLoop || $data) {\n                const valid = gen.let(\"valid\", true);\n                cxt.block$data(valid, () => loopUntilMissing(missing, valid));\n                cxt.ok(valid);\n            }\n            else {\n                gen.if(code_1.checkMissingProp(cxt, schema, missing));\n                code_1.reportMissingProp(cxt, missing);\n                gen.else();\n            }\n        }\n        function loopAllRequired() {\n            gen.forOf(\"prop\", schemaCode, (prop) => {\n                cxt.setParams({ missingProperty: prop });\n                gen.if(code_1.noPropertyInData(gen, data, prop, opts.ownProperties), () => cxt.error());\n            });\n        }\n        function loopUntilMissing(missing, valid) {\n            cxt.setParams({ missingProperty: missing });\n            gen.forOf(missing, schemaCode, () => {\n                gen.assign(valid, code_1.propertyInData(gen, data, missing, opts.ownProperties));\n                gen.if(codegen_1.not(valid), () => {\n                    cxt.error();\n                    gen.break();\n                });\n            }, codegen_1.nil);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=required.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxItems\" ? \"more\" : \"fewer\";\n        return codegen_1.str `must NOT have ${comp} than ${schemaCode} items`;\n    },\n    params: ({ schemaCode }) => codegen_1._ `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxItems\", \"minItems\"],\n    type: \"array\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxItems\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data(codegen_1._ `${data}.length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitItems.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst dataType_1 = require(\"../../compile/validate/dataType\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: ({ params: { i, j } }) => codegen_1.str `must NOT have duplicate items (items ## ${j} and ${i} are identical)`,\n    params: ({ params: { i, j } }) => codegen_1._ `{i: ${i}, j: ${j}}`,\n};\nconst def = {\n    keyword: \"uniqueItems\",\n    type: \"array\",\n    schemaType: \"boolean\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, parentSchema, schemaCode, it } = cxt;\n        if (!$data && !schema)\n            return;\n        const valid = gen.let(\"valid\");\n        const itemTypes = parentSchema.items ? dataType_1.getSchemaTypes(parentSchema.items) : [];\n        cxt.block$data(valid, validateUniqueItems, codegen_1._ `${schemaCode} === false`);\n        cxt.ok(valid);\n        function validateUniqueItems() {\n            const i = gen.let(\"i\", codegen_1._ `${data}.length`);\n            const j = gen.let(\"j\");\n            cxt.setParams({ i, j });\n            gen.assign(valid, true);\n            gen.if(codegen_1._ `${i} > 1`, () => (canOptimize() ? loopN : loopN2)(i, j));\n        }\n        function canOptimize() {\n            return itemTypes.length > 0 && !itemTypes.some((t) => t === \"object\" || t === \"array\");\n        }\n        function loopN(i, j) {\n            const item = gen.name(\"item\");\n            const wrongType = dataType_1.checkDataTypes(itemTypes, item, it.opts.strictNumbers, dataType_1.DataType.Wrong);\n            const indices = gen.const(\"indices\", codegen_1._ `{}`);\n            gen.for(codegen_1._ `;${i}--;`, () => {\n                gen.let(item, codegen_1._ `${data}[${i}]`);\n                gen.if(wrongType, codegen_1._ `continue`);\n                if (itemTypes.length > 1)\n                    gen.if(codegen_1._ `typeof ${item} == \"string\"`, codegen_1._ `${item} += \"_\"`);\n                gen\n                    .if(codegen_1._ `typeof ${indices}[${item}] == \"number\"`, () => {\n                    gen.assign(j, codegen_1._ `${indices}[${item}]`);\n                    cxt.error();\n                    gen.assign(valid, false).break();\n                })\n                    .code(codegen_1._ `${indices}[${item}] = ${i}`);\n            });\n        }\n        function loopN2(i, j) {\n            const eql = util_1.useFunc(gen, equal_1.default);\n            const outer = gen.name(\"outer\");\n            gen.label(outer).for(codegen_1._ `;${i}--;`, () => gen.for(codegen_1._ `${j} = ${i}; ${j}--;`, () => gen.if(codegen_1._ `${eql}(${data}[${i}], ${data}[${j}])`, () => {\n                cxt.error();\n                gen.assign(valid, false).break(outer);\n            })));\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=uniqueItems.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://github.com/ajv-validator/ajv/issues/889\nconst equal = require(\"fast-deep-equal\");\nequal.code = 'require(\"ajv/dist/runtime/equal\").default';\nexports.default = equal;\n//# sourceMappingURL=equal.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to constant\",\n    params: ({ schemaCode }) => codegen_1._ `{allowedValue: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"const\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, schemaCode } = cxt;\n        // TODO optimize for scalar values in schema\n        cxt.fail$data(codegen_1._ `!${util_1.useFunc(gen, equal_1.default)}(${data}, ${schemaCode})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=const.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to one of the allowed values\",\n    params: ({ schemaCode }) => codegen_1._ `{allowedValues: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"enum\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        if (!$data && schema.length === 0)\n            throw new Error(\"enum must have non-empty array\");\n        const useLoop = schema.length >= it.opts.loopEnum;\n        const eql = util_1.useFunc(gen, equal_1.default);\n        let valid;\n        if (useLoop || $data) {\n            valid = gen.let(\"valid\");\n            cxt.block$data(valid, loopEnum);\n        }\n        else {\n            /* istanbul ignore if */\n            if (!Array.isArray(schema))\n                throw new Error(\"ajv implementation error\");\n            const vSchema = gen.const(\"vSchema\", schemaCode);\n            valid = codegen_1.or(...schema.map((_x, i) => equalCode(vSchema, i)));\n        }\n        cxt.pass(valid);\n        function loopEnum() {\n            gen.assign(valid, false);\n            gen.forOf(\"v\", schemaCode, (v) => gen.if(codegen_1._ `${eql}(${data}, ${v})`, () => gen.assign(valid, true).break()));\n        }\n        function equalCode(vSchema, i) {\n            const sch = schema[i];\n            return sch && typeof sch === \"object\"\n                ? codegen_1._ `${eql}(${data}, ${vSchema}[${i}])`\n                : codegen_1._ `${data} === ${sch}`;\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=enum.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst additionalItems_1 = require(\"./additionalItems\");\nconst prefixItems_1 = require(\"./prefixItems\");\nconst items_1 = require(\"./items\");\nconst items2020_1 = require(\"./items2020\");\nconst contains_1 = require(\"./contains\");\nconst dependencies_1 = require(\"./dependencies\");\nconst propertyNames_1 = require(\"./propertyNames\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst properties_1 = require(\"./properties\");\nconst patternProperties_1 = require(\"./patternProperties\");\nconst not_1 = require(\"./not\");\nconst anyOf_1 = require(\"./anyOf\");\nconst oneOf_1 = require(\"./oneOf\");\nconst allOf_1 = require(\"./allOf\");\nconst if_1 = require(\"./if\");\nconst thenElse_1 = require(\"./thenElse\");\nfunction getApplicator(draft2020 = false) {\n    const applicator = [\n        // any\n        not_1.default,\n        anyOf_1.default,\n        oneOf_1.default,\n        allOf_1.default,\n        if_1.default,\n        thenElse_1.default,\n        // object\n        propertyNames_1.default,\n        additionalProperties_1.default,\n        dependencies_1.default,\n        properties_1.default,\n        patternProperties_1.default,\n    ];\n    // array\n    if (draft2020)\n        applicator.push(prefixItems_1.default, items2020_1.default);\n    else\n        applicator.push(additionalItems_1.default, items_1.default);\n    applicator.push(contains_1.default);\n    return applicator;\n}\nexports.default = getApplicator;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateAdditionalItems = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { len } }) => codegen_1.str `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => codegen_1._ `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"additionalItems\",\n    type: \"array\",\n    schemaType: [\"boolean\", \"object\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { parentSchema, it } = cxt;\n        const { items } = parentSchema;\n        if (!Array.isArray(items)) {\n            util_1.checkStrictMode(it, '\"additionalItems\" is ignored when \"items\" is not an array of schemas');\n            return;\n        }\n        validateAdditionalItems(cxt, items);\n    },\n};\nfunction validateAdditionalItems(cxt, items) {\n    const { gen, schema, data, keyword, it } = cxt;\n    it.items = true;\n    const len = gen.const(\"len\", codegen_1._ `${data}.length`);\n    if (schema === false) {\n        cxt.setParams({ len: items.length });\n        cxt.pass(codegen_1._ `${len} <= ${items.length}`);\n    }\n    else if (typeof schema == \"object\" && !util_1.alwaysValidSchema(it, schema)) {\n        const valid = gen.var(\"valid\", codegen_1._ `${len} <= ${items.length}`); // TODO var\n        gen.if(codegen_1.not(valid), () => validateItems(valid));\n        cxt.ok(valid);\n    }\n    function validateItems(valid) {\n        gen.forRange(\"i\", items.length, len, (i) => {\n            cxt.subschema({ keyword, dataProp: i, dataPropType: util_1.Type.Num }, valid);\n            if (!it.allErrors)\n                gen.if(codegen_1.not(valid), () => gen.break());\n        });\n    }\n}\nexports.validateAdditionalItems = validateAdditionalItems;\nexports.default = def;\n//# sourceMappingURL=additionalItems.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst items_1 = require(\"./items\");\nconst def = {\n    keyword: \"prefixItems\",\n    type: \"array\",\n    schemaType: [\"array\"],\n    before: \"uniqueItems\",\n    code: (cxt) => items_1.validateTuple(cxt, \"items\"),\n};\nexports.default = def;\n//# sourceMappingURL=prefixItems.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateTuple = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"array\", \"boolean\"],\n    before: \"uniqueItems\",\n    code(cxt) {\n        const { schema, it } = cxt;\n        if (Array.isArray(schema))\n            return validateTuple(cxt, \"additionalItems\", schema);\n        it.items = true;\n        if (util_1.alwaysValidSchema(it, schema))\n            return;\n        cxt.ok(code_1.validateArray(cxt));\n    },\n};\nfunction validateTuple(cxt, extraItems, schArr = cxt.schema) {\n    const { gen, parentSchema, data, keyword, it } = cxt;\n    checkStrictTuple(parentSchema);\n    if (it.opts.unevaluated && schArr.length && it.items !== true) {\n        it.items = util_1.mergeEvaluated.items(gen, schArr.length, it.items);\n    }\n    const valid = gen.name(\"valid\");\n    const len = gen.const(\"len\", codegen_1._ `${data}.length`);\n    schArr.forEach((sch, i) => {\n        if (util_1.alwaysValidSchema(it, sch))\n            return;\n        gen.if(codegen_1._ `${len} > ${i}`, () => cxt.subschema({\n            keyword,\n            schemaProp: i,\n            dataProp: i,\n        }, valid));\n        cxt.ok(valid);\n    });\n    function checkStrictTuple(sch) {\n        const { opts, errSchemaPath } = it;\n        const l = schArr.length;\n        const fullTuple = l === sch.minItems && (l === sch.maxItems || sch[extraItems] === false);\n        if (opts.strictTuples && !fullTuple) {\n            const msg = `\"${keyword}\" is ${l}-tuple, but minItems or maxItems/${extraItems} are not specified or different at path \"${errSchemaPath}\"`;\n            util_1.checkStrictMode(it, msg, opts.strictTuples);\n        }\n    }\n}\nexports.validateTuple = validateTuple;\nexports.default = def;\n//# sourceMappingURL=items.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst additionalItems_1 = require(\"./additionalItems\");\nconst error = {\n    message: ({ params: { len } }) => codegen_1.str `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => codegen_1._ `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { schema, parentSchema, it } = cxt;\n        const { prefixItems } = parentSchema;\n        it.items = true;\n        if (util_1.alwaysValidSchema(it, schema))\n            return;\n        if (prefixItems)\n            additionalItems_1.validateAdditionalItems(cxt, prefixItems);\n        else\n            cxt.ok(code_1.validateArray(cxt));\n    },\n};\nexports.default = def;\n//# sourceMappingURL=items2020.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { min, max } }) => max === undefined\n        ? codegen_1.str `must contain at least ${min} valid item(s)`\n        : codegen_1.str `must contain at least ${min} and no more than ${max} valid item(s)`,\n    params: ({ params: { min, max } }) => max === undefined ? codegen_1._ `{minContains: ${min}}` : codegen_1._ `{minContains: ${min}, maxContains: ${max}}`,\n};\nconst def = {\n    keyword: \"contains\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        let min;\n        let max;\n        const { minContains, maxContains } = parentSchema;\n        if (it.opts.next) {\n            min = minContains === undefined ? 1 : minContains;\n            max = maxContains;\n        }\n        else {\n            min = 1;\n        }\n        const len = gen.const(\"len\", codegen_1._ `${data}.length`);\n        cxt.setParams({ min, max });\n        if (max === undefined && min === 0) {\n            util_1.checkStrictMode(it, `\"minContains\" == 0 without \"maxContains\": \"contains\" keyword ignored`);\n            return;\n        }\n        if (max !== undefined && min > max) {\n            util_1.checkStrictMode(it, `\"minContains\" > \"maxContains\" is always invalid`);\n            cxt.fail();\n            return;\n        }\n        if (util_1.alwaysValidSchema(it, schema)) {\n            let cond = codegen_1._ `${len} >= ${min}`;\n            if (max !== undefined)\n                cond = codegen_1._ `${cond} && ${len} <= ${max}`;\n            cxt.pass(cond);\n            return;\n        }\n        it.items = true;\n        const valid = gen.name(\"valid\");\n        if (max === undefined && min === 1) {\n            validateItems(valid, () => gen.if(valid, () => gen.break()));\n        }\n        else {\n            gen.let(valid, false);\n            const schValid = gen.name(\"_valid\");\n            const count = gen.let(\"count\", 0);\n            validateItems(schValid, () => gen.if(schValid, () => checkLimits(count)));\n        }\n        cxt.result(valid, () => cxt.reset());\n        function validateItems(_valid, block) {\n            gen.forRange(\"i\", 0, len, (i) => {\n                cxt.subschema({\n                    keyword: \"contains\",\n                    dataProp: i,\n                    dataPropType: util_1.Type.Num,\n                    compositeRule: true,\n                }, _valid);\n                block();\n            });\n        }\n        function checkLimits(count) {\n            gen.code(codegen_1._ `${count}++`);\n            if (max === undefined) {\n                gen.if(codegen_1._ `${count} >= ${min}`, () => gen.assign(valid, true).break());\n            }\n            else {\n                gen.if(codegen_1._ `${count} > ${max}`, () => gen.assign(valid, false).break());\n                if (min === 1)\n                    gen.assign(valid, true);\n                else\n                    gen.if(codegen_1._ `${count} >= ${min}`, () => gen.assign(valid, true));\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=contains.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateSchemaDeps = exports.validatePropertyDeps = exports.error = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nexports.error = {\n    message: ({ params: { property, depsCount, deps } }) => {\n        const property_ies = depsCount === 1 ? \"property\" : \"properties\";\n        return codegen_1.str `must have ${property_ies} ${deps} when property ${property} is present`;\n    },\n    params: ({ params: { property, depsCount, deps, missingProperty } }) => codegen_1._ `{property: ${property},\n    missingProperty: ${missingProperty},\n    depsCount: ${depsCount},\n    deps: ${deps}}`, // TODO change to reference\n};\nconst def = {\n    keyword: \"dependencies\",\n    type: \"object\",\n    schemaType: \"object\",\n    error: exports.error,\n    code(cxt) {\n        const [propDeps, schDeps] = splitDependencies(cxt);\n        validatePropertyDeps(cxt, propDeps);\n        validateSchemaDeps(cxt, schDeps);\n    },\n};\nfunction splitDependencies({ schema }) {\n    const propertyDeps = {};\n    const schemaDeps = {};\n    for (const key in schema) {\n        if (key === \"__proto__\")\n            continue;\n        const deps = Array.isArray(schema[key]) ? propertyDeps : schemaDeps;\n        deps[key] = schema[key];\n    }\n    return [propertyDeps, schemaDeps];\n}\nfunction validatePropertyDeps(cxt, propertyDeps = cxt.schema) {\n    const { gen, data, it } = cxt;\n    if (Object.keys(propertyDeps).length === 0)\n        return;\n    const missing = gen.let(\"missing\");\n    for (const prop in propertyDeps) {\n        const deps = propertyDeps[prop];\n        if (deps.length === 0)\n            continue;\n        const hasProperty = code_1.propertyInData(gen, data, prop, it.opts.ownProperties);\n        cxt.setParams({\n            property: prop,\n            depsCount: deps.length,\n            deps: deps.join(\", \"),\n        });\n        if (it.allErrors) {\n            gen.if(hasProperty, () => {\n                for (const depProp of deps) {\n                    code_1.checkReportMissingProp(cxt, depProp);\n                }\n            });\n        }\n        else {\n            gen.if(codegen_1._ `${hasProperty} && (${code_1.checkMissingProp(cxt, deps, missing)})`);\n            code_1.reportMissingProp(cxt, missing);\n            gen.else();\n        }\n    }\n}\nexports.validatePropertyDeps = validatePropertyDeps;\nfunction validateSchemaDeps(cxt, schemaDeps = cxt.schema) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    for (const prop in schemaDeps) {\n        if (util_1.alwaysValidSchema(it, schemaDeps[prop]))\n            continue;\n        gen.if(code_1.propertyInData(gen, data, prop, it.opts.ownProperties), () => {\n            const schCxt = cxt.subschema({ keyword, schemaProp: prop }, valid);\n            cxt.mergeValidEvaluated(schCxt, valid);\n        }, () => gen.var(valid, true) // TODO var\n        );\n        cxt.ok(valid);\n    }\n}\nexports.validateSchemaDeps = validateSchemaDeps;\nexports.default = def;\n//# sourceMappingURL=dependencies.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"property name must be valid\",\n    params: ({ params }) => codegen_1._ `{propertyName: ${params.propertyName}}`,\n};\nconst def = {\n    keyword: \"propertyNames\",\n    type: \"object\",\n    schemaType: [\"object\", \"boolean\"],\n    error,\n    code(cxt) {\n        const { gen, schema, data, it } = cxt;\n        if (util_1.alwaysValidSchema(it, schema))\n            return;\n        const valid = gen.name(\"valid\");\n        gen.forIn(\"key\", data, (key) => {\n            cxt.setParams({ propertyName: key });\n            cxt.subschema({\n                keyword: \"propertyNames\",\n                data: key,\n                dataTypes: [\"string\"],\n                propertyName: key,\n                compositeRule: true,\n            }, valid);\n            gen.if(codegen_1.not(valid), () => {\n                cxt.error(true);\n                if (!it.allErrors)\n                    gen.break();\n            });\n        });\n        cxt.ok(valid);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=propertyNames.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must NOT have additional properties\",\n    params: ({ params }) => codegen_1._ `{additionalProperty: ${params.additionalProperty}}`,\n};\nconst def = {\n    keyword: \"additionalProperties\",\n    type: [\"object\"],\n    schemaType: [\"boolean\", \"object\"],\n    allowUndefined: true,\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, errsCount, it } = cxt;\n        /* istanbul ignore if */\n        if (!errsCount)\n            throw new Error(\"ajv implementation error\");\n        const { allErrors, opts } = it;\n        it.props = true;\n        if (opts.removeAdditional !== \"all\" && util_1.alwaysValidSchema(it, schema))\n            return;\n        const props = code_1.allSchemaProperties(parentSchema.properties);\n        const patProps = code_1.allSchemaProperties(parentSchema.patternProperties);\n        checkAdditionalProperties();\n        cxt.ok(codegen_1._ `${errsCount} === ${names_1.default.errors}`);\n        function checkAdditionalProperties() {\n            gen.forIn(\"key\", data, (key) => {\n                if (!props.length && !patProps.length)\n                    additionalPropertyCode(key);\n                else\n                    gen.if(isAdditional(key), () => additionalPropertyCode(key));\n            });\n        }\n        function isAdditional(key) {\n            let definedProp;\n            if (props.length > 8) {\n                // TODO maybe an option instead of hard-coded 8?\n                const propsSchema = util_1.schemaRefOrVal(it, parentSchema.properties, \"properties\");\n                definedProp = code_1.isOwnProperty(gen, propsSchema, key);\n            }\n            else if (props.length) {\n                definedProp = codegen_1.or(...props.map((p) => codegen_1._ `${key} === ${p}`));\n            }\n            else {\n                definedProp = codegen_1.nil;\n            }\n            if (patProps.length) {\n                definedProp = codegen_1.or(definedProp, ...patProps.map((p) => codegen_1._ `${code_1.usePattern(cxt, p)}.test(${key})`));\n            }\n            return codegen_1.not(definedProp);\n        }\n        function deleteAdditional(key) {\n            gen.code(codegen_1._ `delete ${data}[${key}]`);\n        }\n        function additionalPropertyCode(key) {\n            if (opts.removeAdditional === \"all\" || (opts.removeAdditional && schema === false)) {\n                deleteAdditional(key);\n                return;\n            }\n            if (schema === false) {\n                cxt.setParams({ additionalProperty: key });\n                cxt.error();\n                if (!allErrors)\n                    gen.break();\n                return;\n            }\n            if (typeof schema == \"object\" && !util_1.alwaysValidSchema(it, schema)) {\n                const valid = gen.name(\"valid\");\n                if (opts.removeAdditional === \"failing\") {\n                    applyAdditionalSchema(key, valid, false);\n                    gen.if(codegen_1.not(valid), () => {\n                        cxt.reset();\n                        deleteAdditional(key);\n                    });\n                }\n                else {\n                    applyAdditionalSchema(key, valid);\n                    if (!allErrors)\n                        gen.if(codegen_1.not(valid), () => gen.break());\n                }\n            }\n        }\n        function applyAdditionalSchema(key, valid, errors) {\n            const subschema = {\n                keyword: \"additionalProperties\",\n                dataProp: key,\n                dataPropType: util_1.Type.Str,\n            };\n            if (errors === false) {\n                Object.assign(subschema, {\n                    compositeRule: true,\n                    createErrors: false,\n                    allErrors: false,\n                });\n            }\n            cxt.subschema(subschema, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=additionalProperties.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst validate_1 = require(\"../../compile/validate\");\nconst code_1 = require(\"../code\");\nconst util_1 = require(\"../../compile/util\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst def = {\n    keyword: \"properties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        if (it.opts.removeAdditional === \"all\" && parentSchema.additionalProperties === undefined) {\n            additionalProperties_1.default.code(new validate_1.KeywordCxt(it, additionalProperties_1.default, \"additionalProperties\"));\n        }\n        const allProps = code_1.allSchemaProperties(schema);\n        for (const prop of allProps) {\n            it.definedProperties.add(prop);\n        }\n        if (it.opts.unevaluated && allProps.length && it.props !== true) {\n            it.props = util_1.mergeEvaluated.props(gen, util_1.toHash(allProps), it.props);\n        }\n        const properties = allProps.filter((p) => !util_1.alwaysValidSchema(it, schema[p]));\n        if (properties.length === 0)\n            return;\n        const valid = gen.name(\"valid\");\n        for (const prop of properties) {\n            if (hasDefault(prop)) {\n                applyPropertySchema(prop);\n            }\n            else {\n                gen.if(code_1.propertyInData(gen, data, prop, it.opts.ownProperties));\n                applyPropertySchema(prop);\n                if (!it.allErrors)\n                    gen.else().var(valid, true);\n                gen.endIf();\n            }\n            cxt.it.definedProperties.add(prop);\n            cxt.ok(valid);\n        }\n        function hasDefault(prop) {\n            return it.opts.useDefaults && !it.compositeRule && schema[prop].default !== undefined;\n        }\n        function applyPropertySchema(prop) {\n            cxt.subschema({\n                keyword: \"properties\",\n                schemaProp: prop,\n                dataProp: prop,\n            }, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=properties.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst util_2 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"patternProperties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, data, parentSchema, it } = cxt;\n        const { opts } = it;\n        const patterns = code_1.schemaProperties(it, schema);\n        // TODO mark properties matching patterns with always valid schemas as evaluated\n        if (patterns.length === 0)\n            return;\n        const checkProperties = opts.strictSchema && !opts.allowMatchingProperties && parentSchema.properties;\n        const valid = gen.name(\"valid\");\n        if (it.props !== true && !(it.props instanceof codegen_1.Name)) {\n            it.props = util_2.evaluatedPropsToName(gen, it.props);\n        }\n        const { props } = it;\n        validatePatternProperties();\n        function validatePatternProperties() {\n            for (const pat of patterns) {\n                if (checkProperties)\n                    checkMatchingProperties(pat);\n                if (it.allErrors) {\n                    validateProperties(pat);\n                }\n                else {\n                    gen.var(valid, true); // TODO var\n                    validateProperties(pat);\n                    gen.if(valid);\n                }\n            }\n        }\n        function checkMatchingProperties(pat) {\n            for (const prop in checkProperties) {\n                if (new RegExp(pat).test(prop)) {\n                    util_1.checkStrictMode(it, `property ${prop} matches pattern ${pat} (use allowMatchingProperties)`);\n                }\n            }\n        }\n        function validateProperties(pat) {\n            gen.forIn(\"key\", data, (key) => {\n                gen.if(codegen_1._ `${code_1.usePattern(cxt, pat)}.test(${key})`, () => {\n                    cxt.subschema({\n                        keyword: \"patternProperties\",\n                        schemaProp: pat,\n                        dataProp: key,\n                        dataPropType: util_2.Type.Str,\n                    }, valid);\n                    if (it.opts.unevaluated && props !== true) {\n                        gen.assign(codegen_1._ `${props}[${key}]`, true);\n                    }\n                    else if (!it.allErrors) {\n                        // can short-circuit if `unevaluatedProperties` is not supported (opts.next === false)\n                        // or if all properties were evaluated (props === true)\n                        gen.if(codegen_1.not(valid), () => gen.break());\n                    }\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=patternProperties.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"not\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        if (util_1.alwaysValidSchema(it, schema)) {\n            cxt.fail();\n            return;\n        }\n        const valid = gen.name(\"valid\");\n        cxt.subschema({\n            keyword: \"not\",\n            compositeRule: true,\n            createErrors: false,\n            allErrors: false,\n        }, valid);\n        cxt.result(valid, () => cxt.error(), () => cxt.reset());\n    },\n    error: { message: \"must NOT be valid\" },\n};\nexports.default = def;\n//# sourceMappingURL=not.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"anyOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    code: code_1.validateUnion,\n    error: { message: \"must match a schema in anyOf\" },\n};\nexports.default = def;\n//# sourceMappingURL=anyOf.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must match exactly one schema in oneOf\",\n    params: ({ params }) => codegen_1._ `{passingSchemas: ${params.passing}}`,\n};\nconst def = {\n    keyword: \"oneOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        if (it.opts.discriminator && parentSchema.discriminator)\n            return;\n        const schArr = schema;\n        const valid = gen.let(\"valid\", false);\n        const passing = gen.let(\"passing\", null);\n        const schValid = gen.name(\"_valid\");\n        cxt.setParams({ passing });\n        // TODO possibly fail straight away (with warning or exception) if there are two empty always valid schemas\n        gen.block(validateOneOf);\n        cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n        function validateOneOf() {\n            schArr.forEach((sch, i) => {\n                let schCxt;\n                if (util_1.alwaysValidSchema(it, sch)) {\n                    gen.var(schValid, true);\n                }\n                else {\n                    schCxt = cxt.subschema({\n                        keyword: \"oneOf\",\n                        schemaProp: i,\n                        compositeRule: true,\n                    }, schValid);\n                }\n                if (i > 0) {\n                    gen\n                        .if(codegen_1._ `${schValid} && ${valid}`)\n                        .assign(valid, false)\n                        .assign(passing, codegen_1._ `[${passing}, ${i}]`)\n                        .else();\n                }\n                gen.if(schValid, () => {\n                    gen.assign(valid, true);\n                    gen.assign(passing, i);\n                    if (schCxt)\n                        cxt.mergeEvaluated(schCxt, codegen_1.Name);\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=oneOf.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"allOf\",\n    schemaType: \"array\",\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        const valid = gen.name(\"valid\");\n        schema.forEach((sch, i) => {\n            if (util_1.alwaysValidSchema(it, sch))\n                return;\n            const schCxt = cxt.subschema({ keyword: \"allOf\", schemaProp: i }, valid);\n            cxt.ok(valid);\n            cxt.mergeEvaluated(schCxt);\n        });\n    },\n};\nexports.default = def;\n//# sourceMappingURL=allOf.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params }) => codegen_1.str `must match \"${params.ifClause}\" schema`,\n    params: ({ params }) => codegen_1._ `{failingKeyword: ${params.ifClause}}`,\n};\nconst def = {\n    keyword: \"if\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, parentSchema, it } = cxt;\n        if (parentSchema.then === undefined && parentSchema.else === undefined) {\n            util_1.checkStrictMode(it, '\"if\" without \"then\" and \"else\" is ignored');\n        }\n        const hasThen = hasSchema(it, \"then\");\n        const hasElse = hasSchema(it, \"else\");\n        if (!hasThen && !hasElse)\n            return;\n        const valid = gen.let(\"valid\", true);\n        const schValid = gen.name(\"_valid\");\n        validateIf();\n        cxt.reset();\n        if (hasThen && hasElse) {\n            const ifClause = gen.let(\"ifClause\");\n            cxt.setParams({ ifClause });\n            gen.if(schValid, validateClause(\"then\", ifClause), validateClause(\"else\", ifClause));\n        }\n        else if (hasThen) {\n            gen.if(schValid, validateClause(\"then\"));\n        }\n        else {\n            gen.if(codegen_1.not(schValid), validateClause(\"else\"));\n        }\n        cxt.pass(valid, () => cxt.error(true));\n        function validateIf() {\n            const schCxt = cxt.subschema({\n                keyword: \"if\",\n                compositeRule: true,\n                createErrors: false,\n                allErrors: false,\n            }, schValid);\n            cxt.mergeEvaluated(schCxt);\n        }\n        function validateClause(keyword, ifClause) {\n            return () => {\n                const schCxt = cxt.subschema({ keyword }, schValid);\n                gen.assign(valid, schValid);\n                cxt.mergeValidEvaluated(schCxt, valid);\n                if (ifClause)\n                    gen.assign(ifClause, codegen_1._ `${keyword}`);\n                else\n                    cxt.setParams({ ifClause: keyword });\n            };\n        }\n    },\n};\nfunction hasSchema(it, keyword) {\n    const schema = it.schema[keyword];\n    return schema !== undefined && !util_1.alwaysValidSchema(it, schema);\n}\nexports.default = def;\n//# sourceMappingURL=if.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: [\"then\", \"else\"],\n    schemaType: [\"object\", \"boolean\"],\n    code({ keyword, parentSchema, it }) {\n        if (parentSchema.if === undefined)\n            util_1.checkStrictMode(it, `\"${keyword}\" without \"if\" is ignored`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=thenElse.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst format_1 = require(\"./format\");\nconst format = [format_1.default];\nexports.default = format;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => codegen_1.str `must match format \"${schemaCode}\"`,\n    params: ({ schemaCode }) => codegen_1._ `{format: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"format\",\n    type: [\"number\", \"string\"],\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt, ruleType) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        const { opts, errSchemaPath, schemaEnv, self } = it;\n        if (!opts.validateFormats)\n            return;\n        if ($data)\n            validate$DataFormat();\n        else\n            validateFormat();\n        function validate$DataFormat() {\n            const fmts = gen.scopeValue(\"formats\", {\n                ref: self.formats,\n                code: opts.code.formats,\n            });\n            const fDef = gen.const(\"fDef\", codegen_1._ `${fmts}[${schemaCode}]`);\n            const fType = gen.let(\"fType\");\n            const format = gen.let(\"format\");\n            // TODO simplify\n            gen.if(codegen_1._ `typeof ${fDef} == \"object\" && !(${fDef} instanceof RegExp)`, () => gen.assign(fType, codegen_1._ `${fDef}.type || \"string\"`).assign(format, codegen_1._ `${fDef}.validate`), () => gen.assign(fType, codegen_1._ `\"string\"`).assign(format, fDef));\n            cxt.fail$data(codegen_1.or(unknownFmt(), invalidFmt()));\n            function unknownFmt() {\n                if (opts.strictSchema === false)\n                    return codegen_1.nil;\n                return codegen_1._ `${schemaCode} && !${format}`;\n            }\n            function invalidFmt() {\n                const callFormat = schemaEnv.$async\n                    ? codegen_1._ `(${fDef}.async ? await ${format}(${data}) : ${format}(${data}))`\n                    : codegen_1._ `${format}(${data})`;\n                const validData = codegen_1._ `(typeof ${format} == \"function\" ? ${callFormat} : ${format}.test(${data}))`;\n                return codegen_1._ `${format} && ${format} !== true && ${fType} === ${ruleType} && !${validData}`;\n            }\n        }\n        function validateFormat() {\n            const formatDef = self.formats[schema];\n            if (!formatDef) {\n                unknownFormat();\n                return;\n            }\n            if (formatDef === true)\n                return;\n            const [fmtType, format, fmtRef] = getFormat(formatDef);\n            if (fmtType === ruleType)\n                cxt.pass(validCondition());\n            function unknownFormat() {\n                if (opts.strictSchema === false) {\n                    self.logger.warn(unknownMsg());\n                    return;\n                }\n                throw new Error(unknownMsg());\n                function unknownMsg() {\n                    return `unknown format \"${schema}\" ignored in schema at path \"${errSchemaPath}\"`;\n                }\n            }\n            function getFormat(fmtDef) {\n                const code = fmtDef instanceof RegExp\n                    ? codegen_1.regexpCode(fmtDef)\n                    : opts.code.formats\n                        ? codegen_1._ `${opts.code.formats}${codegen_1.getProperty(schema)}`\n                        : undefined;\n                const fmt = gen.scopeValue(\"formats\", { key: schema, ref: fmtDef, code });\n                if (typeof fmtDef == \"object\" && !(fmtDef instanceof RegExp)) {\n                    return [fmtDef.type || \"string\", fmtDef.validate, codegen_1._ `${fmt}.validate`];\n                }\n                return [\"string\", fmtDef, fmt];\n            }\n            function validCondition() {\n                if (typeof formatDef == \"object\" && !(formatDef instanceof RegExp) && formatDef.async) {\n                    if (!schemaEnv.$async)\n                        throw new Error(\"async format in sync schema\");\n                    return codegen_1._ `await ${fmtRef}(${data})`;\n                }\n                return typeof format == \"function\" ? codegen_1._ `${fmtRef}(${data})` : codegen_1._ `${fmtRef}.test(${data})`;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=format.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.contentVocabulary = exports.metadataVocabulary = void 0;\nexports.metadataVocabulary = [\n    \"title\",\n    \"description\",\n    \"default\",\n    \"deprecated\",\n    \"readOnly\",\n    \"writeOnly\",\n    \"examples\",\n];\nexports.contentVocabulary = [\n    \"contentMediaType\",\n    \"contentEncoding\",\n    \"contentSchema\",\n];\n//# sourceMappingURL=metadata.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst types_1 = require(\"../discriminator/types\");\nconst error = {\n    message: ({ params: { discrError, tagName } }) => discrError === types_1.DiscrError.Tag\n        ? `tag \"${tagName}\" must be string`\n        : `value of tag \"${tagName}\" must be in oneOf`,\n    params: ({ params: { discrError, tag, tagName } }) => codegen_1._ `{error: ${discrError}, tag: ${tagName}, tagValue: ${tag}}`,\n};\nconst def = {\n    keyword: \"discriminator\",\n    type: \"object\",\n    schemaType: \"object\",\n    error,\n    code(cxt) {\n        const { gen, data, schema, parentSchema, it } = cxt;\n        const { oneOf } = parentSchema;\n        if (!it.opts.discriminator) {\n            throw new Error(\"discriminator: requires discriminator option\");\n        }\n        const tagName = schema.propertyName;\n        if (typeof tagName != \"string\")\n            throw new Error(\"discriminator: requires propertyName\");\n        if (schema.mapping)\n            throw new Error(\"discriminator: mapping is not supported\");\n        if (!oneOf)\n            throw new Error(\"discriminator: requires oneOf keyword\");\n        const valid = gen.let(\"valid\", false);\n        const tag = gen.const(\"tag\", codegen_1._ `${data}${codegen_1.getProperty(tagName)}`);\n        gen.if(codegen_1._ `typeof ${tag} == \"string\"`, () => validateMapping(), () => cxt.error(false, { discrError: types_1.DiscrError.Tag, tag, tagName }));\n        cxt.ok(valid);\n        function validateMapping() {\n            const mapping = getMapping();\n            gen.if(false);\n            for (const tagValue in mapping) {\n                gen.elseIf(codegen_1._ `${tag} === ${tagValue}`);\n                gen.assign(valid, applyTagSchema(mapping[tagValue]));\n            }\n            gen.else();\n            cxt.error(false, { discrError: types_1.DiscrError.Mapping, tag, tagName });\n            gen.endIf();\n        }\n        function applyTagSchema(schemaProp) {\n            const _valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({ keyword: \"oneOf\", schemaProp }, _valid);\n            cxt.mergeEvaluated(schCxt, codegen_1.Name);\n            return _valid;\n        }\n        function getMapping() {\n            var _a;\n            const oneOfMapping = {};\n            const topRequired = hasRequired(parentSchema);\n            let tagRequired = true;\n            for (let i = 0; i < oneOf.length; i++) {\n                const sch = oneOf[i];\n                const propSch = (_a = sch.properties) === null || _a === void 0 ? void 0 : _a[tagName];\n                if (typeof propSch != \"object\") {\n                    throw new Error(`discriminator: oneOf schemas must have \"properties/${tagName}\"`);\n                }\n                tagRequired = tagRequired && (topRequired || hasRequired(sch));\n                addMappings(propSch, i);\n            }\n            if (!tagRequired)\n                throw new Error(`discriminator: \"${tagName}\" must be required`);\n            return oneOfMapping;\n            function hasRequired({ required }) {\n                return Array.isArray(required) && required.includes(tagName);\n            }\n            function addMappings(sch, i) {\n                if (sch.const) {\n                    addMapping(sch.const, i);\n                }\n                else if (sch.enum) {\n                    for (const tagValue of sch.enum) {\n                        addMapping(tagValue, i);\n                    }\n                }\n                else {\n                    throw new Error(`discriminator: \"properties/${tagName}\" must have \"const\" or \"enum\"`);\n                }\n            }\n            function addMapping(tagValue, i) {\n                if (typeof tagValue != \"string\" || tagValue in oneOfMapping) {\n                    throw new Error(`discriminator: \"${tagName}\" values must be unique strings`);\n                }\n                oneOfMapping[tagValue] = i;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=index.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DiscrError = void 0;\nvar DiscrError;\n(function (DiscrError) {\n    DiscrError[\"Tag\"] = \"tag\";\n    DiscrError[\"Mapping\"] = \"mapping\";\n})(DiscrError = exports.DiscrError || (exports.DiscrError = {}));\n//# sourceMappingURL=types.js.map", "module.exports = {\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"$id\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Core schema meta-schema\",\n  \"definitions\": {\n    \"schemaArray\": {\n      \"type\": \"array\",\n      \"minItems\": 1,\n      \"items\": {\"$ref\": \"#\"}\n    },\n    \"nonNegativeInteger\": {\n      \"type\": \"integer\",\n      \"minimum\": 0\n    },\n    \"nonNegativeIntegerDefault0\": {\n      \"allOf\": [{\"$ref\": \"#/definitions/nonNegativeInteger\"}, {\"default\": 0}]\n    },\n    \"simpleTypes\": {\n      \"enum\": [\"array\", \"boolean\", \"integer\", \"null\", \"number\", \"object\", \"string\"]\n    },\n    \"stringArray\": {\n      \"type\": \"array\",\n      \"items\": {\"type\": \"string\"},\n      \"uniqueItems\": true,\n      \"default\": []\n    }\n  },\n  \"type\": [\"object\", \"boolean\"],\n  \"properties\": {\n    \"$id\": {\n      \"type\": \"string\",\n      \"format\": \"uri-reference\"\n    },\n    \"$schema\": {\n      \"type\": \"string\",\n      \"format\": \"uri\"\n    },\n    \"$ref\": {\n      \"type\": \"string\",\n      \"format\": \"uri-reference\"\n    },\n    \"$comment\": {\n      \"type\": \"string\"\n    },\n    \"title\": {\n      \"type\": \"string\"\n    },\n    \"description\": {\n      \"type\": \"string\"\n    },\n    \"default\": true,\n    \"readOnly\": {\n      \"type\": \"boolean\",\n      \"default\": false\n    },\n    \"examples\": {\n      \"type\": \"array\",\n      \"items\": true\n    },\n    \"multipleOf\": {\n      \"type\": \"number\",\n      \"exclusiveMinimum\": 0\n    },\n    \"maximum\": {\n      \"type\": \"number\"\n    },\n    \"exclusiveMaximum\": {\n      \"type\": \"number\"\n    },\n    \"minimum\": {\n      \"type\": \"number\"\n    },\n    \"exclusiveMinimum\": {\n      \"type\": \"number\"\n    },\n    \"maxLength\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minLength\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"pattern\": {\n      \"type\": \"string\",\n      \"format\": \"regex\"\n    },\n    \"additionalItems\": {\"$ref\": \"#\"},\n    \"items\": {\n      \"anyOf\": [{\"$ref\": \"#\"}, {\"$ref\": \"#/definitions/schemaArray\"}],\n      \"default\": true\n    },\n    \"maxItems\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minItems\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"uniqueItems\": {\n      \"type\": \"boolean\",\n      \"default\": false\n    },\n    \"contains\": {\"$ref\": \"#\"},\n    \"maxProperties\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minProperties\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"required\": {\"$ref\": \"#/definitions/stringArray\"},\n    \"additionalProperties\": {\"$ref\": \"#\"},\n    \"definitions\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"default\": {}\n    },\n    \"properties\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"default\": {}\n    },\n    \"patternProperties\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"propertyNames\": {\"format\": \"regex\"},\n      \"default\": {}\n    },\n    \"dependencies\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"anyOf\": [{\"$ref\": \"#\"}, {\"$ref\": \"#/definitions/stringArray\"}]\n      }\n    },\n    \"propertyNames\": {\"$ref\": \"#\"},\n    \"const\": true,\n    \"enum\": {\n      \"type\": \"array\",\n      \"items\": true,\n      \"minItems\": 1,\n      \"uniqueItems\": true\n    },\n    \"type\": {\n      \"anyOf\": [\n        {\"$ref\": \"#/definitions/simpleTypes\"},\n        {\n          \"type\": \"array\",\n          \"items\": {\"$ref\": \"#/definitions/simpleTypes\"},\n          \"minItems\": 1,\n          \"uniqueItems\": true\n        }\n      ]\n    },\n    \"format\": {\"type\": \"string\"},\n    \"contentMediaType\": {\"type\": \"string\"},\n    \"contentEncoding\": {\"type\": \"string\"},\n    \"if\": {\"$ref\": \"#\"},\n    \"then\": {\"$ref\": \"#\"},\n    \"else\": {\"$ref\": \"#\"},\n    \"allOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"anyOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"oneOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"not\": {\"$ref\": \"#\"}\n  },\n  \"default\": true\n}\n"]}