module.exports = (function() {
var __MODS__ = {};
var __DEFINE__ = function(modId, func, req) { var m = { exports: {}, _tempexports: {} }; __MODS__[modId] = { status: 0, func: func, req: req, m: m }; };
var __REQUIRE__ = function(modId, source) { if(!__MODS__[modId]) return require(source); if(!__MODS__[modId].status) { var m = __MODS__[modId].m; m._exports = m._tempexports; var desp = Object.getOwnPropertyDescriptor(m, "exports"); if (desp && desp.configurable) Object.defineProperty(m, "exports", { set: function (val) { if(typeof val === "object" && val !== m._exports) { m._exports.__proto__ = val.__proto__; Object.keys(val).forEach(function (k) { m._exports[k] = val[k]; }); } m._tempexports = val }, get: function () { return m._tempexports; } }); __MODS__[modId].status = 1; __MODS__[modId].func(__MODS__[modId].req, m, m.exports); } return __MODS__[modId].m.exports; };
var __REQUIRE_WILDCARD__ = function(obj) { if(obj && obj.__esModule) { return obj; } else { var newObj = {}; if(obj != null) { for(var k in obj) { if (Object.prototype.hasOwnProperty.call(obj, k)) newObj[k] = obj[k]; } } newObj.default = obj; return newObj; } };
var __REQUIRE_DEFAULT__ = function(obj) { return obj && obj.__esModule ? obj.default : obj; };
__DEFINE__(1736920635621, function(require, module, exports) {

var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBorderCharacters = exports.createStream = exports.table = void 0;
const createStream_1 = require("./createStream");
Object.defineProperty(exports, "createStream", { enumerable: true, get: function () { return createStream_1.createStream; } });
const getBorderCharacters_1 = require("./getBorderCharacters");
Object.defineProperty(exports, "getBorderCharacters", { enumerable: true, get: function () { return getBorderCharacters_1.getBorderCharacters; } });
const table_1 = require("./table");
Object.defineProperty(exports, "table", { enumerable: true, get: function () { return table_1.table; } });
__exportStar(require("./types/api"), exports);

}, function(modId) {var map = {"./createStream":1736920635622,"./getBorderCharacters":1736920635626,"./table":1736920635642,"./types/api":1736920635649}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635622, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.createStream = void 0;
const alignTableData_1 = require("./alignTableData");
const calculateRowHeights_1 = require("./calculateRowHeights");
const drawBorder_1 = require("./drawBorder");
const drawRow_1 = require("./drawRow");
const makeStreamConfig_1 = require("./makeStreamConfig");
const mapDataUsingRowHeights_1 = require("./mapDataUsingRowHeights");
const padTableData_1 = require("./padTableData");
const stringifyTableData_1 = require("./stringifyTableData");
const truncateTableData_1 = require("./truncateTableData");
const prepareData = (data, config) => {
    let rows = stringifyTableData_1.stringifyTableData(data);
    rows = truncateTableData_1.truncateTableData(rows, config);
    const rowHeights = calculateRowHeights_1.calculateRowHeights(rows, config);
    rows = mapDataUsingRowHeights_1.mapDataUsingRowHeights(rows, rowHeights, config);
    rows = alignTableData_1.alignTableData(rows, config);
    rows = padTableData_1.padTableData(rows, config);
    return rows;
};
const create = (row, columnWidths, config) => {
    const rows = prepareData([row], config);
    const body = rows.map((literalRow) => {
        return drawRow_1.drawRow(literalRow, config);
    }).join('');
    let output;
    output = '';
    output += drawBorder_1.drawBorderTop(columnWidths, config);
    output += body;
    output += drawBorder_1.drawBorderBottom(columnWidths, config);
    output = output.trimEnd();
    process.stdout.write(output);
};
const append = (row, columnWidths, config) => {
    const rows = prepareData([row], config);
    const body = rows.map((literalRow) => {
        return drawRow_1.drawRow(literalRow, config);
    }).join('');
    let output = '';
    const bottom = drawBorder_1.drawBorderBottom(columnWidths, config);
    if (bottom !== '\n') {
        output = '\r\u001B[K';
    }
    output += drawBorder_1.drawBorderJoin(columnWidths, config);
    output += body;
    output += bottom;
    output = output.trimEnd();
    process.stdout.write(output);
};
const createStream = (userConfig) => {
    const config = makeStreamConfig_1.makeStreamConfig(userConfig);
    const columnWidths = Object.values(config.columns).map((column) => {
        return column.width + column.paddingLeft + column.paddingRight;
    });
    let empty = true;
    return {
        write: (row) => {
            if (row.length !== config.columnCount) {
                throw new Error('Row cell count does not match the config.columnCount.');
            }
            if (empty) {
                empty = false;
                create(row, columnWidths, config);
            }
            else {
                append(row, columnWidths, config);
            }
        },
    };
};
exports.createStream = createStream;

}, function(modId) { var map = {"./alignTableData":1736920635623,"./calculateRowHeights":1736920635627,"./drawBorder":1736920635632,"./drawRow":1736920635634,"./makeStreamConfig":1736920635635,"./mapDataUsingRowHeights":1736920635638,"./padTableData":1736920635639,"./stringifyTableData":1736920635640,"./truncateTableData":1736920635641}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635623, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.alignTableData = void 0;
const alignString_1 = require("./alignString");
const alignTableData = (rows, config) => {
    return rows.map((row) => {
        return row.map((cell, cellIndex) => {
            const { width, alignment } = config.columns[cellIndex];
            return alignString_1.alignString(cell, width, alignment);
        });
    });
};
exports.alignTableData = alignTableData;

}, function(modId) { var map = {"./alignString":1736920635624}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635624, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.alignString = void 0;
const string_width_1 = __importDefault(require("string-width"));
const utils_1 = require("./utils");
const alignLeft = (subject, width) => {
    return subject + ' '.repeat(width);
};
const alignRight = (subject, width) => {
    return ' '.repeat(width) + subject;
};
const alignCenter = (subject, width) => {
    return ' '.repeat(Math.floor(width / 2)) + subject + ' '.repeat(Math.ceil(width / 2));
};
const alignJustify = (subject, width) => {
    const spaceSequenceCount = utils_1.countSpaceSequence(subject);
    if (spaceSequenceCount === 0) {
        return alignLeft(subject, width);
    }
    const addingSpaces = utils_1.distributeUnevenly(width, spaceSequenceCount);
    if (Math.max(...addingSpaces) > 3) {
        return alignLeft(subject, width);
    }
    let spaceSequenceIndex = 0;
    return subject.replace(/\s+/g, (groupSpace) => {
        return groupSpace + ' '.repeat(addingSpaces[spaceSequenceIndex++]);
    });
};
/**
 * Pads a string to the left and/or right to position the subject
 * text in a desired alignment within a container.
 */
const alignString = (subject, containerWidth, alignment) => {
    const subjectWidth = string_width_1.default(subject);
    if (subjectWidth === containerWidth) {
        return subject;
    }
    if (subjectWidth > containerWidth) {
        throw new Error('Subject parameter value width cannot be greater than the container width.');
    }
    if (subjectWidth === 0) {
        return ' '.repeat(containerWidth);
    }
    const availableWidth = containerWidth - subjectWidth;
    if (alignment === 'left') {
        return alignLeft(subject, availableWidth);
    }
    if (alignment === 'right') {
        return alignRight(subject, availableWidth);
    }
    if (alignment === 'justify') {
        return alignJustify(subject, availableWidth);
    }
    return alignCenter(subject, availableWidth);
};
exports.alignString = alignString;

}, function(modId) { var map = {"./utils":1736920635625}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635625, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.distributeUnevenly = exports.countSpaceSequence = exports.groupBySizes = exports.makeBorderConfig = exports.splitAnsi = exports.normalizeString = void 0;
const slice_ansi_1 = __importDefault(require("slice-ansi"));
const string_width_1 = __importDefault(require("string-width"));
const strip_ansi_1 = __importDefault(require("strip-ansi"));
const getBorderCharacters_1 = require("./getBorderCharacters");
/**
 * Converts Windows-style newline to Unix-style
 *
 * @internal
 */
const normalizeString = (input) => {
    return input.replace(/\r\n/g, '\n');
};
exports.normalizeString = normalizeString;
/**
 * Splits ansi string by newlines
 *
 * @internal
 */
const splitAnsi = (input) => {
    const lengths = strip_ansi_1.default(input).split('\n').map(string_width_1.default);
    const result = [];
    let startIndex = 0;
    lengths.forEach((length) => {
        result.push(length === 0 ? '' : slice_ansi_1.default(input, startIndex, startIndex + length));
        // Plus 1 for the newline character itself
        startIndex += length + 1;
    });
    return result;
};
exports.splitAnsi = splitAnsi;
/**
 * Merges user provided border characters with the default border ("honeywell") characters.
 *
 * @internal
 */
const makeBorderConfig = (border) => {
    return {
        ...getBorderCharacters_1.getBorderCharacters('honeywell'),
        ...border,
    };
};
exports.makeBorderConfig = makeBorderConfig;
/**
 * Groups the array into sub-arrays by sizes.
 *
 * @internal
 * @example
 * groupBySizes(['a', 'b', 'c', 'd', 'e'], [2, 1, 2]) = [ ['a', 'b'], ['c'], ['d', 'e'] ]
 */
const groupBySizes = (array, sizes) => {
    let startIndex = 0;
    return sizes.map((size) => {
        const group = array.slice(startIndex, startIndex + size);
        startIndex += size;
        return group;
    });
};
exports.groupBySizes = groupBySizes;
/**
 * Counts the number of continuous spaces in a string
 *
 * @internal
 * @example
 * countGroupSpaces('a  bc  de f') = 3
 */
const countSpaceSequence = (input) => {
    var _a, _b;
    return (_b = (_a = input.match(/\s+/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;
};
exports.countSpaceSequence = countSpaceSequence;
/**
 * Creates the non-increasing number array given sum and length
 * whose the difference between maximum and minimum is not greater than 1
 *
 * @internal
 * @example
 * distributeUnevenly(6, 3) = [2, 2, 2]
 * distributeUnevenly(8, 3) = [3, 3, 2]
 */
const distributeUnevenly = (sum, length) => {
    const result = Array.from({ length }).fill(Math.floor(sum / length));
    return result.map((element, index) => {
        return element + (index < sum % length ? 1 : 0);
    });
};
exports.distributeUnevenly = distributeUnevenly;

}, function(modId) { var map = {"./getBorderCharacters":1736920635626}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635626, function(require, module, exports) {

/* eslint-disable sort-keys-fix/sort-keys-fix */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBorderCharacters = void 0;
const getBorderCharacters = (name) => {
    if (name === 'honeywell') {
        return {
            topBody: '═',
            topJoin: '╤',
            topLeft: '╔',
            topRight: '╗',
            bottomBody: '═',
            bottomJoin: '╧',
            bottomLeft: '╚',
            bottomRight: '╝',
            bodyLeft: '║',
            bodyRight: '║',
            bodyJoin: '│',
            headerJoin: '┬',
            joinBody: '─',
            joinLeft: '╟',
            joinRight: '╢',
            joinJoin: '┼',
        };
    }
    if (name === 'norc') {
        return {
            topBody: '─',
            topJoin: '┬',
            topLeft: '┌',
            topRight: '┐',
            bottomBody: '─',
            bottomJoin: '┴',
            bottomLeft: '└',
            bottomRight: '┘',
            bodyLeft: '│',
            bodyRight: '│',
            bodyJoin: '│',
            headerJoin: '┬',
            joinBody: '─',
            joinLeft: '├',
            joinRight: '┤',
            joinJoin: '┼',
        };
    }
    if (name === 'ramac') {
        return {
            topBody: '-',
            topJoin: '+',
            topLeft: '+',
            topRight: '+',
            bottomBody: '-',
            bottomJoin: '+',
            bottomLeft: '+',
            bottomRight: '+',
            bodyLeft: '|',
            bodyRight: '|',
            bodyJoin: '|',
            headerJoin: '+',
            joinBody: '-',
            joinLeft: '|',
            joinRight: '|',
            joinJoin: '|',
        };
    }
    if (name === 'void') {
        return {
            topBody: '',
            topJoin: '',
            topLeft: '',
            topRight: '',
            bottomBody: '',
            bottomJoin: '',
            bottomLeft: '',
            bottomRight: '',
            bodyLeft: '',
            bodyRight: '',
            bodyJoin: '',
            headerJoin: '',
            joinBody: '',
            joinLeft: '',
            joinRight: '',
            joinJoin: '',
        };
    }
    throw new Error('Unknown border template "' + name + '".');
};
exports.getBorderCharacters = getBorderCharacters;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635627, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateRowHeights = void 0;
const calculateCellHeight_1 = require("./calculateCellHeight");
/**
 * Produces an array of values that describe the largest value length (height) in every row.
 */
const calculateRowHeights = (rows, config) => {
    return rows.map((row) => {
        let rowHeight = 1;
        row.forEach((cell, cellIndex) => {
            const cellHeight = calculateCellHeight_1.calculateCellHeight(cell, config.columns[cellIndex].width, config.columns[cellIndex].wrapWord);
            rowHeight = Math.max(rowHeight, cellHeight);
        });
        return rowHeight;
    });
};
exports.calculateRowHeights = calculateRowHeights;

}, function(modId) { var map = {"./calculateCellHeight":1736920635628}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635628, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateCellHeight = void 0;
const wrapCell_1 = require("./wrapCell");
/**
 * Calculates height of cell content in regard to its width and word wrapping.
 */
const calculateCellHeight = (value, columnWidth, useWrapWord = false) => {
    return wrapCell_1.wrapCell(value, columnWidth, useWrapWord).length;
};
exports.calculateCellHeight = calculateCellHeight;

}, function(modId) { var map = {"./wrapCell":1736920635629}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635629, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapCell = void 0;
const utils_1 = require("./utils");
const wrapString_1 = require("./wrapString");
const wrapWord_1 = require("./wrapWord");
/**
 * Wrap a single cell value into a list of lines
 *
 * Always wraps on newlines, for the remainder uses either word or string wrapping
 * depending on user configuration.
 *
 */
const wrapCell = (cellValue, cellWidth, useWrapWord) => {
    // First split on literal newlines
    const cellLines = utils_1.splitAnsi(cellValue);
    // Then iterate over the list and word-wrap every remaining line if necessary.
    for (let lineNr = 0; lineNr < cellLines.length;) {
        let lineChunks;
        if (useWrapWord) {
            lineChunks = wrapWord_1.wrapWord(cellLines[lineNr], cellWidth);
        }
        else {
            lineChunks = wrapString_1.wrapString(cellLines[lineNr], cellWidth);
        }
        // Replace our original array element with whatever the wrapping returned
        cellLines.splice(lineNr, 1, ...lineChunks);
        lineNr += lineChunks.length;
    }
    return cellLines;
};
exports.wrapCell = wrapCell;

}, function(modId) { var map = {"./utils":1736920635625,"./wrapString":1736920635630,"./wrapWord":1736920635631}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635630, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapString = void 0;
const slice_ansi_1 = __importDefault(require("slice-ansi"));
const string_width_1 = __importDefault(require("string-width"));
/**
 * Creates an array of strings split into groups the length of size.
 * This function works with strings that contain ASCII characters.
 *
 * wrapText is different from would-be "chunk" implementation
 * in that whitespace characters that occur on a chunk size limit are trimmed.
 *
 */
const wrapString = (subject, size) => {
    let subjectSlice = subject;
    const chunks = [];
    do {
        chunks.push(slice_ansi_1.default(subjectSlice, 0, size));
        subjectSlice = slice_ansi_1.default(subjectSlice, size).trim();
    } while (string_width_1.default(subjectSlice));
    return chunks;
};
exports.wrapString = wrapString;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635631, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapWord = void 0;
const slice_ansi_1 = __importDefault(require("slice-ansi"));
const strip_ansi_1 = __importDefault(require("strip-ansi"));
const calculateStringLengths = (input, size) => {
    let subject = strip_ansi_1.default(input);
    const chunks = [];
    // https://regex101.com/r/gY5kZ1/1
    const re = new RegExp('(^.{1,' + String(size) + '}(\\s+|$))|(^.{1,' + String(size - 1) + '}(\\\\|/|_|\\.|,|;|-))');
    do {
        let chunk;
        const match = re.exec(subject);
        if (match) {
            chunk = match[0];
            subject = subject.slice(chunk.length);
            const trimmedLength = chunk.trim().length;
            const offset = chunk.length - trimmedLength;
            chunks.push([trimmedLength, offset]);
        }
        else {
            chunk = subject.slice(0, size);
            subject = subject.slice(size);
            chunks.push([chunk.length, 0]);
        }
    } while (subject.length);
    return chunks;
};
const wrapWord = (input, size) => {
    const result = [];
    let startIndex = 0;
    calculateStringLengths(input, size).forEach(([length, offset]) => {
        result.push(slice_ansi_1.default(input, startIndex, startIndex + length));
        startIndex += length + offset;
    });
    return result;
};
exports.wrapWord = wrapWord;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635632, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.drawBorderTop = exports.drawBorderJoin = exports.drawBorderBottom = exports.drawBorder = exports.createTableBorderGetter = void 0;
const drawContent_1 = require("./drawContent");
const drawBorder = (columnWidths, config) => {
    const { separator, drawVerticalLine } = config;
    const columns = columnWidths.map((size) => {
        return config.separator.body.repeat(size);
    });
    return drawContent_1.drawContent(columns, {
        drawSeparator: drawVerticalLine,
        separatorGetter: (index, columnCount) => {
            if (index === 0) {
                return separator.left;
            }
            if (index === columnCount) {
                return separator.right;
            }
            return separator.join;
        },
    }) + '\n';
};
exports.drawBorder = drawBorder;
const drawBorderTop = (columnWidths, config) => {
    const result = drawBorder(columnWidths, {
        ...config,
        separator: {
            body: config.border.topBody,
            join: config.border.topJoin,
            left: config.border.topLeft,
            right: config.border.topRight,
        },
    });
    if (result === '\n') {
        return '';
    }
    return result;
};
exports.drawBorderTop = drawBorderTop;
const drawBorderJoin = (columnWidths, config) => {
    return drawBorder(columnWidths, {
        ...config,
        separator: {
            body: config.border.joinBody,
            join: config.border.joinJoin,
            left: config.border.joinLeft,
            right: config.border.joinRight,
        },
    });
};
exports.drawBorderJoin = drawBorderJoin;
const drawBorderBottom = (columnWidths, config) => {
    return drawBorder(columnWidths, {
        ...config,
        separator: {
            body: config.border.bottomBody,
            join: config.border.bottomJoin,
            left: config.border.bottomLeft,
            right: config.border.bottomRight,
        },
    });
};
exports.drawBorderBottom = drawBorderBottom;
const createTableBorderGetter = (columnWidths, config) => {
    return (index, size) => {
        if (!config.header) {
            if (index === 0) {
                return drawBorderTop(columnWidths, config);
            }
            if (index === size) {
                return drawBorderBottom(columnWidths, config);
            }
            return drawBorderJoin(columnWidths, config);
        }
        // Deal with the header
        if (index === 0) {
            return drawBorderTop(columnWidths, {
                ...config,
                border: {
                    ...config.border,
                    topJoin: config.border.topBody,
                },
            });
        }
        if (index === 1) {
            return drawBorderJoin(columnWidths, {
                ...config,
                border: {
                    ...config.border,
                    joinJoin: config.border.headerJoin,
                },
            });
        }
        if (index === size) {
            return drawBorderBottom(columnWidths, config);
        }
        return drawBorderJoin(columnWidths, config);
    };
};
exports.createTableBorderGetter = createTableBorderGetter;

}, function(modId) { var map = {"./drawContent":1736920635633}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635633, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.drawContent = void 0;
/**
 * Shared function to draw horizontal borders, rows or the entire table
 */
const drawContent = (contents, separatorConfig) => {
    const { separatorGetter, drawSeparator } = separatorConfig;
    const contentSize = contents.length;
    const result = [];
    if (drawSeparator(0, contentSize)) {
        result.push(separatorGetter(0, contentSize));
    }
    contents.forEach((content, contentIndex) => {
        result.push(content);
        // Only append the middle separator if the content is not the last
        if (contentIndex + 1 < contentSize && drawSeparator(contentIndex + 1, contentSize)) {
            result.push(separatorGetter(contentIndex + 1, contentSize));
        }
    });
    if (drawSeparator(contentSize, contentSize)) {
        result.push(separatorGetter(contentSize, contentSize));
    }
    return result.join('');
};
exports.drawContent = drawContent;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635634, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.drawRow = void 0;
const drawContent_1 = require("./drawContent");
const drawRow = (row, config) => {
    const { border, drawVerticalLine } = config;
    return drawContent_1.drawContent(row, {
        drawSeparator: drawVerticalLine,
        separatorGetter: (index, columnCount) => {
            if (index === 0) {
                return border.bodyLeft;
            }
            if (index === columnCount) {
                return border.bodyRight;
            }
            return border.bodyJoin;
        },
    }) + '\n';
};
exports.drawRow = drawRow;

}, function(modId) { var map = {"./drawContent":1736920635633}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635635, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeStreamConfig = void 0;
const lodash_clonedeep_1 = __importDefault(require("lodash.clonedeep"));
const utils_1 = require("./utils");
const validateConfig_1 = require("./validateConfig");
/**
 * Creates a configuration for every column using default
 * values for the missing configuration properties.
 */
const makeColumnsConfig = (columnCount, columns = {}, columnDefault) => {
    return Array.from({ length: columnCount }).map((_, index) => {
        return {
            alignment: 'left',
            paddingLeft: 1,
            paddingRight: 1,
            truncate: Number.POSITIVE_INFINITY,
            verticalAlignment: 'top',
            wrapWord: false,
            ...columnDefault,
            ...columns[index],
        };
    });
};
/**
 * Makes a new configuration object out of the userConfig object
 * using default values for the missing configuration properties.
 */
const makeStreamConfig = (userConfig) => {
    validateConfig_1.validateConfig('streamConfig.json', userConfig);
    const config = lodash_clonedeep_1.default(userConfig);
    if (config.columnDefault.width === undefined) {
        throw new Error('Must provide config.columnDefault.width when creating a stream.');
    }
    return {
        drawVerticalLine: () => {
            return true;
        },
        ...config,
        border: utils_1.makeBorderConfig(config.border),
        columns: makeColumnsConfig(config.columnCount, config.columns, config.columnDefault),
    };
};
exports.makeStreamConfig = makeStreamConfig;

}, function(modId) { var map = {"./utils":1736920635625,"./validateConfig":1736920635636}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635636, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateConfig = void 0;
const validators_1 = __importDefault(require("./generated/validators"));
const validateConfig = (schemaId, config) => {
    const validate = validators_1.default[schemaId];
    if (!validate(config) && validate.errors) {
        const errors = validate.errors.map((error) => {
            return {
                message: error.message,
                params: error.params,
                schemaPath: error.schemaPath,
            };
        });
        /* eslint-disable no-console */
        console.log('config', config);
        console.log('errors', errors);
        /* eslint-enable no-console */
        throw new Error('Invalid config.');
    }
};
exports.validateConfig = validateConfig;

}, function(modId) { var map = {"./generated/validators":1736920635637}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635637, function(require, module, exports) {

exports["config.json"] = validate43;
const schema13 = {
    "$id": "config.json",
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
        "border": {
            "$ref": "shared.json#/definitions/borders"
        },
        "header": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "alignment": {
                    "$ref": "shared.json#/definitions/alignment"
                },
                "wrapWord": {
                    "type": "boolean"
                },
                "truncate": {
                    "type": "integer"
                },
                "paddingLeft": {
                    "type": "integer"
                },
                "paddingRight": {
                    "type": "integer"
                }
            },
            "required": ["content"],
            "additionalProperties": false
        },
        "columns": {
            "$ref": "shared.json#/definitions/columns"
        },
        "columnDefault": {
            "$ref": "shared.json#/definitions/column"
        },
        "drawVerticalLine": {
            "typeof": "function"
        },
        "drawHorizontalLine": {
            "typeof": "function"
        },
        "singleLine": {
            "typeof": "boolean"
        }
    },
    "additionalProperties": false
};
const schema15 = {
    "type": "object",
    "properties": {
        "topBody": {
            "$ref": "#/definitions/border"
        },
        "topJoin": {
            "$ref": "#/definitions/border"
        },
        "topLeft": {
            "$ref": "#/definitions/border"
        },
        "topRight": {
            "$ref": "#/definitions/border"
        },
        "bottomBody": {
            "$ref": "#/definitions/border"
        },
        "bottomJoin": {
            "$ref": "#/definitions/border"
        },
        "bottomLeft": {
            "$ref": "#/definitions/border"
        },
        "bottomRight": {
            "$ref": "#/definitions/border"
        },
        "bodyLeft": {
            "$ref": "#/definitions/border"
        },
        "bodyRight": {
            "$ref": "#/definitions/border"
        },
        "bodyJoin": {
            "$ref": "#/definitions/border"
        },
        "headerJoin": {
            "$ref": "#/definitions/border"
        },
        "joinBody": {
            "$ref": "#/definitions/border"
        },
        "joinLeft": {
            "$ref": "#/definitions/border"
        },
        "joinRight": {
            "$ref": "#/definitions/border"
        },
        "joinJoin": {
            "$ref": "#/definitions/border"
        }
    },
    "additionalProperties": false
};
const func8 = Object.prototype.hasOwnProperty;
const schema16 = {
    "type": "string"
};
function validate46(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (typeof data !== "string") {
        const err0 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "string"
            },
            message: "must be string"
        };
        if (vErrors === null) {
            vErrors = [err0];
        }
        else {
            vErrors.push(err0);
        }
        errors++;
    }
    validate46.errors = vErrors;
    return errors === 0;
}
function validate45(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(func8.call(schema15.properties, key0))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.topBody !== undefined) {
            if (!(validate46(data.topBody, {
                instancePath: instancePath + "/topBody",
                parentData: data,
                parentDataProperty: "topBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topJoin !== undefined) {
            if (!(validate46(data.topJoin, {
                instancePath: instancePath + "/topJoin",
                parentData: data,
                parentDataProperty: "topJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topLeft !== undefined) {
            if (!(validate46(data.topLeft, {
                instancePath: instancePath + "/topLeft",
                parentData: data,
                parentDataProperty: "topLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topRight !== undefined) {
            if (!(validate46(data.topRight, {
                instancePath: instancePath + "/topRight",
                parentData: data,
                parentDataProperty: "topRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomBody !== undefined) {
            if (!(validate46(data.bottomBody, {
                instancePath: instancePath + "/bottomBody",
                parentData: data,
                parentDataProperty: "bottomBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomJoin !== undefined) {
            if (!(validate46(data.bottomJoin, {
                instancePath: instancePath + "/bottomJoin",
                parentData: data,
                parentDataProperty: "bottomJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomLeft !== undefined) {
            if (!(validate46(data.bottomLeft, {
                instancePath: instancePath + "/bottomLeft",
                parentData: data,
                parentDataProperty: "bottomLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomRight !== undefined) {
            if (!(validate46(data.bottomRight, {
                instancePath: instancePath + "/bottomRight",
                parentData: data,
                parentDataProperty: "bottomRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyLeft !== undefined) {
            if (!(validate46(data.bodyLeft, {
                instancePath: instancePath + "/bodyLeft",
                parentData: data,
                parentDataProperty: "bodyLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyRight !== undefined) {
            if (!(validate46(data.bodyRight, {
                instancePath: instancePath + "/bodyRight",
                parentData: data,
                parentDataProperty: "bodyRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyJoin !== undefined) {
            if (!(validate46(data.bodyJoin, {
                instancePath: instancePath + "/bodyJoin",
                parentData: data,
                parentDataProperty: "bodyJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.headerJoin !== undefined) {
            if (!(validate46(data.headerJoin, {
                instancePath: instancePath + "/headerJoin",
                parentData: data,
                parentDataProperty: "headerJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinBody !== undefined) {
            if (!(validate46(data.joinBody, {
                instancePath: instancePath + "/joinBody",
                parentData: data,
                parentDataProperty: "joinBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinLeft !== undefined) {
            if (!(validate46(data.joinLeft, {
                instancePath: instancePath + "/joinLeft",
                parentData: data,
                parentDataProperty: "joinLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinRight !== undefined) {
            if (!(validate46(data.joinRight, {
                instancePath: instancePath + "/joinRight",
                parentData: data,
                parentDataProperty: "joinRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinJoin !== undefined) {
            if (!(validate46(data.joinJoin, {
                instancePath: instancePath + "/joinJoin",
                parentData: data,
                parentDataProperty: "joinJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
    }
    else {
        const err1 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    validate45.errors = vErrors;
    return errors === 0;
}
const schema17 = {
    "type": "string",
    "enum": ["left", "right", "center", "justify"]
};
const func0 = require("ajv/dist/runtime/equal").default;
function validate64(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (typeof data !== "string") {
        const err0 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "string"
            },
            message: "must be string"
        };
        if (vErrors === null) {
            vErrors = [err0];
        }
        else {
            vErrors.push(err0);
        }
        errors++;
    }
    if (!((((data === "left") || (data === "right")) || (data === "center")) || (data === "justify"))) {
        const err1 = {
            instancePath,
            schemaPath: "#/enum",
            keyword: "enum",
            params: {
                allowedValues: schema17.enum
            },
            message: "must be equal to one of the allowed values"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    validate64.errors = vErrors;
    return errors === 0;
}
const schema18 = {
    "oneOf": [{
            "type": "object",
            "patternProperties": {
                "^[0-9]+$": {
                    "$ref": "#/definitions/column"
                }
            },
            "additionalProperties": false
        }, {
            "type": "array",
            "items": {
                "$ref": "#/definitions/column"
            }
        }]
};
const pattern0 = new RegExp("^[0-9]+$", "u");
const schema19 = {
    "type": "object",
    "properties": {
        "alignment": {
            "$ref": "#/definitions/alignment"
        },
        "verticalAlignment": {
            "type": "string",
            "enum": ["top", "middle", "bottom"]
        },
        "width": {
            "type": "integer",
            "minimum": 1
        },
        "wrapWord": {
            "type": "boolean"
        },
        "truncate": {
            "type": "integer"
        },
        "paddingLeft": {
            "type": "integer"
        },
        "paddingRight": {
            "type": "integer"
        }
    },
    "additionalProperties": false
};
function validate68(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (typeof data !== "string") {
        const err0 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "string"
            },
            message: "must be string"
        };
        if (vErrors === null) {
            vErrors = [err0];
        }
        else {
            vErrors.push(err0);
        }
        errors++;
    }
    if (!((((data === "left") || (data === "right")) || (data === "center")) || (data === "justify"))) {
        const err1 = {
            instancePath,
            schemaPath: "#/enum",
            keyword: "enum",
            params: {
                allowedValues: schema17.enum
            },
            message: "must be equal to one of the allowed values"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    validate68.errors = vErrors;
    return errors === 0;
}
function validate67(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(((((((key0 === "alignment") || (key0 === "verticalAlignment")) || (key0 === "width")) || (key0 === "wrapWord")) || (key0 === "truncate")) || (key0 === "paddingLeft")) || (key0 === "paddingRight"))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.alignment !== undefined) {
            if (!(validate68(data.alignment, {
                instancePath: instancePath + "/alignment",
                parentData: data,
                parentDataProperty: "alignment",
                rootData
            }))) {
                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);
                errors = vErrors.length;
            }
        }
        if (data.verticalAlignment !== undefined) {
            let data1 = data.verticalAlignment;
            if (typeof data1 !== "string") {
                const err1 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/type",
                    keyword: "type",
                    params: {
                        type: "string"
                    },
                    message: "must be string"
                };
                if (vErrors === null) {
                    vErrors = [err1];
                }
                else {
                    vErrors.push(err1);
                }
                errors++;
            }
            if (!(((data1 === "top") || (data1 === "middle")) || (data1 === "bottom"))) {
                const err2 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/enum",
                    keyword: "enum",
                    params: {
                        allowedValues: schema19.properties.verticalAlignment.enum
                    },
                    message: "must be equal to one of the allowed values"
                };
                if (vErrors === null) {
                    vErrors = [err2];
                }
                else {
                    vErrors.push(err2);
                }
                errors++;
            }
        }
        if (data.width !== undefined) {
            let data2 = data.width;
            if (!(((typeof data2 == "number") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {
                const err3 = {
                    instancePath: instancePath + "/width",
                    schemaPath: "#/properties/width/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err3];
                }
                else {
                    vErrors.push(err3);
                }
                errors++;
            }
            if ((typeof data2 == "number") && (isFinite(data2))) {
                if (data2 < 1 || isNaN(data2)) {
                    const err4 = {
                        instancePath: instancePath + "/width",
                        schemaPath: "#/properties/width/minimum",
                        keyword: "minimum",
                        params: {
                            comparison: ">=",
                            limit: 1
                        },
                        message: "must be >= 1"
                    };
                    if (vErrors === null) {
                        vErrors = [err4];
                    }
                    else {
                        vErrors.push(err4);
                    }
                    errors++;
                }
            }
        }
        if (data.wrapWord !== undefined) {
            if (typeof data.wrapWord !== "boolean") {
                const err5 = {
                    instancePath: instancePath + "/wrapWord",
                    schemaPath: "#/properties/wrapWord/type",
                    keyword: "type",
                    params: {
                        type: "boolean"
                    },
                    message: "must be boolean"
                };
                if (vErrors === null) {
                    vErrors = [err5];
                }
                else {
                    vErrors.push(err5);
                }
                errors++;
            }
        }
        if (data.truncate !== undefined) {
            let data4 = data.truncate;
            if (!(((typeof data4 == "number") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {
                const err6 = {
                    instancePath: instancePath + "/truncate",
                    schemaPath: "#/properties/truncate/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err6];
                }
                else {
                    vErrors.push(err6);
                }
                errors++;
            }
        }
        if (data.paddingLeft !== undefined) {
            let data5 = data.paddingLeft;
            if (!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {
                const err7 = {
                    instancePath: instancePath + "/paddingLeft",
                    schemaPath: "#/properties/paddingLeft/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err7];
                }
                else {
                    vErrors.push(err7);
                }
                errors++;
            }
        }
        if (data.paddingRight !== undefined) {
            let data6 = data.paddingRight;
            if (!(((typeof data6 == "number") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {
                const err8 = {
                    instancePath: instancePath + "/paddingRight",
                    schemaPath: "#/properties/paddingRight/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err8];
                }
                else {
                    vErrors.push(err8);
                }
                errors++;
            }
        }
    }
    else {
        const err9 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err9];
        }
        else {
            vErrors.push(err9);
        }
        errors++;
    }
    validate67.errors = vErrors;
    return errors === 0;
}
function validate66(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    const _errs0 = errors;
    let valid0 = false;
    let passing0 = null;
    const _errs1 = errors;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(pattern0.test(key0))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/oneOf/0/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        for (const key1 in data) {
            if (pattern0.test(key1)) {
                if (!(validate67(data[key1], {
                    instancePath: instancePath + "/" + key1.replace(/~/g, "~0").replace(/\//g, "~1"),
                    parentData: data,
                    parentDataProperty: key1,
                    rootData
                }))) {
                    vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);
                    errors = vErrors.length;
                }
            }
        }
    }
    else {
        const err1 = {
            instancePath,
            schemaPath: "#/oneOf/0/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    var _valid0 = _errs1 === errors;
    if (_valid0) {
        valid0 = true;
        passing0 = 0;
    }
    const _errs5 = errors;
    if (Array.isArray(data)) {
        const len0 = data.length;
        for (let i0 = 0; i0 < len0; i0++) {
            if (!(validate67(data[i0], {
                instancePath: instancePath + "/" + i0,
                parentData: data,
                parentDataProperty: i0,
                rootData
            }))) {
                vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);
                errors = vErrors.length;
            }
        }
    }
    else {
        const err2 = {
            instancePath,
            schemaPath: "#/oneOf/1/type",
            keyword: "type",
            params: {
                type: "array"
            },
            message: "must be array"
        };
        if (vErrors === null) {
            vErrors = [err2];
        }
        else {
            vErrors.push(err2);
        }
        errors++;
    }
    var _valid0 = _errs5 === errors;
    if (_valid0 && valid0) {
        valid0 = false;
        passing0 = [passing0, 1];
    }
    else {
        if (_valid0) {
            valid0 = true;
            passing0 = 1;
        }
    }
    if (!valid0) {
        const err3 = {
            instancePath,
            schemaPath: "#/oneOf",
            keyword: "oneOf",
            params: {
                passingSchemas: passing0
            },
            message: "must match exactly one schema in oneOf"
        };
        if (vErrors === null) {
            vErrors = [err3];
        }
        else {
            vErrors.push(err3);
        }
        errors++;
    }
    else {
        errors = _errs0;
        if (vErrors !== null) {
            if (_errs0) {
                vErrors.length = _errs0;
            }
            else {
                vErrors = null;
            }
        }
    }
    validate66.errors = vErrors;
    return errors === 0;
}
function validate73(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(((((((key0 === "alignment") || (key0 === "verticalAlignment")) || (key0 === "width")) || (key0 === "wrapWord")) || (key0 === "truncate")) || (key0 === "paddingLeft")) || (key0 === "paddingRight"))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.alignment !== undefined) {
            if (!(validate68(data.alignment, {
                instancePath: instancePath + "/alignment",
                parentData: data,
                parentDataProperty: "alignment",
                rootData
            }))) {
                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);
                errors = vErrors.length;
            }
        }
        if (data.verticalAlignment !== undefined) {
            let data1 = data.verticalAlignment;
            if (typeof data1 !== "string") {
                const err1 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/type",
                    keyword: "type",
                    params: {
                        type: "string"
                    },
                    message: "must be string"
                };
                if (vErrors === null) {
                    vErrors = [err1];
                }
                else {
                    vErrors.push(err1);
                }
                errors++;
            }
            if (!(((data1 === "top") || (data1 === "middle")) || (data1 === "bottom"))) {
                const err2 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/enum",
                    keyword: "enum",
                    params: {
                        allowedValues: schema19.properties.verticalAlignment.enum
                    },
                    message: "must be equal to one of the allowed values"
                };
                if (vErrors === null) {
                    vErrors = [err2];
                }
                else {
                    vErrors.push(err2);
                }
                errors++;
            }
        }
        if (data.width !== undefined) {
            let data2 = data.width;
            if (!(((typeof data2 == "number") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {
                const err3 = {
                    instancePath: instancePath + "/width",
                    schemaPath: "#/properties/width/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err3];
                }
                else {
                    vErrors.push(err3);
                }
                errors++;
            }
            if ((typeof data2 == "number") && (isFinite(data2))) {
                if (data2 < 1 || isNaN(data2)) {
                    const err4 = {
                        instancePath: instancePath + "/width",
                        schemaPath: "#/properties/width/minimum",
                        keyword: "minimum",
                        params: {
                            comparison: ">=",
                            limit: 1
                        },
                        message: "must be >= 1"
                    };
                    if (vErrors === null) {
                        vErrors = [err4];
                    }
                    else {
                        vErrors.push(err4);
                    }
                    errors++;
                }
            }
        }
        if (data.wrapWord !== undefined) {
            if (typeof data.wrapWord !== "boolean") {
                const err5 = {
                    instancePath: instancePath + "/wrapWord",
                    schemaPath: "#/properties/wrapWord/type",
                    keyword: "type",
                    params: {
                        type: "boolean"
                    },
                    message: "must be boolean"
                };
                if (vErrors === null) {
                    vErrors = [err5];
                }
                else {
                    vErrors.push(err5);
                }
                errors++;
            }
        }
        if (data.truncate !== undefined) {
            let data4 = data.truncate;
            if (!(((typeof data4 == "number") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {
                const err6 = {
                    instancePath: instancePath + "/truncate",
                    schemaPath: "#/properties/truncate/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err6];
                }
                else {
                    vErrors.push(err6);
                }
                errors++;
            }
        }
        if (data.paddingLeft !== undefined) {
            let data5 = data.paddingLeft;
            if (!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {
                const err7 = {
                    instancePath: instancePath + "/paddingLeft",
                    schemaPath: "#/properties/paddingLeft/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err7];
                }
                else {
                    vErrors.push(err7);
                }
                errors++;
            }
        }
        if (data.paddingRight !== undefined) {
            let data6 = data.paddingRight;
            if (!(((typeof data6 == "number") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {
                const err8 = {
                    instancePath: instancePath + "/paddingRight",
                    schemaPath: "#/properties/paddingRight/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err8];
                }
                else {
                    vErrors.push(err8);
                }
                errors++;
            }
        }
    }
    else {
        const err9 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err9];
        }
        else {
            vErrors.push(err9);
        }
        errors++;
    }
    validate73.errors = vErrors;
    return errors === 0;
}
function validate43(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    /*# sourceURL="config.json" */ ;
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(((((((key0 === "border") || (key0 === "header")) || (key0 === "columns")) || (key0 === "columnDefault")) || (key0 === "drawVerticalLine")) || (key0 === "drawHorizontalLine")) || (key0 === "singleLine"))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.border !== undefined) {
            if (!(validate45(data.border, {
                instancePath: instancePath + "/border",
                parentData: data,
                parentDataProperty: "border",
                rootData
            }))) {
                vErrors = vErrors === null ? validate45.errors : vErrors.concat(validate45.errors);
                errors = vErrors.length;
            }
        }
        if (data.header !== undefined) {
            let data1 = data.header;
            if (data1 && typeof data1 == "object" && !Array.isArray(data1)) {
                if (data1.content === undefined) {
                    const err1 = {
                        instancePath: instancePath + "/header",
                        schemaPath: "#/properties/header/required",
                        keyword: "required",
                        params: {
                            missingProperty: "content"
                        },
                        message: "must have required property '" + "content" + "'"
                    };
                    if (vErrors === null) {
                        vErrors = [err1];
                    }
                    else {
                        vErrors.push(err1);
                    }
                    errors++;
                }
                for (const key1 in data1) {
                    if (!((((((key1 === "content") || (key1 === "alignment")) || (key1 === "wrapWord")) || (key1 === "truncate")) || (key1 === "paddingLeft")) || (key1 === "paddingRight"))) {
                        const err2 = {
                            instancePath: instancePath + "/header",
                            schemaPath: "#/properties/header/additionalProperties",
                            keyword: "additionalProperties",
                            params: {
                                additionalProperty: key1
                            },
                            message: "must NOT have additional properties"
                        };
                        if (vErrors === null) {
                            vErrors = [err2];
                        }
                        else {
                            vErrors.push(err2);
                        }
                        errors++;
                    }
                }
                if (data1.content !== undefined) {
                    if (typeof data1.content !== "string") {
                        const err3 = {
                            instancePath: instancePath + "/header/content",
                            schemaPath: "#/properties/header/properties/content/type",
                            keyword: "type",
                            params: {
                                type: "string"
                            },
                            message: "must be string"
                        };
                        if (vErrors === null) {
                            vErrors = [err3];
                        }
                        else {
                            vErrors.push(err3);
                        }
                        errors++;
                    }
                }
                if (data1.alignment !== undefined) {
                    if (!(validate64(data1.alignment, {
                        instancePath: instancePath + "/header/alignment",
                        parentData: data1,
                        parentDataProperty: "alignment",
                        rootData
                    }))) {
                        vErrors = vErrors === null ? validate64.errors : vErrors.concat(validate64.errors);
                        errors = vErrors.length;
                    }
                }
                if (data1.wrapWord !== undefined) {
                    if (typeof data1.wrapWord !== "boolean") {
                        const err4 = {
                            instancePath: instancePath + "/header/wrapWord",
                            schemaPath: "#/properties/header/properties/wrapWord/type",
                            keyword: "type",
                            params: {
                                type: "boolean"
                            },
                            message: "must be boolean"
                        };
                        if (vErrors === null) {
                            vErrors = [err4];
                        }
                        else {
                            vErrors.push(err4);
                        }
                        errors++;
                    }
                }
                if (data1.truncate !== undefined) {
                    let data5 = data1.truncate;
                    if (!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {
                        const err5 = {
                            instancePath: instancePath + "/header/truncate",
                            schemaPath: "#/properties/header/properties/truncate/type",
                            keyword: "type",
                            params: {
                                type: "integer"
                            },
                            message: "must be integer"
                        };
                        if (vErrors === null) {
                            vErrors = [err5];
                        }
                        else {
                            vErrors.push(err5);
                        }
                        errors++;
                    }
                }
                if (data1.paddingLeft !== undefined) {
                    let data6 = data1.paddingLeft;
                    if (!(((typeof data6 == "number") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {
                        const err6 = {
                            instancePath: instancePath + "/header/paddingLeft",
                            schemaPath: "#/properties/header/properties/paddingLeft/type",
                            keyword: "type",
                            params: {
                                type: "integer"
                            },
                            message: "must be integer"
                        };
                        if (vErrors === null) {
                            vErrors = [err6];
                        }
                        else {
                            vErrors.push(err6);
                        }
                        errors++;
                    }
                }
                if (data1.paddingRight !== undefined) {
                    let data7 = data1.paddingRight;
                    if (!(((typeof data7 == "number") && (!(data7 % 1) && !isNaN(data7))) && (isFinite(data7)))) {
                        const err7 = {
                            instancePath: instancePath + "/header/paddingRight",
                            schemaPath: "#/properties/header/properties/paddingRight/type",
                            keyword: "type",
                            params: {
                                type: "integer"
                            },
                            message: "must be integer"
                        };
                        if (vErrors === null) {
                            vErrors = [err7];
                        }
                        else {
                            vErrors.push(err7);
                        }
                        errors++;
                    }
                }
            }
            else {
                const err8 = {
                    instancePath: instancePath + "/header",
                    schemaPath: "#/properties/header/type",
                    keyword: "type",
                    params: {
                        type: "object"
                    },
                    message: "must be object"
                };
                if (vErrors === null) {
                    vErrors = [err8];
                }
                else {
                    vErrors.push(err8);
                }
                errors++;
            }
        }
        if (data.columns !== undefined) {
            if (!(validate66(data.columns, {
                instancePath: instancePath + "/columns",
                parentData: data,
                parentDataProperty: "columns",
                rootData
            }))) {
                vErrors = vErrors === null ? validate66.errors : vErrors.concat(validate66.errors);
                errors = vErrors.length;
            }
        }
        if (data.columnDefault !== undefined) {
            if (!(validate73(data.columnDefault, {
                instancePath: instancePath + "/columnDefault",
                parentData: data,
                parentDataProperty: "columnDefault",
                rootData
            }))) {
                vErrors = vErrors === null ? validate73.errors : vErrors.concat(validate73.errors);
                errors = vErrors.length;
            }
        }
        if (data.drawVerticalLine !== undefined) {
            if (typeof data.drawVerticalLine != "function") {
                const err9 = {
                    instancePath: instancePath + "/drawVerticalLine",
                    schemaPath: "#/properties/drawVerticalLine/typeof",
                    keyword: "typeof",
                    params: {},
                    message: "should pass \"typeof\" keyword validation"
                };
                if (vErrors === null) {
                    vErrors = [err9];
                }
                else {
                    vErrors.push(err9);
                }
                errors++;
            }
        }
        if (data.drawHorizontalLine !== undefined) {
            if (typeof data.drawHorizontalLine != "function") {
                const err10 = {
                    instancePath: instancePath + "/drawHorizontalLine",
                    schemaPath: "#/properties/drawHorizontalLine/typeof",
                    keyword: "typeof",
                    params: {},
                    message: "should pass \"typeof\" keyword validation"
                };
                if (vErrors === null) {
                    vErrors = [err10];
                }
                else {
                    vErrors.push(err10);
                }
                errors++;
            }
        }
        if (data.singleLine !== undefined) {
            if (typeof data.singleLine != "boolean") {
                const err11 = {
                    instancePath: instancePath + "/singleLine",
                    schemaPath: "#/properties/singleLine/typeof",
                    keyword: "typeof",
                    params: {},
                    message: "should pass \"typeof\" keyword validation"
                };
                if (vErrors === null) {
                    vErrors = [err11];
                }
                else {
                    vErrors.push(err11);
                }
                errors++;
            }
        }
    }
    else {
        const err12 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err12];
        }
        else {
            vErrors.push(err12);
        }
        errors++;
    }
    validate43.errors = vErrors;
    return errors === 0;
}
exports["streamConfig.json"] = validate76;
const schema22 = {
    "$id": "streamConfig.json",
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
        "border": {
            "$ref": "shared.json#/definitions/borders"
        },
        "columns": {
            "$ref": "shared.json#/definitions/columns"
        },
        "columnDefault": {
            "$ref": "shared.json#/definitions/column"
        },
        "columnCount": {
            "type": "integer",
            "minimum": 1
        },
        "drawVerticalLine": {
            "typeof": "function"
        }
    },
    "required": ["columnDefault", "columnCount"],
    "additionalProperties": false
};
function validate77(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(func8.call(schema15.properties, key0))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.topBody !== undefined) {
            if (!(validate46(data.topBody, {
                instancePath: instancePath + "/topBody",
                parentData: data,
                parentDataProperty: "topBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topJoin !== undefined) {
            if (!(validate46(data.topJoin, {
                instancePath: instancePath + "/topJoin",
                parentData: data,
                parentDataProperty: "topJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topLeft !== undefined) {
            if (!(validate46(data.topLeft, {
                instancePath: instancePath + "/topLeft",
                parentData: data,
                parentDataProperty: "topLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.topRight !== undefined) {
            if (!(validate46(data.topRight, {
                instancePath: instancePath + "/topRight",
                parentData: data,
                parentDataProperty: "topRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomBody !== undefined) {
            if (!(validate46(data.bottomBody, {
                instancePath: instancePath + "/bottomBody",
                parentData: data,
                parentDataProperty: "bottomBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomJoin !== undefined) {
            if (!(validate46(data.bottomJoin, {
                instancePath: instancePath + "/bottomJoin",
                parentData: data,
                parentDataProperty: "bottomJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomLeft !== undefined) {
            if (!(validate46(data.bottomLeft, {
                instancePath: instancePath + "/bottomLeft",
                parentData: data,
                parentDataProperty: "bottomLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bottomRight !== undefined) {
            if (!(validate46(data.bottomRight, {
                instancePath: instancePath + "/bottomRight",
                parentData: data,
                parentDataProperty: "bottomRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyLeft !== undefined) {
            if (!(validate46(data.bodyLeft, {
                instancePath: instancePath + "/bodyLeft",
                parentData: data,
                parentDataProperty: "bodyLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyRight !== undefined) {
            if (!(validate46(data.bodyRight, {
                instancePath: instancePath + "/bodyRight",
                parentData: data,
                parentDataProperty: "bodyRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.bodyJoin !== undefined) {
            if (!(validate46(data.bodyJoin, {
                instancePath: instancePath + "/bodyJoin",
                parentData: data,
                parentDataProperty: "bodyJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.headerJoin !== undefined) {
            if (!(validate46(data.headerJoin, {
                instancePath: instancePath + "/headerJoin",
                parentData: data,
                parentDataProperty: "headerJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinBody !== undefined) {
            if (!(validate46(data.joinBody, {
                instancePath: instancePath + "/joinBody",
                parentData: data,
                parentDataProperty: "joinBody",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinLeft !== undefined) {
            if (!(validate46(data.joinLeft, {
                instancePath: instancePath + "/joinLeft",
                parentData: data,
                parentDataProperty: "joinLeft",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinRight !== undefined) {
            if (!(validate46(data.joinRight, {
                instancePath: instancePath + "/joinRight",
                parentData: data,
                parentDataProperty: "joinRight",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
        if (data.joinJoin !== undefined) {
            if (!(validate46(data.joinJoin, {
                instancePath: instancePath + "/joinJoin",
                parentData: data,
                parentDataProperty: "joinJoin",
                rootData
            }))) {
                vErrors = vErrors === null ? validate46.errors : vErrors.concat(validate46.errors);
                errors = vErrors.length;
            }
        }
    }
    else {
        const err1 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    validate77.errors = vErrors;
    return errors === 0;
}
function validate95(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    const _errs0 = errors;
    let valid0 = false;
    let passing0 = null;
    const _errs1 = errors;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(pattern0.test(key0))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/oneOf/0/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        for (const key1 in data) {
            if (pattern0.test(key1)) {
                if (!(validate67(data[key1], {
                    instancePath: instancePath + "/" + key1.replace(/~/g, "~0").replace(/\//g, "~1"),
                    parentData: data,
                    parentDataProperty: key1,
                    rootData
                }))) {
                    vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);
                    errors = vErrors.length;
                }
            }
        }
    }
    else {
        const err1 = {
            instancePath,
            schemaPath: "#/oneOf/0/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err1];
        }
        else {
            vErrors.push(err1);
        }
        errors++;
    }
    var _valid0 = _errs1 === errors;
    if (_valid0) {
        valid0 = true;
        passing0 = 0;
    }
    const _errs5 = errors;
    if (Array.isArray(data)) {
        const len0 = data.length;
        for (let i0 = 0; i0 < len0; i0++) {
            if (!(validate67(data[i0], {
                instancePath: instancePath + "/" + i0,
                parentData: data,
                parentDataProperty: i0,
                rootData
            }))) {
                vErrors = vErrors === null ? validate67.errors : vErrors.concat(validate67.errors);
                errors = vErrors.length;
            }
        }
    }
    else {
        const err2 = {
            instancePath,
            schemaPath: "#/oneOf/1/type",
            keyword: "type",
            params: {
                type: "array"
            },
            message: "must be array"
        };
        if (vErrors === null) {
            vErrors = [err2];
        }
        else {
            vErrors.push(err2);
        }
        errors++;
    }
    var _valid0 = _errs5 === errors;
    if (_valid0 && valid0) {
        valid0 = false;
        passing0 = [passing0, 1];
    }
    else {
        if (_valid0) {
            valid0 = true;
            passing0 = 1;
        }
    }
    if (!valid0) {
        const err3 = {
            instancePath,
            schemaPath: "#/oneOf",
            keyword: "oneOf",
            params: {
                passingSchemas: passing0
            },
            message: "must match exactly one schema in oneOf"
        };
        if (vErrors === null) {
            vErrors = [err3];
        }
        else {
            vErrors.push(err3);
        }
        errors++;
    }
    else {
        errors = _errs0;
        if (vErrors !== null) {
            if (_errs0) {
                vErrors.length = _errs0;
            }
            else {
                vErrors = null;
            }
        }
    }
    validate95.errors = vErrors;
    return errors === 0;
}
function validate99(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        for (const key0 in data) {
            if (!(((((((key0 === "alignment") || (key0 === "verticalAlignment")) || (key0 === "width")) || (key0 === "wrapWord")) || (key0 === "truncate")) || (key0 === "paddingLeft")) || (key0 === "paddingRight"))) {
                const err0 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err0];
                }
                else {
                    vErrors.push(err0);
                }
                errors++;
            }
        }
        if (data.alignment !== undefined) {
            if (!(validate68(data.alignment, {
                instancePath: instancePath + "/alignment",
                parentData: data,
                parentDataProperty: "alignment",
                rootData
            }))) {
                vErrors = vErrors === null ? validate68.errors : vErrors.concat(validate68.errors);
                errors = vErrors.length;
            }
        }
        if (data.verticalAlignment !== undefined) {
            let data1 = data.verticalAlignment;
            if (typeof data1 !== "string") {
                const err1 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/type",
                    keyword: "type",
                    params: {
                        type: "string"
                    },
                    message: "must be string"
                };
                if (vErrors === null) {
                    vErrors = [err1];
                }
                else {
                    vErrors.push(err1);
                }
                errors++;
            }
            if (!(((data1 === "top") || (data1 === "middle")) || (data1 === "bottom"))) {
                const err2 = {
                    instancePath: instancePath + "/verticalAlignment",
                    schemaPath: "#/properties/verticalAlignment/enum",
                    keyword: "enum",
                    params: {
                        allowedValues: schema19.properties.verticalAlignment.enum
                    },
                    message: "must be equal to one of the allowed values"
                };
                if (vErrors === null) {
                    vErrors = [err2];
                }
                else {
                    vErrors.push(err2);
                }
                errors++;
            }
        }
        if (data.width !== undefined) {
            let data2 = data.width;
            if (!(((typeof data2 == "number") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) {
                const err3 = {
                    instancePath: instancePath + "/width",
                    schemaPath: "#/properties/width/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err3];
                }
                else {
                    vErrors.push(err3);
                }
                errors++;
            }
            if ((typeof data2 == "number") && (isFinite(data2))) {
                if (data2 < 1 || isNaN(data2)) {
                    const err4 = {
                        instancePath: instancePath + "/width",
                        schemaPath: "#/properties/width/minimum",
                        keyword: "minimum",
                        params: {
                            comparison: ">=",
                            limit: 1
                        },
                        message: "must be >= 1"
                    };
                    if (vErrors === null) {
                        vErrors = [err4];
                    }
                    else {
                        vErrors.push(err4);
                    }
                    errors++;
                }
            }
        }
        if (data.wrapWord !== undefined) {
            if (typeof data.wrapWord !== "boolean") {
                const err5 = {
                    instancePath: instancePath + "/wrapWord",
                    schemaPath: "#/properties/wrapWord/type",
                    keyword: "type",
                    params: {
                        type: "boolean"
                    },
                    message: "must be boolean"
                };
                if (vErrors === null) {
                    vErrors = [err5];
                }
                else {
                    vErrors.push(err5);
                }
                errors++;
            }
        }
        if (data.truncate !== undefined) {
            let data4 = data.truncate;
            if (!(((typeof data4 == "number") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) {
                const err6 = {
                    instancePath: instancePath + "/truncate",
                    schemaPath: "#/properties/truncate/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err6];
                }
                else {
                    vErrors.push(err6);
                }
                errors++;
            }
        }
        if (data.paddingLeft !== undefined) {
            let data5 = data.paddingLeft;
            if (!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) {
                const err7 = {
                    instancePath: instancePath + "/paddingLeft",
                    schemaPath: "#/properties/paddingLeft/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err7];
                }
                else {
                    vErrors.push(err7);
                }
                errors++;
            }
        }
        if (data.paddingRight !== undefined) {
            let data6 = data.paddingRight;
            if (!(((typeof data6 == "number") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))) {
                const err8 = {
                    instancePath: instancePath + "/paddingRight",
                    schemaPath: "#/properties/paddingRight/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err8];
                }
                else {
                    vErrors.push(err8);
                }
                errors++;
            }
        }
    }
    else {
        const err9 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err9];
        }
        else {
            vErrors.push(err9);
        }
        errors++;
    }
    validate99.errors = vErrors;
    return errors === 0;
}
function validate76(data, { instancePath = "", parentData, parentDataProperty, rootData = data } = {}) {
    /*# sourceURL="streamConfig.json" */ ;
    let vErrors = null;
    let errors = 0;
    if (data && typeof data == "object" && !Array.isArray(data)) {
        if (data.columnDefault === undefined) {
            const err0 = {
                instancePath,
                schemaPath: "#/required",
                keyword: "required",
                params: {
                    missingProperty: "columnDefault"
                },
                message: "must have required property '" + "columnDefault" + "'"
            };
            if (vErrors === null) {
                vErrors = [err0];
            }
            else {
                vErrors.push(err0);
            }
            errors++;
        }
        if (data.columnCount === undefined) {
            const err1 = {
                instancePath,
                schemaPath: "#/required",
                keyword: "required",
                params: {
                    missingProperty: "columnCount"
                },
                message: "must have required property '" + "columnCount" + "'"
            };
            if (vErrors === null) {
                vErrors = [err1];
            }
            else {
                vErrors.push(err1);
            }
            errors++;
        }
        for (const key0 in data) {
            if (!(((((key0 === "border") || (key0 === "columns")) || (key0 === "columnDefault")) || (key0 === "columnCount")) || (key0 === "drawVerticalLine"))) {
                const err2 = {
                    instancePath,
                    schemaPath: "#/additionalProperties",
                    keyword: "additionalProperties",
                    params: {
                        additionalProperty: key0
                    },
                    message: "must NOT have additional properties"
                };
                if (vErrors === null) {
                    vErrors = [err2];
                }
                else {
                    vErrors.push(err2);
                }
                errors++;
            }
        }
        if (data.border !== undefined) {
            if (!(validate77(data.border, {
                instancePath: instancePath + "/border",
                parentData: data,
                parentDataProperty: "border",
                rootData
            }))) {
                vErrors = vErrors === null ? validate77.errors : vErrors.concat(validate77.errors);
                errors = vErrors.length;
            }
        }
        if (data.columns !== undefined) {
            if (!(validate95(data.columns, {
                instancePath: instancePath + "/columns",
                parentData: data,
                parentDataProperty: "columns",
                rootData
            }))) {
                vErrors = vErrors === null ? validate95.errors : vErrors.concat(validate95.errors);
                errors = vErrors.length;
            }
        }
        if (data.columnDefault !== undefined) {
            if (!(validate99(data.columnDefault, {
                instancePath: instancePath + "/columnDefault",
                parentData: data,
                parentDataProperty: "columnDefault",
                rootData
            }))) {
                vErrors = vErrors === null ? validate99.errors : vErrors.concat(validate99.errors);
                errors = vErrors.length;
            }
        }
        if (data.columnCount !== undefined) {
            let data3 = data.columnCount;
            if (!(((typeof data3 == "number") && (!(data3 % 1) && !isNaN(data3))) && (isFinite(data3)))) {
                const err3 = {
                    instancePath: instancePath + "/columnCount",
                    schemaPath: "#/properties/columnCount/type",
                    keyword: "type",
                    params: {
                        type: "integer"
                    },
                    message: "must be integer"
                };
                if (vErrors === null) {
                    vErrors = [err3];
                }
                else {
                    vErrors.push(err3);
                }
                errors++;
            }
            if ((typeof data3 == "number") && (isFinite(data3))) {
                if (data3 < 1 || isNaN(data3)) {
                    const err4 = {
                        instancePath: instancePath + "/columnCount",
                        schemaPath: "#/properties/columnCount/minimum",
                        keyword: "minimum",
                        params: {
                            comparison: ">=",
                            limit: 1
                        },
                        message: "must be >= 1"
                    };
                    if (vErrors === null) {
                        vErrors = [err4];
                    }
                    else {
                        vErrors.push(err4);
                    }
                    errors++;
                }
            }
        }
        if (data.drawVerticalLine !== undefined) {
            if (typeof data.drawVerticalLine != "function") {
                const err5 = {
                    instancePath: instancePath + "/drawVerticalLine",
                    schemaPath: "#/properties/drawVerticalLine/typeof",
                    keyword: "typeof",
                    params: {},
                    message: "should pass \"typeof\" keyword validation"
                };
                if (vErrors === null) {
                    vErrors = [err5];
                }
                else {
                    vErrors.push(err5);
                }
                errors++;
            }
        }
    }
    else {
        const err6 = {
            instancePath,
            schemaPath: "#/type",
            keyword: "type",
            params: {
                type: "object"
            },
            message: "must be object"
        };
        if (vErrors === null) {
            vErrors = [err6];
        }
        else {
            vErrors.push(err6);
        }
        errors++;
    }
    validate76.errors = vErrors;
    return errors === 0;
}

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635638, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.mapDataUsingRowHeights = void 0;
const wrapCell_1 = require("./wrapCell");
const createEmptyStrings = (length) => {
    return new Array(length).fill('');
};
const padCellVertically = (lines, rowHeight, columnConfig) => {
    const { verticalAlignment } = columnConfig;
    const availableLines = rowHeight - lines.length;
    if (verticalAlignment === 'top') {
        return [...lines, ...createEmptyStrings(availableLines)];
    }
    if (verticalAlignment === 'bottom') {
        return [...createEmptyStrings(availableLines), ...lines];
    }
    return [
        ...createEmptyStrings(Math.floor(availableLines / 2)),
        ...lines,
        ...createEmptyStrings(Math.ceil(availableLines / 2)),
    ];
};
const flatten = (array) => {
    return [].concat(...array);
};
const mapDataUsingRowHeights = (unmappedRows, rowHeights, config) => {
    const tableWidth = unmappedRows[0].length;
    const mappedRows = unmappedRows.map((unmappedRow, unmappedRowIndex) => {
        const outputRowHeight = rowHeights[unmappedRowIndex];
        const outputRow = Array.from({ length: outputRowHeight }, () => {
            return new Array(tableWidth).fill('');
        });
        unmappedRow.forEach((cell, cellIndex) => {
            const cellLines = wrapCell_1.wrapCell(cell, config.columns[cellIndex].width, config.columns[cellIndex].wrapWord);
            const paddedCellLines = padCellVertically(cellLines, outputRowHeight, config.columns[cellIndex]);
            paddedCellLines.forEach((cellLine, cellLineIndex) => {
                outputRow[cellLineIndex][cellIndex] = cellLine;
            });
        });
        return outputRow;
    });
    return flatten(mappedRows);
};
exports.mapDataUsingRowHeights = mapDataUsingRowHeights;

}, function(modId) { var map = {"./wrapCell":1736920635629}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635639, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.padTableData = exports.padString = void 0;
const padString = (input, paddingLeft, paddingRight) => {
    return ' '.repeat(paddingLeft) + input + ' '.repeat(paddingRight);
};
exports.padString = padString;
const padTableData = (rows, config) => {
    return rows.map((cells) => {
        return cells.map((cell, cellIndex) => {
            const { paddingLeft, paddingRight } = config.columns[cellIndex];
            return exports.padString(cell, paddingLeft, paddingRight);
        });
    });
};
exports.padTableData = padTableData;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635640, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.stringifyTableData = void 0;
const utils_1 = require("./utils");
const stringifyTableData = (rows) => {
    return rows.map((cells) => {
        return cells.map((cell) => {
            return utils_1.normalizeString(String(cell));
        });
    });
};
exports.stringifyTableData = stringifyTableData;

}, function(modId) { var map = {"./utils":1736920635625}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635641, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.truncateTableData = exports.truncateString = void 0;
const lodash_truncate_1 = __importDefault(require("lodash.truncate"));
const truncateString = (input, length) => {
    return lodash_truncate_1.default(input, { length,
        omission: '…' });
};
exports.truncateString = truncateString;
/**
 * @todo Make it work with ASCII content.
 */
const truncateTableData = (rows, config) => {
    return rows.map((cells) => {
        return cells.map((cell, cellIndex) => {
            return exports.truncateString(cell, config.columns[cellIndex].truncate);
        });
    });
};
exports.truncateTableData = truncateTableData;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635642, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.table = void 0;
const alignTableData_1 = require("./alignTableData");
const calculateCellWidths_1 = require("./calculateCellWidths");
const calculateRowHeights_1 = require("./calculateRowHeights");
const drawTable_1 = require("./drawTable");
const makeTableConfig_1 = require("./makeTableConfig");
const mapDataUsingRowHeights_1 = require("./mapDataUsingRowHeights");
const padTableData_1 = require("./padTableData");
const stringifyTableData_1 = require("./stringifyTableData");
const truncateTableData_1 = require("./truncateTableData");
const validateTableData_1 = require("./validateTableData");
const table = (data, userConfig = {}) => {
    validateTableData_1.validateTableData(data);
    let rows = stringifyTableData_1.stringifyTableData(data);
    const config = makeTableConfig_1.makeTableConfig(rows, userConfig);
    rows = truncateTableData_1.truncateTableData(rows, config);
    const rowHeights = calculateRowHeights_1.calculateRowHeights(rows, config);
    rows = mapDataUsingRowHeights_1.mapDataUsingRowHeights(rows, rowHeights, config);
    rows = alignTableData_1.alignTableData(rows, config);
    rows = padTableData_1.padTableData(rows, config);
    const cellWidths = calculateCellWidths_1.calculateCellWidths(rows[0]);
    return drawTable_1.drawTable(rows, cellWidths, rowHeights, config);
};
exports.table = table;

}, function(modId) { var map = {"./alignTableData":1736920635623,"./calculateCellWidths":1736920635643,"./calculateRowHeights":1736920635627,"./drawTable":1736920635644,"./makeTableConfig":1736920635646,"./mapDataUsingRowHeights":1736920635638,"./padTableData":1736920635639,"./stringifyTableData":1736920635640,"./truncateTableData":1736920635641,"./validateTableData":1736920635648}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635643, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateCellWidths = void 0;
const string_width_1 = __importDefault(require("string-width"));
/**
 * Calculates width of each cell contents in a row.
 */
const calculateCellWidths = (cells) => {
    return cells.map((cell) => {
        return Math.max(...cell.split('\n').map(string_width_1.default));
    });
};
exports.calculateCellWidths = calculateCellWidths;

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635644, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.drawTable = void 0;
const string_width_1 = __importDefault(require("string-width"));
const drawBorder_1 = require("./drawBorder");
const drawContent_1 = require("./drawContent");
const drawHeader_1 = require("./drawHeader");
const drawRow_1 = require("./drawRow");
const utils_1 = require("./utils");
const drawTable = (rows, columnWidths, rowHeights, config) => {
    const { drawHorizontalLine, singleLine, } = config;
    const contents = utils_1.groupBySizes(rows, rowHeights).map((group) => {
        return group.map((row) => {
            return drawRow_1.drawRow(row, config);
        }).join('');
    });
    if (config.header) {
        // assume that topLeft/right border have width = 1
        const headerWidth = string_width_1.default(drawRow_1.drawRow(rows[0], config)) - 2 -
            config.header.paddingLeft - config.header.paddingRight;
        const header = drawHeader_1.drawHeader(headerWidth, config);
        contents.unshift(header);
    }
    return drawContent_1.drawContent(contents, {
        drawSeparator: (index, size) => {
            // Top/bottom border
            if (index === 0 || index === size) {
                return drawHorizontalLine(index, size);
            }
            return !singleLine && drawHorizontalLine(index, size);
        },
        separatorGetter: drawBorder_1.createTableBorderGetter(columnWidths, config),
    });
};
exports.drawTable = drawTable;

}, function(modId) { var map = {"./drawBorder":1736920635632,"./drawContent":1736920635633,"./drawHeader":1736920635645,"./drawRow":1736920635634,"./utils":1736920635625}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635645, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.drawHeader = void 0;
const alignString_1 = require("./alignString");
const drawRow_1 = require("./drawRow");
const padTableData_1 = require("./padTableData");
const truncateTableData_1 = require("./truncateTableData");
const wrapCell_1 = require("./wrapCell");
const drawHeader = (width, config) => {
    if (!config.header) {
        throw new Error('Can not draw header without header configuration');
    }
    const { alignment, paddingRight, paddingLeft, wrapWord } = config.header;
    let content = config.header.content;
    content = truncateTableData_1.truncateString(content, config.header.truncate);
    const headerLines = wrapCell_1.wrapCell(content, width, wrapWord);
    return headerLines.map((headerLine) => {
        let line = alignString_1.alignString(headerLine, width, alignment);
        line = padTableData_1.padString(line, paddingLeft, paddingRight);
        return drawRow_1.drawRow([line], {
            ...config,
            drawVerticalLine: (index) => {
                const columnCount = config.columns.length;
                return config.drawVerticalLine(index === 0 ? 0 : columnCount, columnCount);
            },
        });
    }).join('');
};
exports.drawHeader = drawHeader;

}, function(modId) { var map = {"./alignString":1736920635624,"./drawRow":1736920635634,"./padTableData":1736920635639,"./truncateTableData":1736920635641,"./wrapCell":1736920635629}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635646, function(require, module, exports) {

var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeTableConfig = void 0;
const lodash_clonedeep_1 = __importDefault(require("lodash.clonedeep"));
const calculateColumnWidths_1 = __importDefault(require("./calculateColumnWidths"));
const utils_1 = require("./utils");
const validateConfig_1 = require("./validateConfig");
/**
 * Creates a configuration for every column using default
 * values for the missing configuration properties.
 */
const makeColumnsConfig = (rows, columns, columnDefault) => {
    const columnWidths = calculateColumnWidths_1.default(rows);
    return rows[0].map((_, columnIndex) => {
        return {
            alignment: 'left',
            paddingLeft: 1,
            paddingRight: 1,
            truncate: Number.POSITIVE_INFINITY,
            verticalAlignment: 'top',
            width: columnWidths[columnIndex],
            wrapWord: false,
            ...columnDefault,
            ...columns === null || columns === void 0 ? void 0 : columns[columnIndex],
        };
    });
};
const makeHeaderConfig = (config) => {
    if (!config.header) {
        return undefined;
    }
    return {
        alignment: 'center',
        paddingLeft: 1,
        paddingRight: 1,
        truncate: Number.POSITIVE_INFINITY,
        wrapWord: false,
        ...config.header,
    };
};
/**
 * Makes a new configuration object out of the userConfig object
 * using default values for the missing configuration properties.
 */
const makeTableConfig = (rows, userConfig = {}) => {
    var _a, _b, _c;
    validateConfig_1.validateConfig('config.json', userConfig);
    const config = lodash_clonedeep_1.default(userConfig);
    return {
        ...config,
        border: utils_1.makeBorderConfig(config.border),
        columns: makeColumnsConfig(rows, config.columns, config.columnDefault),
        drawHorizontalLine: (_a = config.drawHorizontalLine) !== null && _a !== void 0 ? _a : (() => {
            return true;
        }),
        drawVerticalLine: (_b = config.drawVerticalLine) !== null && _b !== void 0 ? _b : (() => {
            return true;
        }),
        header: makeHeaderConfig(config),
        singleLine: (_c = config.singleLine) !== null && _c !== void 0 ? _c : false,
    };
};
exports.makeTableConfig = makeTableConfig;

}, function(modId) { var map = {"./calculateColumnWidths":1736920635647,"./utils":1736920635625,"./validateConfig":1736920635636}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635647, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
const calculateCellWidths_1 = require("./calculateCellWidths");
/**
 * Produces an array of values that describe the largest value length (width) in every column.
 */
exports.default = (rows) => {
    const columnWidths = new Array(rows[0].length).fill(0);
    rows.forEach((row) => {
        const cellWidths = calculateCellWidths_1.calculateCellWidths(row);
        cellWidths.forEach((cellWidth, cellIndex) => {
            columnWidths[cellIndex] = Math.max(columnWidths[cellIndex], cellWidth);
        });
    });
    return columnWidths;
};

}, function(modId) { var map = {"./calculateCellWidths":1736920635643}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635648, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });
exports.validateTableData = void 0;
const utils_1 = require("./utils");
const validateTableData = (rows) => {
    if (!Array.isArray(rows)) {
        throw new TypeError('Table data must be an array.');
    }
    if (rows.length === 0) {
        throw new Error('Table must define at least one row.');
    }
    if (rows[0].length === 0) {
        throw new Error('Table must define at least one column.');
    }
    const columnNumber = rows[0].length;
    for (const row of rows) {
        if (!Array.isArray(row)) {
            throw new TypeError('Table row data must be an array.');
        }
        if (row.length !== columnNumber) {
            throw new Error('Table must have a consistent number of cells.');
        }
        for (const cell of row) {
            // eslint-disable-next-line no-control-regex
            if (/[\u0001-\u0006\u0008\u0009\u000B-\u001A]/.test(utils_1.normalizeString(String(cell)))) {
                throw new Error('Table data must not contain control characters.');
            }
        }
    }
};
exports.validateTableData = validateTableData;

}, function(modId) { var map = {"./utils":1736920635625}; return __REQUIRE__(map[modId], modId); })
__DEFINE__(1736920635649, function(require, module, exports) {

Object.defineProperty(exports, "__esModule", { value: true });

}, function(modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
return __REQUIRE__(1736920635621);
})()
//miniprogram-npm-outsideDeps=["string-width","slice-ansi","strip-ansi","lodash.clonedeep","ajv/dist/runtime/equal","lodash.truncate"]
//# sourceMappingURL=index.js.map