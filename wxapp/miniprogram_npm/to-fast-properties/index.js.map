{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nlet fastProto = null;\n\n// Creates an object with permanently fast properties in V8. See <PERSON>n Verwaest's\n// post https://medium.com/@tverwaes/setting-up-prototypes-in-v8-ec9c9491dfe2#5f62\n// for more details. Use %HasFastProperties(object) and the Node.js flag\n// --allow-natives-syntax to check whether an object has fast properties.\nfunction FastObject(o) {\n\t// A prototype object will have \"fast properties\" enabled once it is checked\n\t// against the inline property cache of a function, e.g. fastProto.property:\n\t// https://github.com/v8/v8/blob/6.0.122/test/mjsunit/fast-prototype.js#L48-L63\n\tif (fastProto !== null && typeof fastProto.property) {\n\t\tconst result = fastProto;\n\t\tfastProto = FastObject.prototype = null;\n\t\treturn result;\n\t}\n\tfastProto = FastObject.prototype = o == null ? Object.create(null) : o;\n\treturn new FastObject;\n}\n\n// Initialize the inline property cache of FastObject\nFastObject();\n\nmodule.exports = function toFastproperties(o) {\n\treturn FastObject(o);\n};\n"]}