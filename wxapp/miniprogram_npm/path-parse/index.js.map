{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar isWindows = process.platform === 'win32';\n\n// Regex to split a windows path into three parts: [*, device, slash,\n// tail] windows-only\nvar splitDeviceRe =\n    /^([a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/]+[^\\\\\\/]+)?([\\\\\\/])?([\\s\\S]*?)$/;\n\n// Regex to split the tail part of the above into [*, dir, basename, ext]\nvar splitTailRe =\n    /^([\\s\\S]*?)((?:\\.{1,2}|[^\\\\\\/]+?|)(\\.[^.\\/\\\\]*|))(?:[\\\\\\/]*)$/;\n\nvar win32 = {};\n\n// Function to split a filename into [root, dir, basename, ext]\nfunction win32SplitPath(filename) {\n  // Separate device+slash from tail\n  var result = splitDeviceRe.exec(filename),\n      device = (result[1] || '') + (result[2] || ''),\n      tail = result[3] || '';\n  // Split the tail into dir, basename and extension\n  var result2 = splitTailRe.exec(tail),\n      dir = result2[1],\n      basename = result2[2],\n      ext = result2[3];\n  return [device, dir, basename, ext];\n}\n\nwin32.parse = function(pathString) {\n  if (typeof pathString !== 'string') {\n    throw new TypeError(\n        \"Parameter 'pathString' must be a string, not \" + typeof pathString\n    );\n  }\n  var allParts = win32SplitPath(pathString);\n  if (!allParts || allParts.length !== 4) {\n    throw new TypeError(\"Invalid path '\" + pathString + \"'\");\n  }\n  return {\n    root: allParts[0],\n    dir: allParts[0] + allParts[1].slice(0, -1),\n    base: allParts[2],\n    ext: allParts[3],\n    name: allParts[2].slice(0, allParts[2].length - allParts[3].length)\n  };\n};\n\n\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nvar splitPathRe =\n    /^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;\nvar posix = {};\n\n\nfunction posixSplitPath(filename) {\n  return splitPathRe.exec(filename).slice(1);\n}\n\n\nposix.parse = function(pathString) {\n  if (typeof pathString !== 'string') {\n    throw new TypeError(\n        \"Parameter 'pathString' must be a string, not \" + typeof pathString\n    );\n  }\n  var allParts = posixSplitPath(pathString);\n  if (!allParts || allParts.length !== 4) {\n    throw new TypeError(\"Invalid path '\" + pathString + \"'\");\n  }\n  allParts[1] = allParts[1] || '';\n  allParts[2] = allParts[2] || '';\n  allParts[3] = allParts[3] || '';\n\n  return {\n    root: allParts[0],\n    dir: allParts[0] + allParts[1].slice(0, -1),\n    base: allParts[2],\n    ext: allParts[3],\n    name: allParts[2].slice(0, allParts[2].length - allParts[3].length)\n  };\n};\n\n\nif (isWindows)\n  module.exports = win32.parse;\nelse /* posix */\n  module.exports = posix.parse;\n\nmodule.exports.posix = posix.parse;\nmodule.exports.win32 = win32.parse;\n"]}