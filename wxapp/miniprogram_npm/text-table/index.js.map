{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = function (rows_, opts) {\n    if (!opts) opts = {};\n    var hsep = opts.hsep === undefined ? '  ' : opts.hsep;\n    var align = opts.align || [];\n    var stringLength = opts.stringLength\n        || function (s) { return String(s).length; }\n    ;\n    \n    var dotsizes = reduce(rows_, function (acc, row) {\n        forEach(row, function (c, ix) {\n            var n = dotindex(c);\n            if (!acc[ix] || n > acc[ix]) acc[ix] = n;\n        });\n        return acc;\n    }, []);\n    \n    var rows = map(rows_, function (row) {\n        return map(row, function (c_, ix) {\n            var c = String(c_);\n            if (align[ix] === '.') {\n                var index = dotindex(c);\n                var size = dotsizes[ix] + (/\\./.test(c) ? 1 : 2)\n                    - (stringLength(c) - index)\n                ;\n                return c + Array(size).join(' ');\n            }\n            else return c;\n        });\n    });\n    \n    var sizes = reduce(rows, function (acc, row) {\n        forEach(row, function (c, ix) {\n            var n = stringLength(c);\n            if (!acc[ix] || n > acc[ix]) acc[ix] = n;\n        });\n        return acc;\n    }, []);\n    \n    return map(rows, function (row) {\n        return map(row, function (c, ix) {\n            var n = (sizes[ix] - stringLength(c)) || 0;\n            var s = Array(Math.max(n + 1, 1)).join(' ');\n            if (align[ix] === 'r' || align[ix] === '.') {\n                return s + c;\n            }\n            if (align[ix] === 'c') {\n                return Array(Math.ceil(n / 2 + 1)).join(' ')\n                    + c + Array(Math.floor(n / 2 + 1)).join(' ')\n                ;\n            }\n            \n            return c + s;\n        }).join(hsep).replace(/\\s+$/, '');\n    }).join('\\n');\n};\n\nfunction dotindex (c) {\n    var m = /\\.[^.]*$/.exec(c);\n    return m ? m.index + 1 : c.length;\n}\n\nfunction reduce (xs, f, init) {\n    if (xs.reduce) return xs.reduce(f, init);\n    var i = 0;\n    var acc = arguments.length >= 3 ? init : xs[i++];\n    for (; i < xs.length; i++) {\n        f(acc, xs[i], i);\n    }\n    return acc;\n}\n\nfunction forEach (xs, f) {\n    if (xs.forEach) return xs.forEach(f);\n    for (var i = 0; i < xs.length; i++) {\n        f.call(xs, xs[i], i);\n    }\n}\n\nfunction map (xs, f) {\n    if (xs.map) return xs.map(f);\n    var res = [];\n    for (var i = 0; i < xs.length; i++) {\n        res.push(f.call(xs, xs[i], i));\n    }\n    return res;\n}\n"]}