{"version": 3, "sources": ["index.js", "parse-type.js", "check.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Generated by LiveScript 1.6.0\n(function(){\n  var VERSION, parseType, parsedTypeCheck, typeCheck;\n  VERSION = '0.4.0';\n  parseType = require('./parse-type');\n  parsedTypeCheck = require('./check');\n  typeCheck = function(type, input, options){\n    return parsedTypeCheck(parseType(type), input, options);\n  };\n  module.exports = {\n    VERSION: VERSION,\n    typeCheck: typeCheck,\n    parsedTypeCheck: parsedTypeCheck,\n    parseType: parseType\n  };\n}).call(this);\n", "// Generated by LiveScript 1.6.0\n(function(){\n  var identifierRegex, tokenRegex;\n  identifierRegex = /[\\$\\w]+/;\n  function peek(tokens){\n    var token;\n    token = tokens[0];\n    if (token == null) {\n      throw new Error('Unexpected end of input.');\n    }\n    return token;\n  }\n  function consumeIdent(tokens){\n    var token;\n    token = peek(tokens);\n    if (!identifierRegex.test(token)) {\n      throw new Error(\"Expected text, got '\" + token + \"' instead.\");\n    }\n    return tokens.shift();\n  }\n  function consumeOp(tokens, op){\n    var token;\n    token = peek(tokens);\n    if (token !== op) {\n      throw new Error(\"Expected '\" + op + \"', got '\" + token + \"' instead.\");\n    }\n    return tokens.shift();\n  }\n  function maybeConsumeOp(tokens, op){\n    var token;\n    token = tokens[0];\n    if (token === op) {\n      return tokens.shift();\n    } else {\n      return null;\n    }\n  }\n  function consumeArray(tokens){\n    var types;\n    consumeOp(tokens, '[');\n    if (peek(tokens) === ']') {\n      throw new Error(\"Must specify type of Array - eg. [Type], got [] instead.\");\n    }\n    types = consumeTypes(tokens);\n    consumeOp(tokens, ']');\n    return {\n      structure: 'array',\n      of: types\n    };\n  }\n  function consumeTuple(tokens){\n    var components;\n    components = [];\n    consumeOp(tokens, '(');\n    if (peek(tokens) === ')') {\n      throw new Error(\"Tuple must be of at least length 1 - eg. (Type), got () instead.\");\n    }\n    for (;;) {\n      components.push(consumeTypes(tokens));\n      maybeConsumeOp(tokens, ',');\n      if (')' === peek(tokens)) {\n        break;\n      }\n    }\n    consumeOp(tokens, ')');\n    return {\n      structure: 'tuple',\n      of: components\n    };\n  }\n  function consumeFields(tokens){\n    var fields, subset, ref$, key, types;\n    fields = {};\n    consumeOp(tokens, '{');\n    subset = false;\n    for (;;) {\n      if (maybeConsumeOp(tokens, '...')) {\n        subset = true;\n        break;\n      }\n      ref$ = consumeField(tokens), key = ref$[0], types = ref$[1];\n      fields[key] = types;\n      maybeConsumeOp(tokens, ',');\n      if ('}' === peek(tokens)) {\n        break;\n      }\n    }\n    consumeOp(tokens, '}');\n    return {\n      structure: 'fields',\n      of: fields,\n      subset: subset\n    };\n  }\n  function consumeField(tokens){\n    var key, types;\n    key = consumeIdent(tokens);\n    consumeOp(tokens, ':');\n    types = consumeTypes(tokens);\n    return [key, types];\n  }\n  function maybeConsumeStructure(tokens){\n    switch (tokens[0]) {\n    case '[':\n      return consumeArray(tokens);\n    case '(':\n      return consumeTuple(tokens);\n    case '{':\n      return consumeFields(tokens);\n    }\n  }\n  function consumeType(tokens){\n    var token, wildcard, type, structure;\n    token = peek(tokens);\n    wildcard = token === '*';\n    if (wildcard || identifierRegex.test(token)) {\n      type = wildcard\n        ? consumeOp(tokens, '*')\n        : consumeIdent(tokens);\n      structure = maybeConsumeStructure(tokens);\n      if (structure) {\n        return structure.type = type, structure;\n      } else {\n        return {\n          type: type\n        };\n      }\n    } else {\n      structure = maybeConsumeStructure(tokens);\n      if (!structure) {\n        throw new Error(\"Unexpected character: \" + token);\n      }\n      return structure;\n    }\n  }\n  function consumeTypes(tokens){\n    var lookahead, types, typesSoFar, typeObj, type, structure;\n    if ('::' === peek(tokens)) {\n      throw new Error(\"No comment before comment separator '::' found.\");\n    }\n    lookahead = tokens[1];\n    if (lookahead != null && lookahead === '::') {\n      tokens.shift();\n      tokens.shift();\n    }\n    types = [];\n    typesSoFar = {};\n    if ('Maybe' === peek(tokens)) {\n      tokens.shift();\n      types = [\n        {\n          type: 'Undefined'\n        }, {\n          type: 'Null'\n        }\n      ];\n      typesSoFar = {\n        Undefined: true,\n        Null: true\n      };\n    }\n    for (;;) {\n      typeObj = consumeType(tokens), type = typeObj.type, structure = typeObj.structure;\n      if (!typesSoFar[type]) {\n        types.push(typeObj);\n      }\n      if (structure == null) {\n        typesSoFar[type] = true;\n      }\n      if (!maybeConsumeOp(tokens, '|')) {\n        break;\n      }\n    }\n    return types;\n  }\n  tokenRegex = RegExp('\\\\.\\\\.\\\\.|::|->|' + identifierRegex.source + '|\\\\S', 'g');\n  module.exports = function(input){\n    var tokens, e;\n    if (!input.length) {\n      throw new Error('No type specified.');\n    }\n    tokens = input.match(tokenRegex) || [];\n    if (in$('->', tokens)) {\n      throw new Error(\"Function types are not supported.\\ To validate that something is a function, you may use 'Function'.\");\n    }\n    try {\n      return consumeTypes(tokens);\n    } catch (e$) {\n      e = e$;\n      throw new Error(e.message + \" - Remaining tokens: \" + JSON.stringify(tokens) + \" - Initial input: '\" + input + \"'\");\n    }\n  };\n  function in$(x, xs){\n    var i = -1, l = xs.length >>> 0;\n    while (++i < l) if (x === xs[i]) return true;\n    return false;\n  }\n}).call(this);\n", "// Generated by LiveScript 1.6.0\n(function(){\n  var ref$, any, all, isItNaN, types, defaultType, toString$ = {}.toString;\n  ref$ = require('prelude-ls'), any = ref$.any, all = ref$.all, isItNaN = ref$.isItNaN;\n  types = {\n    Number: {\n      typeOf: 'Number',\n      validate: function(it){\n        return !isItNaN(it);\n      }\n    },\n    NaN: {\n      typeOf: 'Number',\n      validate: isItNaN\n    },\n    Int: {\n      typeOf: 'Number',\n      validate: function(it){\n        return !isItNaN(it) && it % 1 === 0;\n      }\n    },\n    Float: {\n      typeOf: 'Number',\n      validate: function(it){\n        return !isItNaN(it);\n      }\n    },\n    Date: {\n      typeOf: 'Date',\n      validate: function(it){\n        return !isItNaN(it.getTime());\n      }\n    }\n  };\n  defaultType = {\n    array: 'Array',\n    tuple: 'Array'\n  };\n  function checkArray(input, type, options){\n    return all(function(it){\n      return checkMultiple(it, type.of, options);\n    }, input);\n  }\n  function checkTuple(input, type, options){\n    var i, i$, ref$, len$, types;\n    i = 0;\n    for (i$ = 0, len$ = (ref$ = type.of).length; i$ < len$; ++i$) {\n      types = ref$[i$];\n      if (!checkMultiple(input[i], types, options)) {\n        return false;\n      }\n      i++;\n    }\n    return input.length <= i;\n  }\n  function checkFields(input, type, options){\n    var inputKeys, numInputKeys, k, numOfKeys, key, ref$, types;\n    inputKeys = {};\n    numInputKeys = 0;\n    for (k in input) {\n      inputKeys[k] = true;\n      numInputKeys++;\n    }\n    numOfKeys = 0;\n    for (key in ref$ = type.of) {\n      types = ref$[key];\n      if (!checkMultiple(input[key], types, options)) {\n        return false;\n      }\n      if (inputKeys[key]) {\n        numOfKeys++;\n      }\n    }\n    return type.subset || numInputKeys === numOfKeys;\n  }\n  function checkStructure(input, type, options){\n    if (!(input instanceof Object)) {\n      return false;\n    }\n    switch (type.structure) {\n    case 'fields':\n      return checkFields(input, type, options);\n    case 'array':\n      return checkArray(input, type, options);\n    case 'tuple':\n      return checkTuple(input, type, options);\n    }\n  }\n  function check(input, typeObj, options){\n    var type, structure, setting, that;\n    type = typeObj.type, structure = typeObj.structure;\n    if (type) {\n      if (type === '*') {\n        return true;\n      }\n      setting = options.customTypes[type] || types[type];\n      if (setting) {\n        return (setting.typeOf === void 8 || setting.typeOf === toString$.call(input).slice(8, -1)) && setting.validate(input);\n      } else {\n        return type === toString$.call(input).slice(8, -1) && (!structure || checkStructure(input, typeObj, options));\n      }\n    } else if (structure) {\n      if (that = defaultType[structure]) {\n        if (that !== toString$.call(input).slice(8, -1)) {\n          return false;\n        }\n      }\n      return checkStructure(input, typeObj, options);\n    } else {\n      throw new Error(\"No type defined. Input: \" + input + \".\");\n    }\n  }\n  function checkMultiple(input, types, options){\n    if (toString$.call(types).slice(8, -1) !== 'Array') {\n      throw new Error(\"Types must be in an array. Input: \" + input + \".\");\n    }\n    return any(function(it){\n      return check(input, it, options);\n    }, types);\n  }\n  module.exports = function(parsedType, input, options){\n    options == null && (options = {});\n    if (options.customTypes == null) {\n      options.customTypes = {};\n    }\n    return checkMultiple(input, parsedType, options);\n  };\n}).call(this);\n"]}