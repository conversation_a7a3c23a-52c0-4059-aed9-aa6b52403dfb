{"version": 3, "sources": ["v8-compile-cache.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nconst Module = require('module');\nconst crypto = require('crypto');\nconst fs = require('fs');\nconst path = require('path');\nconst vm = require('vm');\nconst os = require('os');\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n\n//------------------------------------------------------------------------------\n// FileSystemBlobStore\n//------------------------------------------------------------------------------\n\nclass FileSystemBlobStore {\n  constructor(directory, prefix) {\n    const name = prefix ? slashEscape(prefix + '.') : '';\n    this._blobFilename = path.join(directory, name + 'BLOB');\n    this._mapFilename = path.join(directory, name + 'MAP');\n    this._lockFilename = path.join(directory, name + 'LOCK');\n    this._directory = directory;\n    this._load();\n  }\n\n  has(key, invalidationKey) {\n    if (hasOwnProperty.call(this._memoryBlobs, key)) {\n      return this._invalidationKeys[key] === invalidationKey;\n    } else if (hasOwnProperty.call(this._storedMap, key)) {\n      return this._storedMap[key][0] === invalidationKey;\n    }\n    return false;\n  }\n\n  get(key, invalidationKey) {\n    if (hasOwnProperty.call(this._memoryBlobs, key)) {\n      if (this._invalidationKeys[key] === invalidationKey) {\n        return this._memoryBlobs[key];\n      }\n    } else if (hasOwnProperty.call(this._storedMap, key)) {\n      const mapping = this._storedMap[key];\n      if (mapping[0] === invalidationKey) {\n        return this._storedBlob.slice(mapping[1], mapping[2]);\n      }\n    }\n  }\n\n  set(key, invalidationKey, buffer) {\n    this._invalidationKeys[key] = invalidationKey;\n    this._memoryBlobs[key] = buffer;\n    this._dirty = true;\n  }\n\n  delete(key) {\n    if (hasOwnProperty.call(this._memoryBlobs, key)) {\n      this._dirty = true;\n      delete this._memoryBlobs[key];\n    }\n    if (hasOwnProperty.call(this._invalidationKeys, key)) {\n      this._dirty = true;\n      delete this._invalidationKeys[key];\n    }\n    if (hasOwnProperty.call(this._storedMap, key)) {\n      this._dirty = true;\n      delete this._storedMap[key];\n    }\n  }\n\n  isDirty() {\n    return this._dirty;\n  }\n\n  save() {\n    const dump = this._getDump();\n    const blobToStore = Buffer.concat(dump[0]);\n    const mapToStore = JSON.stringify(dump[1]);\n\n    try {\n      mkdirpSync(this._directory);\n      fs.writeFileSync(this._lockFilename, 'LOCK', {flag: 'wx'});\n    } catch (error) {\n      // Swallow the exception if we fail to acquire the lock.\n      return false;\n    }\n\n    try {\n      fs.writeFileSync(this._blobFilename, blobToStore);\n      fs.writeFileSync(this._mapFilename, mapToStore);\n    } finally {\n      fs.unlinkSync(this._lockFilename);\n    }\n\n    return true;\n  }\n\n  _load() {\n    try {\n      this._storedBlob = fs.readFileSync(this._blobFilename);\n      this._storedMap = JSON.parse(fs.readFileSync(this._mapFilename));\n    } catch (e) {\n      this._storedBlob = Buffer.alloc(0);\n      this._storedMap = {};\n    }\n    this._dirty = false;\n    this._memoryBlobs = {};\n    this._invalidationKeys = {};\n  }\n\n  _getDump() {\n    const buffers = [];\n    const newMap = {};\n    let offset = 0;\n\n    function push(key, invalidationKey, buffer) {\n      buffers.push(buffer);\n      newMap[key] = [invalidationKey, offset, offset + buffer.length];\n      offset += buffer.length;\n    }\n\n    for (const key of Object.keys(this._memoryBlobs)) {\n      const buffer = this._memoryBlobs[key];\n      const invalidationKey = this._invalidationKeys[key];\n      push(key, invalidationKey, buffer);\n    }\n\n    for (const key of Object.keys(this._storedMap)) {\n      if (hasOwnProperty.call(newMap, key)) continue;\n      const mapping = this._storedMap[key];\n      const buffer = this._storedBlob.slice(mapping[1], mapping[2]);\n      push(key, mapping[0], buffer);\n    }\n\n    return [buffers, newMap];\n  }\n}\n\n//------------------------------------------------------------------------------\n// NativeCompileCache\n//------------------------------------------------------------------------------\n\nclass NativeCompileCache {\n  constructor() {\n    this._cacheStore = null;\n    this._previousModuleCompile = null;\n  }\n\n  setCacheStore(cacheStore) {\n    this._cacheStore = cacheStore;\n  }\n\n  install() {\n    const self = this;\n    const hasRequireResolvePaths = typeof require.resolve.paths === 'function';\n    this._previousModuleCompile = Module.prototype._compile;\n    Module.prototype._compile = function(content, filename) {\n      const mod = this;\n\n      function require(id) {\n        return mod.require(id);\n      }\n\n      // https://github.com/nodejs/node/blob/v10.15.3/lib/internal/modules/cjs/helpers.js#L28\n      function resolve(request, options) {\n        return Module._resolveFilename(request, mod, false, options);\n      }\n      require.resolve = resolve;\n\n      // https://github.com/nodejs/node/blob/v10.15.3/lib/internal/modules/cjs/helpers.js#L37\n      // resolve.resolve.paths was added in v8.9.0\n      if (hasRequireResolvePaths) {\n        resolve.paths = function paths(request) {\n          return Module._resolveLookupPaths(request, mod, true);\n        };\n      }\n\n      require.main = process.mainModule;\n\n      // Enable support to add extra extension types\n      require.extensions = Module._extensions;\n      require.cache = Module._cache;\n\n      const dirname = path.dirname(filename);\n\n      const compiledWrapper = self._moduleCompile(filename, content);\n\n      // We skip the debugger setup because by the time we run, node has already\n      // done that itself.\n\n      // `Buffer` is included for Electron.\n      // See https://github.com/zertosh/v8-compile-cache/pull/10#issuecomment-518042543\n      const args = [mod.exports, require, mod, filename, dirname, process, global, Buffer];\n      return compiledWrapper.apply(mod.exports, args);\n    };\n  }\n\n  uninstall() {\n    Module.prototype._compile = this._previousModuleCompile;\n  }\n\n  _moduleCompile(filename, content) {\n    // https://github.com/nodejs/node/blob/v7.5.0/lib/module.js#L511\n\n    // Remove shebang\n    var contLen = content.length;\n    if (contLen >= 2) {\n      if (content.charCodeAt(0) === 35/*#*/ &&\n          content.charCodeAt(1) === 33/*!*/) {\n        if (contLen === 2) {\n          // Exact match\n          content = '';\n        } else {\n          // Find end of shebang line and slice it off\n          var i = 2;\n          for (; i < contLen; ++i) {\n            var code = content.charCodeAt(i);\n            if (code === 10/*\\n*/ || code === 13/*\\r*/) break;\n          }\n          if (i === contLen) {\n            content = '';\n          } else {\n            // Note that this actually includes the newline character(s) in the\n            // new output. This duplicates the behavior of the regular\n            // expression that was previously used to replace the shebang line\n            content = content.slice(i);\n          }\n        }\n      }\n    }\n\n    // create wrapper function\n    var wrapper = Module.wrap(content);\n\n    var invalidationKey = crypto\n      .createHash('sha1')\n      .update(content, 'utf8')\n      .digest('hex');\n\n    var buffer = this._cacheStore.get(filename, invalidationKey);\n\n    var script = new vm.Script(wrapper, {\n      filename: filename,\n      lineOffset: 0,\n      displayErrors: true,\n      cachedData: buffer,\n      produceCachedData: true,\n    });\n\n    if (script.cachedDataProduced) {\n      this._cacheStore.set(filename, invalidationKey, script.cachedData);\n    } else if (script.cachedDataRejected) {\n      this._cacheStore.delete(filename);\n    }\n\n    var compiledWrapper = script.runInThisContext({\n      filename: filename,\n      lineOffset: 0,\n      columnOffset: 0,\n      displayErrors: true,\n    });\n\n    return compiledWrapper;\n  }\n}\n\n//------------------------------------------------------------------------------\n// utilities\n//\n// https://github.com/substack/node-mkdirp/blob/f2003bb/index.js#L55-L98\n// https://github.com/zertosh/slash-escape/blob/e7ebb99/slash-escape.js\n//------------------------------------------------------------------------------\n\nfunction mkdirpSync(p_) {\n  _mkdirpSync(path.resolve(p_), 0o777);\n}\n\nfunction _mkdirpSync(p, mode) {\n  try {\n    fs.mkdirSync(p, mode);\n  } catch (err0) {\n    if (err0.code === 'ENOENT') {\n      _mkdirpSync(path.dirname(p));\n      _mkdirpSync(p);\n    } else {\n      try {\n        const stat = fs.statSync(p);\n        if (!stat.isDirectory()) { throw err0; }\n      } catch (err1) {\n        throw err0;\n      }\n    }\n  }\n}\n\nfunction slashEscape(str) {\n  const ESCAPE_LOOKUP = {\n    '\\\\': 'zB',\n    ':': 'zC',\n    '/': 'zS',\n    '\\x00': 'z0',\n    'z': 'zZ',\n  };\n  const ESCAPE_REGEX = /[\\\\:/\\x00z]/g; // eslint-disable-line no-control-regex\n  return str.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\nfunction supportsCachedData() {\n  const script = new vm.Script('\"\"', {produceCachedData: true});\n  // chakracore, as of v1.7.1.0, returns `false`.\n  return script.cachedDataProduced === true;\n}\n\nfunction getCacheDir() {\n  const v8_compile_cache_cache_dir = process.env.V8_COMPILE_CACHE_CACHE_DIR;\n  if (v8_compile_cache_cache_dir) {\n    return v8_compile_cache_cache_dir;\n  }\n\n  // Avoid cache ownership issues on POSIX systems.\n  const dirname = typeof process.getuid === 'function'\n    ? 'v8-compile-cache-' + process.getuid()\n    : 'v8-compile-cache';\n  const version = typeof process.versions.v8 === 'string'\n    ? process.versions.v8\n    : typeof process.versions.chakracore === 'string'\n      ? 'chakracore-' + process.versions.chakracore\n      : 'node-' + process.version;\n  const cacheDir = path.join(os.tmpdir(), dirname, version);\n  return cacheDir;\n}\n\nfunction getMainName() {\n  // `require.main.filename` is undefined or null when:\n  //    * node -e 'require(\"v8-compile-cache\")'\n  //    * node -r 'v8-compile-cache'\n  //    * Or, requiring from the REPL.\n  const mainName = require.main && typeof require.main.filename === 'string'\n    ? require.main.filename\n    : process.cwd();\n  return mainName;\n}\n\n//------------------------------------------------------------------------------\n// main\n//------------------------------------------------------------------------------\n\nif (!process.env.DISABLE_V8_COMPILE_CACHE && supportsCachedData()) {\n  const cacheDir = getCacheDir();\n  const prefix = getMainName();\n  const blobStore = new FileSystemBlobStore(cacheDir, prefix);\n\n  const nativeCompileCache = new NativeCompileCache();\n  nativeCompileCache.setCacheStore(blobStore);\n  nativeCompileCache.install();\n\n  process.once('exit', () => {\n    if (blobStore.isDirty()) {\n      blobStore.save();\n    }\n    nativeCompileCache.uninstall();\n  });\n}\n\nmodule.exports.__TEST__ = {\n  FileSystemBlobStore,\n  NativeCompileCache,\n  mkdirpSync,\n  slashEscape,\n  supportsCachedData,\n  getCacheDir,\n  getMainName,\n};\n"]}